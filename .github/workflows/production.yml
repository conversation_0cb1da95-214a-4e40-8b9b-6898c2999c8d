name: Git Pull Only

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  pull:
    runs-on: prodigy

    steps:
    - name: Checkout repository with submodules
      uses: actions/checkout@v3  
      with:
        submodules: 'true' 
    - name: Initialize and update submodules
      run: |
        git submodule update --init --recursive  

    - name: Pull latest changes
      run: |
        cd "/mnt/airflow-dags/airflow-v1"
        sudo git pull 