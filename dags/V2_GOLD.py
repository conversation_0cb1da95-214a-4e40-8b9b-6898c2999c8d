from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

first_instances = [
    Gold("newcon_mapa_cotas")
    .query(
        Query.Gold.newcon_mapa_cotas
    ),
    Gold("controladoria_gerencial_veiculos")
    .query(
        Query.Gold.controladoria_gerencial_veiculos
    ),
    Gold("controladoria_gerencial_pecas")
    .query(
        Query.Gold.controladoria_gerencial_pecas
    ),
    Gold("controladoria_gerencial_servicos")
    .query(
        Query.Gold.controladoria_gerencial_servicos
    ),
    Gold("controladoria_interno_vendedores")
    .query(
        Query.Gold.controladoria_interno_vendedores
    ),
    Gold("controladoria_interno_veiculos")
    .query(
        Query.Gold.controladoria_interno_veiculos
    ),
    Gold("controladoria_interno_pecas")
    .query(
        Query.Gold.controladoria_interno_pecas
    ),
    Gold("controladoria_interno_servicos")
    .query(
        Query.Gold.controladoria_interno_servicos
    ),
    Gold("controladoria_interno_financeiro")
    .query(
        Query.Gold.controladoria_interno_financeiro
    ),
    Gold("newcon_extratofinanceiro")
    .query(
        Query.Gold.gold_newcon_extratofinanceiro
    ),
    Gold("faturamento")
    .query(
        Query.Gold.faturamento
    ),
    Gold("quiver_cgf_producao")
    .query(
        Query.Gold.quiver_cgf_producao
    ),
    Gold("quiver_cgf_renovacoes")
    .query(
        Query.Gold.quiver_cgf_renovacoes
    ),
    Gold("quiver_cgf_seguros_automotivos")
    .query(
        Query.Gold.quiver_cgf_seguros_automotivos
    ),
    Gold("syonet_cgf_vendas").times_per_day(5)
    .query(
        Query.Gold.syonet_cgf_vendas
    ),
    Gold("syo_cgf_vendas_seg").times_per_day(5)
    .query(
        Query.Gold.syo_cgf_vendas_seg
    ),
    Gold("syonet_cgf_renovacoes").times_per_day(5)
    .query(
        Query.Gold.syonet_cgf_renovacoes
    ),
    Gold("autom_vendas").times_per_day(5)
    .query(
        Query.Gold.autom_vendas
    ),
    Gold("autom_pecas_oficina")
    .query(
        Query.Gold.autom_pecas_oficina
    ),
    Gold("autom_pecas_balcao")
    .query(
        Query.Gold.autom_pecas_balcao
    ),
    Gold("autom_servicos")
    .query(
        Query.Gold.autom_servicos
    ),
    Gold("autom_pecas_detalhamento")
    .query(
        Query.Gold.autom_pecas_detalhamento
    ),
    Gold("autom_veiculos")
    .query(
        Query.Gold.autom_veiculos
    ),
    Gold("financeiro_sistemas").times_per_day(1)
    .query(
        Query.Gold.financeiro_sistemas
    ).index({
        "idx_gold_financeiro_sistemas_doc_cli": ["doccli"]
    }),
    Gold("faturamento_sistemas").times_per_day(1)
    .query(
        Query.Gold.faturamento_sistemas
    ).index({
        "idx_gold_faturamento_sistemas_doc_cli": ["doccli"],
        "idx_gold_faturamento_sistemas_codempresa": ["codempresa"],
        "idx_gold_faturamento_sistemaorigem": ["sistemaorigem"]
    }),
    Gold("formapagto_faturamento_sistemas").times_per_day(1)
    .query(
        Query.Gold.formapagto_faturamento_sistemas
    ).index({
        "idx_gold_formapagto_faturamento_sistemas_formapagto" : ["forma_pagto"],
        "idx_gold_formapagto_faturamento_sistemas_tiporecurso" : ["tipo_recurso"],
        "idx_gold_formapagto_faturamento_sistemas_doccli" : ["doccli"],
        "idx_gold_formapagto_faturamento_sistemas_chave_sistemaorigem" : ["chave_faturamento","sysorigem"]
    }),
    Gold("clientes_sistemas").times_per_day(1)
    .query(
        Query.Gold.clientes_sistemas
    ).index({
        "idx_gold_clientes_sistemas_doc_cli" : ["doccli"]
    }),
    Gold("clientes_sistemas_cadunico").times_per_day(1)
    .query(
        Query.Gold.clientes_sistemas_cadunico
    ),
    Gold("calendario_projetado")
    .query(
        Query.Gold.calendario_projetado
    ),
    Gold("consorcio_consolidado")
    .query(
        Query.Gold.consorcio_consolidado
    ),
    Gold("dealer_cohort_maquinas")
    .query(
        Query.Gold.dealer_cohort_maquinas
    ),
    Gold("dealer_cohort_servicos")
    .query(
        Query.Gold.dealer_cohort_servicos
    ),
    Gold("dealer_valor_agregado").times_per_day(1)
    .query(
        Query.Gold.dealer_valor_agregado
    ),
    Gold("chamados_formalizacao_credito_bamaq_assinatura")
    .query(
        Query.Gold.sync_chamados_formalizacao_credito_bamaq_assinatura
    ),
    Gold("chamados_encerrados_facilities")
    .query(
        Query.Gold.sync_chamados_encerrados_facilities
    ),
    Gold("calendario")
    .query(
        Query.Gold.calendario
    ),
    Gold("crm_auto_historico_etapas_horas_trabalhadas")
    .query(
        Query.Gold.crm_auto_historico_etapas_horas_trabalhadas
    ),
    Gold("crm_auto_historico_acao_horas_trabalhadas")
    .query(
        Query.Gold.crm_auto_historico_acao_horas_trabalhadas
    ),
    Gold("autom_veiculos_detalhamento")
    .query(
        Query.Gold.autom_veiculos_detalhamento
    ),
    Gold("lancamentos_contabeis_imobilizado")
    .query(
        Query.Gold.lancamentos_contabeis_imobilizado
    ),
    Gold("autom_vendas_premium")
    .query(
        Query.Gold.autom_vendas_premium
    ),
    Gold("estoque_premium")
    .query(
        Query.Gold.estoque_premium
    ),   
    Gold("capex_operacional")
    .query(
        Query.Gold.capex_operacional
    ),
    Gold("contas_imobilizado")
    .query(
        Query.Gold.contas_imobilizado
    ),
    Gold("faturamento_seminovos")
    .query(
        Query.Gold.faturamento_seminovos
    ),
    Gold("custos_especificos_premium")
    .query(
        Query.Gold.custos_especificos_premium
    ),
    Gold("RFV_posvendas")
    .query(
        Query.Gold.RFV_posvendas
    ),
    Gold("RFV_Vendas")
    .query(
        Query.Gold.RFV_vendas
    ),
    Gold("consorcio_producao")
    .query(
        Query.Gold.consorcio_producao
    ),
    Gold("usuarios_licenciados_office365").times_per_day(5)
    .query(
        Query.Gold.usuarios_licenciados_office365
    ),
    Gold("acessos_relatorios_powerbi").times_per_day(5)
    .query(
        Query.Gold.acessos_relatorios_powerbi
    ),
    Gold("licencas_atribuidas_office365").times_per_day(5)
    .query(
        Query.Gold.licencas_atribuidas_office365
    )
]

second_instances = [
    Gold("bandeirasgrupobamaq").sys("sge")
    .query(
        Query.Gold.bandeirasgrupobamaq
    ),
    Gold("comissoes_pagas_consorcio").times_per_day(5)
    .query(
        Query.Gold.comissoes_pagas_consorcio
    )
]

third_instances = [
    Gold("comissoes_estornadas_consorcio").times_per_day(5)
    .query(
        Query.Gold.comissoes_estornadas_consorcio
    )
]

with DAG(
    'V2-GOLD',
    default_args=default_args,
    description='DAG para ETL gold de dados da BAMAQ',
    schedule_interval=None,
    max_active_runs=1,
    max_active_tasks=12, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, [first_instances, second_instances, third_instances], triggers=['V2-VIEW'])

    start_etl_task >> end_etl_task >> dag_stats