from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from datetime import datetime, timedelta
import time

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

def dummy_task():
    # Sleep for 30 seconds to ensure pod stays alive
    time.sleep(5)

with DAG(
    'WARMUP_NODES',
    default_args=default_args,
    description='DAG to warm up nodes with dummy tasks',
    schedule_interval='5 6,13,15,17,19 * * *',
    max_active_runs=1,
    max_active_tasks=20,  # Allow all tasks to run in parallel
    start_date=days_ago(1),
    catchup=False
) as dag:

    # Create 20 dummy tasks that will all start immediately
    tasks = []
    for i in range(20):
        task = PythonOperator(
            task_id=f'dummy_task_{i}',
            python_callable=dummy_task
        )
        tasks.append(task) 