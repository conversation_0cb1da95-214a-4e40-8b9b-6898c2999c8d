# 🔧 Guia de Troubleshooting - ETL Salesforce Marketing Cloud

## 🚨 Problemas Mais Comuns

### 1. 🔌 Falhas de Conexão

#### ❌ Erro: "Connection timeout to SQL Server"

**Sintomas:**
- Pipeline falha na fase de extração
- Logs mostram timeout de conexão
- Erro específico: `pymssql.OperationalError: (20003, b'DB-Lib error')`

**Diagnóstico:**
```bash
# Testar conectividade de rede
ping ***********  # NewCon Batman
ping ***********  # NewCon Jocker

# Testar porta específica
telnet *********** 1433
nc -zv *********** 1433

# Verificar DNS
nslookup ***********
```

**Soluções:**
1. **Verificar Firewall**
   ```bash
   sudo ufw status
   sudo iptables -L | grep 1433
   ```

2. **Testar Conexão Manual**
   ```bash
   python -c "
   import pymssql
   conn = pymssql.connect(
       server='***********',
       user='$NEWCON_USER',
       password='$NEWCON_PASSWORD',
       database='dbNewCon',
       timeout=30
   )
   print('✅ Conexão SQL Server OK')
   "
   ```

3. **Usar Servidor Secundário**
   - Sistema automaticamente tenta failover para Jocker (***********)
   - Verificar logs para confirmar tentativa de failover

#### ❌ Erro: "Salesforce Authentication Failed"

**Sintomas:**
- Falha na fase de carregamento
- Erro 401 Unauthorized
- Token de acesso inválido

**Diagnóstico:**
```bash
# Verificar credenciais
echo "Client ID: $SALESFORCE_CLIENT_ID"
echo "Auth URI: $SALESFORCE_AUTH_URI"

# Testar autenticação manual
curl -X POST "$SALESFORCE_AUTH_URI/v2/token" \
  -H "Content-Type: application/json" \
  -d "{
    \"grant_type\": \"client_credentials\",
    \"client_id\": \"$SALESFORCE_CLIENT_ID\",
    \"client_secret\": \"$SALESFORCE_CLIENT_SECRET\"
  }"
```

**Soluções:**
1. **Verificar Credenciais**
   ```bash
   # Validar configuração
   python -c "
   from salesforce_client import validate_salesforce_config
   is_valid, missing = validate_salesforce_config()
   print(f'Válido: {is_valid}')
   if not is_valid:
       print(f'Campos faltando: {missing}')
   "
   ```

2. **Renovar Client Secret**
   - Acessar Salesforce Marketing Cloud
   - Gerar novo Client Secret
   - Atualizar variável `SALESFORCE_CLIENT_SECRET`

### 2. 📊 Problemas de Dados

#### ❌ Erro: "Required field missing"

**Sintomas:**
- Falha na validação de dados
- Logs mostram campos obrigatórios ausentes
- Erro: `ValidationError: Campo obrigatório 'email' não encontrado`

**Diagnóstico:**
```bash
# Verificar estrutura dos dados
python -c "
import pandas as pd
from data_extractors import DataExtractor

extractor = DataExtractor(test_sample=10)
data = extractor.extract_all_data()

for table, df in data.items():
    print(f'{table}: {list(df.columns)}')
    print(f'Registros: {len(df)}')
    print('---')
"
```

**Soluções:**
1. **Verificar Campos Obrigatórios**
   ```python
   # Campos obrigatórios por tabela
   REQUIRED_FIELDS = {
       'tb_clientes': ['cnpjcpf', 'email'],
       'tb_leads': ['cnpjcpf', 'dt_simulacao'], 
       'tb_produtos': ['id_produto'],
       'tb_propostas': ['idproposta', 'email']
   }
   ```

2. **Executar em Modo CSV para Análise**
   ```bash
   export SALESFORCE_OUTPUT_MODE=csv
   python etl_main.py --test-sample 100
   
   # Analisar CSVs gerados
   head -5 carga_fria/tb_*.csv
   ```

#### ❌ Erro: "Data type mismatch"

**Sintomas:**
- Falha na transformação de dados
- Erro de conversão de tipos
- `ValueError: cannot convert float NaN to integer`

**Diagnóstico:**
```bash
# Verificar tipos de dados
python -c "
import pandas as pd
df = pd.read_csv('carga_fria/tb_produtos_latest.csv')
print(df.dtypes)
print(df.isnull().sum())
"
```

**Soluções:**
1. **Limpeza de Dados**
   - Sistema já implementa limpeza automática
   - Verificar logs de transformação para detalhes

2. **Ajustar Configurações de Tipo**
   ```python
   # Em data_transformers.py
   # Conversões são tratadas automaticamente
   # Verificar função clean_and_validate_data()
   ```

### 3. 🚀 Problemas de Performance

#### ❌ Problema: "Pipeline muito lento"

**Sintomas:**
- Execução demora mais de 2 horas
- Timeouts frequentes
- Alto uso de memória

**Diagnóstico:**
```bash
# Verificar uso de recursos
top -p $(pgrep -f "python.*etl")
free -h
df -h

# Verificar conexões de rede
netstat -an | grep ESTABLISHED | wc -l

# Analisar logs de performance
grep "Tempo de execução" logs/etl_consolidated.log | tail -10
```

**Soluções:**
1. **Otimizar Configurações**
   ```bash
   # Reduzir tamanho dos lotes
   export BATCH_SIZE_DATABASE=5000
   export BATCH_SIZE_SALESFORCE=1000
   
   # Aumentar timeout
   export TIMEOUT_DATABASE_QUERY=600
   ```

2. **Executar por Partes**
   ```bash
   # Executar tabelas individualmente
   python etl_main.py --tables produtos
   python etl_main.py --tables clientes
   python etl_main.py --tables leads
   python etl_main.py --tables propostas
   ```

3. **Modo Rápido (sem verificação)**
   ```bash
   export SALESFORCE_VERIFY_STATUS=false
   ```

### 4. 🔄 Problemas do Airflow

#### ❌ Erro: "DAG not found"

**Sintomas:**
- DAG não aparece na interface
- Erro de importação
- Logs mostram erros de sintaxe

**Diagnóstico:**
```bash
# Verificar sintaxe da DAG
python dags/salesforce_integration/dag_etl_salesforce_table_parallel.py

# Verificar logs do scheduler
tail -f $AIRFLOW_HOME/logs/scheduler/latest/*.log

# Listar DAGs
airflow dags list | grep salesforce
```

**Soluções:**
1. **Verificar Importações**
   ```bash
   cd dags/salesforce_integration
   python -c "
   try:
       from etl_main import *
       print('✅ Importações OK')
   except Exception as e:
       print(f'❌ Erro de importação: {e}')
   "
   ```

2. **Reiniciar Scheduler**
   ```bash
   sudo systemctl restart airflow-scheduler
   ```

#### ❌ Erro: "Task stuck in running state"

**Sintomas:**
- Tarefa fica "running" indefinidamente
- Não há progresso nos logs
- Interface mostra tarefa ativa mas sem atividade

**Soluções:**
1. **Matar Tarefa**
   ```bash
   # Via interface Airflow: marcar como "failed"
   # Via CLI:
   airflow tasks clear etl_salesforce_marketing_cloud_table_parallel extract_newcon_clients $(date +%Y-%m-%d)
   ```

2. **Verificar Processos**
   ```bash
   # Encontrar processos órfãos
   ps aux | grep python | grep etl
   
   # Matar se necessário
   pkill -f "python.*etl"
   ```

### 5. 💾 Problemas de Espaço em Disco

#### ❌ Erro: "No space left on device"

**Diagnóstico:**
```bash
# Verificar espaço em disco
df -h
du -sh /opt/airflow/logs/*
du -sh dags/salesforce_integration/carga_fria/*
```

**Soluções:**
1. **Limpeza Automática**
   ```bash
   # Limpar logs antigos
   find $AIRFLOW_HOME/logs -name "*.log" -mtime +7 -delete
   
   # Limpar CSVs antigos
   find dags/salesforce_integration/carga_fria -name "*.csv" -mtime +3 -delete
   
   # Limpar cache Python
   find . -name "__pycache__" -type d -exec rm -rf {} +
   ```

2. **Configurar Rotação de Logs**
   ```bash
   # Editar airflow.cfg
   [logging]
   base_log_folder = /opt/airflow/logs
   logging_level = INFO
   fab_logging_level = WARN
   log_filename_template = {{ ti.dag_id }}/{{ ti.task_id }}/{{ ts }}/{{ try_number }}.log
   log_processor_filename_template = {{ filename }}.log
   dag_processor_manager_log_location = /opt/airflow/logs/dag_processor_manager/dag_processor_manager.log
   ```

## 🔍 Ferramentas de Diagnóstico

### 📊 Scripts de Verificação

#### Verificação Completa do Sistema
```bash
#!/bin/bash
# health_check.sh

echo "=== VERIFICAÇÃO DE SAÚDE DO SISTEMA ETL ==="

# 1. Verificar serviços
echo "1. Status dos Serviços:"
systemctl is-active airflow-webserver airflow-scheduler

# 2. Verificar conexões
echo "2. Conexões de Banco:"
python -c "from database_connections import test_all_connections; test_all_connections()"

# 3. Verificar Salesforce
echo "3. Conexão Salesforce:"
python -c "from salesforce_client import test_salesforce_connection; test_salesforce_connection()"

# 4. Verificar espaço em disco
echo "4. Espaço em Disco:"
df -h | grep -E "(/$|/opt)"

# 5. Verificar memória
echo "5. Uso de Memória:"
free -h

# 6. Verificar processos
echo "6. Processos ETL:"
ps aux | grep -E "(airflow|python.*etl)" | grep -v grep

echo "=== VERIFICAÇÃO CONCLUÍDA ==="
```

#### Teste Rápido de Pipeline
```bash
#!/bin/bash
# quick_test.sh

echo "=== TESTE RÁPIDO DO PIPELINE ==="

# Configurar modo teste
export SALESFORCE_OUTPUT_MODE=csv
export SALESFORCE_VERIFY_STATUS=false

# Executar com amostra pequena
cd dags/salesforce_integration
python etl_main.py --test-sample 10 --dry-run

# Verificar resultados
if [ $? -eq 0 ]; then
    echo "✅ Teste rápido passou!"
    ls -la carga_fria/
else
    echo "❌ Teste rápido falhou!"
    tail -20 logs/etl_consolidated.log
fi
```

### 📈 Monitoramento Contínuo

#### Script de Monitoramento
```bash
#!/bin/bash
# monitor.sh

while true; do
    echo "$(date): Verificando pipeline..."
    
    # Verificar se há execução ativa
    RUNNING=$(airflow dags state etl_salesforce_marketing_cloud_table_parallel $(date +%Y-%m-%d) 2>/dev/null)
    
    if [ "$RUNNING" = "running" ]; then
        echo "Pipeline executando..."
        
        # Verificar uso de recursos
        echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
        echo "MEM: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
        
        # Verificar logs recentes
        echo "Últimas linhas do log:"
        tail -3 dags/salesforce_integration/logs/etl_consolidated.log
    else
        echo "Pipeline não está executando"
    fi
    
    echo "---"
    sleep 60
done
```

## 📞 Quando Escalar o Problema

### 🟢 Nível 1 - Resolver Localmente
- Falhas de conexão temporárias
- Problemas de espaço em disco
- Reinicialização de serviços
- Execução manual de pipeline

### 🟡 Nível 2 - Suporte Técnico
- Falhas persistentes de conexão
- Problemas de performance
- Erros de configuração complexos
- Falhas de validação de dados

### 🔴 Nível 3 - Desenvolvimento
- Bugs no código
- Necessidade de novos recursos
- Mudanças na estrutura de dados
- Otimizações de arquitetura

## 📋 Checklist de Troubleshooting

### Antes de Escalar
- [ ] Verificar logs detalhados
- [ ] Testar conexões básicas
- [ ] Verificar espaço em disco
- [ ] Tentar execução em modo teste
- [ ] Verificar configurações de ambiente
- [ ] Consultar documentação
- [ ] Verificar status dos serviços externos

### Informações para Suporte
- [ ] Logs completos do erro
- [ ] Timestamp exato da falha
- [ ] Configurações de ambiente
- [ ] Versão do sistema
- [ ] Passos para reproduzir
- [ ] Impacto no negócio
- [ ] Tentativas de resolução já feitas

---

**Próximos Passos**: Consulte a [Documentação de Segurança](SECURITY.md) para boas práticas de segurança.
