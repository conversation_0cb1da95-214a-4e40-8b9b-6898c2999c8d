# 🧩 Documentação Técnica dos Módulos - ETL Salesforce Marketing Cloud

## 📋 Visão Geral dos Módulos

O sistema ETL é composto por 9 módulos Python principais, cada um com responsabilidades específicas e bem definidas. Esta documentação detalha a API e funcionalidades de cada módulo.

## 📁 Estrutura dos Módulos

```
salesforce_integration/
├── 📄 config.py                    # Configurações centralizadas
├── 📄 etl_main.py                  # Orquestrador principal
├── 📄 data_extractors.py           # Extração de dados
├── 📄 data_transformers.py         # Transformação de dados
├── 📄 salesforce_client.py         # Cliente Salesforce
├── 📄 database_connections.py      # Gerenciador de conexões
├── 📄 utils.py                     # Utilitários compartilhados
├── 📄 test_data_manager.py         # Gerenciador de dados de teste
└── 📄 dag_etl_salesforce_table_parallel.py  # DAG do Airflow
```

## ⚙️ config.py - Configurações Centralizadas

### 📋 Responsabilidades
- Centralizar todas as configurações do sistema
- Gerenciar variáveis de ambiente com valores padrão
- Validar configurações obrigatórias
- Fornecer funções auxiliares de configuração

### 🔧 Principais Configurações

#### Bancos de Dados
```python
DATABASE_CONFIGS = {
    'newcon': {
        'type': 'mssql',
        'primary': {...},      # Servidor Batman
        'secondary': {...},    # Servidor Jocker (failover)
        'pool_size': 5,
        'retry_attempts': 3
    },
    'dw_corporativo': {...},   # PostgreSQL
    'orbbits': {...}          # MySQL
}
```

#### Salesforce Marketing Cloud
```python
SALESFORCE_CONFIG = {
    'client_id': 'qfdxgdxstw6wlnn0pnl8usyr',
    'auth_uri': 'https://mc5jxdhk62pllmhs0nc0r--9qgb4.auth.marketingcloudapis.com/',
    'batch_size': 2000,
    'rate_limit': 200,
    'timeout': 300
}
```

#### Data Extensions
```python
DATA_EXTENSIONS = {
    'tb_produtos': {
        'external_key': 'FCC1DCA7-D286-458D-BDCC-D050C1BA61A8',
        'required_fields': ['id_produto'],
        'estimated_records': 20502
    },
    # ... outras tabelas
}
```

### 🔍 Funções Principais

#### `get_database_config(database_name: str) -> Dict[str, Any]`
Retorna configuração específica de um banco de dados.

#### `validate_required_env_vars() -> Tuple[bool, List[str]]`
Valida se todas as variáveis de ambiente obrigatórias estão definidas.

#### `validate_configuration() -> None`
Valida toda a configuração do sistema, levantando exceção se inválida.

## 🎮 etl_main.py - Orquestrador Principal

### 📋 Responsabilidades
- Orquestrar execução completa do pipeline ETL
- Gerenciar modos de operação (dry-run, teste, produção)
- Implementar execução seletiva por tabelas
- Fornecer interface de linha de comando
- Preparar funções para integração com Airflow

### 🏗️ Classe Principal

#### `ETLPipeline`
```python
class ETLPipeline:
    def __init__(self, dry_run: bool = False, test_sample: int = None):
        self.dry_run = dry_run
        self.test_sample = test_sample
        self.extractor = DataExtractor(test_sample=test_sample)
        self.sf_client = None
        
    def run_complete_pipeline(self) -> bool:
        """Executa pipeline completo"""
        
    def run_selective_pipeline(self, tables: List[str]) -> bool:
        """Executa pipeline para tabelas específicas"""
        
    def validate_prerequisites(self) -> bool:
        """Valida pré-requisitos antes da execução"""
```

### 🔧 Funções para Airflow

#### Extração Individual por Tabela
```python
def airflow_extract_newcon_clients_individual_task(**context):
    """Extrai apenas clientes do NewCon"""
    
def airflow_extract_newcon_products_individual_task(**context):
    """Extrai apenas produtos do NewCon"""
    
# ... 7 outras funções de extração individual
```

#### Transformação por Tabela Final
```python
def airflow_transform_produtos_task(**context):
    """Transforma dados para tb_produtos"""
    
def airflow_transform_clientes_task(**context):
    """Transforma dados para tb_clientes"""
    
# ... outras funções de transformação
```

#### Carregamento/Exportação
```python
def airflow_load_produtos_parallel_task(**context):
    """Carrega produtos no Salesforce"""
    
def airflow_export_produtos_csv_task(**context):
    """Exporta produtos para CSV"""
    
# ... outras funções de carregamento/exportação
```

### 📝 Interface de Linha de Comando
```bash
python etl_main.py [opções]

Opções:
  --dry-run              Executa sem modificar dados
  --test-sample N        Limita a N registros para teste
  --tables LISTA         Executa apenas tabelas especificadas
  --exclude LISTA        Exclui tabelas especificadas
  --help                 Mostra ajuda
```

## 📊 data_extractors.py - Extração de Dados

### 📋 Responsabilidades
- Extrair dados de todas as fontes (NewCon, RD Station, Orbbits)
- Implementar rate limiting e retry automático
- Gerenciar conexões de banco com failover
- Fornecer extração paralela e individual por tabela

### 🏗️ Classe Principal

#### `DataExtractor`
```python
class DataExtractor:
    def __init__(self, test_sample: int = None):
        self.test_sample = test_sample
        self.extraction_stats = {...}
        
    def extract_all_data(self) -> Dict[str, pd.DataFrame]:
        """Extrai dados de todas as fontes"""
        
    def extract_newcon_data(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do NewCon"""
        
    def extract_rdstation_data(self) -> pd.DataFrame:
        """Extrai dados do RD Station com paginação"""
        
    def extract_orbbits_data(self) -> Dict[str, pd.DataFrame]:
        """Extrai dados complementares do Orbbits"""
```

### 🔧 Métodos de Extração Individual

#### NewCon (SQL Server)
```python
def extract_newcon_clients(self) -> pd.DataFrame:
    """Extrai clientes do NewCon (~73k registros)"""
    
def extract_newcon_products(self) -> pd.DataFrame:
    """Extrai produtos do NewCon (~20k registros)"""
    
def extract_newcon_leads(self) -> pd.DataFrame:
    """Extrai leads do NewCon (volume variável)"""
    
def extract_newcon_proposals(self) -> pd.DataFrame:
    """Extrai propostas do NewCon (~533k registros)"""
```

#### RD Station (API REST)
```python
def extract_rdstation_leads(self) -> pd.DataFrame:
    """
    Extrai leads do RD Station com:
    - Rate limiting (180 req/min, 3 req/sec)
    - Paginação automática
    - Contorno do limite de 10k registros
    """
```

#### Orbbits (MySQL)
```python
def extract_orbbits_origin(self) -> pd.DataFrame:
    """Extrai dados de origem do Orbbits"""
    
def extract_orbbits_payments(self) -> pd.DataFrame:
    """Extrai dados de pagamentos do Orbbits"""
    
def extract_orbbits_sales(self) -> pd.DataFrame:
    """Extrai dados de vendas do Orbbits"""
    
def extract_orbbits_prices(self) -> pd.DataFrame:
    """Extrai dados de preços do Orbbits"""
```

### 🔄 Recursos Avançados

#### Rate Limiting
```python
@rate_limit(calls=3, period=1)  # 3 calls per second
def _make_rdstation_request(self, url: str) -> Dict:
    """Faz requisição com rate limiting"""
```

#### Retry com Backoff Exponencial
```python
@retry_decorator(max_attempts=3, backoff_factor=2)
def _execute_database_query(self, query: str) -> pd.DataFrame:
    """Executa query com retry automático"""
```

## 🔄 data_transformers.py - Transformação de Dados

### 📋 Responsabilidades
- Transformar dados consolidados em tabelas finais
- Aplicar regras de negócio e validações
- Implementar limpeza e normalização de dados
- Garantir qualidade e integridade dos dados

### 🔧 Funções Principais de Transformação

#### Transformação Completa
```python
def transform_all_data(consolidated_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma todos os dados consolidados em tabelas finais
    
    Args:
        consolidated_data: Dados de todas as 9 extrações
        
    Returns:
        Dict com 4 tabelas finais: produtos, clientes, leads, propostas
    """
```

#### Transformações Individuais
```python
def transform_produtos_only(consolidated_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Transforma dados para tb_produtos
    Fontes: newcon_products + orbbits_prices
    """
    
def transform_clientes_only(consolidated_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Transforma dados para tb_clientes
    Fontes: newcon_clients + orbbits_origin
    """
    
def transform_leads_only(consolidated_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Transforma dados para tb_leads
    Fontes: newcon_leads + rdstation_leads
    """
    
def transform_propostas_only(consolidated_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    Transforma dados para tb_propostas
    Fontes: newcon_proposals + orbbits_payments + orbbits_sales
    """
```

### 🧹 Funções de Limpeza e Validação

#### Limpeza de Dados
```python
def clean_and_validate_data(data: pd.DataFrame, table_name: str) -> pd.DataFrame:
    """
    Aplica limpeza padrão:
    - Remove valores nulos em campos obrigatórios
    - Normaliza tipos de dados
    - Remove caracteres especiais
    - Valida formatos (email, CPF, etc.)
    """
    
def normalize_cpf_cnpj(value: str) -> str:
    """Normaliza CPF/CNPJ removendo formatação"""
    
def validate_email_format(email: str) -> bool:
    """Valida formato de email"""
```

#### Validação de Qualidade
```python
def validate_required_fields(data: pd.DataFrame, table_name: str) -> bool:
    """Valida se campos obrigatórios estão presentes"""
    
def validate_data_types(data: pd.DataFrame, table_name: str) -> bool:
    """Valida tipos de dados esperados"""
    
def validate_business_rules(data: pd.DataFrame, table_name: str) -> bool:
    """Valida regras de negócio específicas"""
```

### 🔗 Funções de Enriquecimento

#### Merge de Dados
```python
def merge_price_data(produtos: pd.DataFrame, precos: pd.DataFrame) -> pd.DataFrame:
    """Enriquece produtos com dados de preços do Orbbits"""
    
def merge_origin_data(clientes: pd.DataFrame, origem: pd.DataFrame) -> pd.DataFrame:
    """Enriquece clientes com dados de origem"""
    
def merge_financial_data(propostas: pd.DataFrame, pagamentos: pd.DataFrame, vendas: pd.DataFrame) -> pd.DataFrame:
    """Enriquece propostas com dados financeiros"""
```

## 🌐 salesforce_client.py - Cliente Salesforce

### 📋 Responsabilidades
- Gerenciar autenticação com Salesforce Marketing Cloud
- Implementar upload de dados em lotes
- Monitorar status de processamento
- Tratar erros e implementar retry

### 🏗️ Classe Principal

#### `SalesforceMarketingCloudClient`
```python
class SalesforceMarketingCloudClient:
    def __init__(self):
        self.auth_token = None
        self.session = requests.Session()
        
    def authenticate(self) -> bool:
        """Autentica com Salesforce usando client credentials"""
        
    def upsert_data_extension(self, external_key: str, data: List[Dict]) -> Dict:
        """
        Faz upsert de dados em Data Extension
        
        Args:
            external_key: Chave externa da Data Extension
            data: Lista de registros para inserir/atualizar
            
        Returns:
            Resultado da operação com request_id
        """
        
    def check_request_status(self, request_id: str) -> Dict:
        """Verifica status de uma requisição assíncrona"""
        
    def process_data_in_batches(self, external_key: str, data: pd.DataFrame) -> List[Dict]:
        """Processa dados em lotes de 2000 registros"""
```

### 🔧 Funções Auxiliares

#### Autenticação e Sessão
```python
def create_salesforce_client() -> SalesforceMarketingCloudClient:
    """Cria e autentica cliente Salesforce"""
    
def validate_salesforce_config() -> Tuple[bool, List[str]]:
    """Valida configuração do Salesforce"""
    
def test_salesforce_connection() -> bool:
    """Testa conexão com Salesforce"""
```

#### Processamento de Dados
```python
def convert_dataframe_to_records(data: Union[pd.DataFrame, List[Dict]]) -> List[Dict]:
    """
    Converte DataFrame para formato Salesforce
    - Trata valores float inválidos (inf, -inf, NaN)
    - Normaliza dados para JSON
    """
    
def create_batches(data: List[Dict], batch_size: int = 2000) -> List[List[Dict]]:
    """Divide dados em lotes para processamento"""
```

#### Pipeline ETL Completo
```python
def process_etl_pipeline(tables_data: Dict[str, pd.DataFrame], verify_status: bool = True) -> Dict[str, Any]:
    """
    Processa pipeline completo de carregamento
    
    Args:
        tables_data: Dados transformados por tabela
        verify_status: Se deve verificar status dos lotes
        
    Returns:
        Resultado consolidado de todas as operações
    """
```

## 🔌 database_connections.py - Gerenciador de Conexões

### 📋 Responsabilidades
- Gerenciar pools de conexão para todos os bancos
- Implementar failover automático (NewCon)
- Fornecer interface unificada para diferentes SGBDs
- Implementar retry e tratamento de erros

### 🏗️ Classe Principal

#### `DatabaseConnectionManager`
```python
class DatabaseConnectionManager:
    def __init__(self):
        self._pools = {}
        self._locks = {}
        
    def get_connection(self, database_name: str) -> Any:
        """Obtém conexão do pool"""
        
    def execute_query(self, database_name: str, query: str) -> pd.DataFrame:
        """Executa query com retry automático"""
        
    def test_connection(self, database_name: str) -> bool:
        """Testa conectividade com banco"""
        
    def close_all_connections(self):
        """Fecha todas as conexões ativas"""
```

### 🔧 Funções de Conexão Específicas

#### SQL Server (NewCon)
```python
def get_newcon_connection(use_secondary: bool = False) -> pymssql.Connection:
    """
    Conecta ao NewCon com failover automático
    - Primário: Batman (***********)
    - Secundário: Jocker (***********)
    """
    
def test_newcon_connection() -> bool:
    """Testa conexão NewCon com ambos os servidores"""
```

#### PostgreSQL (DW Corporativo)
```python
def get_dw_connection() -> psycopg2.Connection:
    """Conecta ao DW Corporativo"""
    
def test_dw_connection() -> bool:
    """Testa conexão DW"""
```

#### MySQL (Orbbits)
```python
def get_orbbits_connection() -> mysql.connector.Connection:
    """Conecta ao Orbbits"""
    
def test_orbbits_connection() -> bool:
    """Testa conexão Orbbits"""
```

### 🔄 Funções de Alto Nível

#### Teste Geral
```python
def test_all_connections() -> bool:
    """
    Testa conectividade com todos os bancos
    Retorna True apenas se todos estiverem acessíveis
    """
    
def get_database_connection(database_name: str) -> Any:
    """Interface unificada para obter conexões"""
```

## 🛠️ utils.py - Utilitários Compartilhados

### 📋 Responsabilidades
- Fornecer funções auxiliares reutilizáveis
- Implementar sistema de logging estruturado
- Implementar decorators para retry e rate limiting
- Fornecer classes para monitoramento e validação

### 🔧 Sistema de Logging

#### Configuração de Logs
```python
def setup_logging(log_level: str = None, log_file: str = None) -> logging.Logger:
    """
    Configura sistema de logging estruturado
    - Suporte a arquivo e console
    - Rotação automática de logs
    - Formato JSON opcional
    """
```

### 🔄 Decorators Utilitários

#### Retry Automático
```python
def retry_decorator(max_attempts: int = 3, backoff_factor: float = 2, jitter: bool = True):
    """
    Decorator para retry automático com backoff exponencial
    
    Args:
        max_attempts: Máximo de tentativas
        backoff_factor: Fator de multiplicação do delay
        jitter: Adicionar aleatoriedade ao delay
    """
    
@retry_decorator(max_attempts=3, backoff_factor=2)
def funcao_com_retry():
    # Função que pode falhar
    pass
```

#### Rate Limiting
```python
def rate_limit(calls: int, period: int):
    """
    Decorator para rate limiting
    
    Args:
        calls: Número de chamadas permitidas
        period: Período em segundos
    """
    
@rate_limit(calls=3, period=1)  # 3 calls per second
def funcao_com_rate_limit():
    # Função com limite de taxa
    pass
```

### 📊 Classes de Monitoramento

#### Progress Tracker
```python
class ProgressTracker:
    """Rastreia progresso de operações longas"""
    
    def __init__(self, total_items: int, description: str = "Processing"):
        self.total_items = total_items
        self.description = description
        
    def update(self, items_processed: int):
        """Atualiza progresso"""
        
    def finish(self):
        """Finaliza rastreamento"""
```

#### Data Quality Checker
```python
class DataQualityChecker:
    """Verifica qualidade de dados"""
    
    def check_completeness(self, data: pd.DataFrame) -> Dict[str, float]:
        """Verifica completude dos dados"""
        
    def check_uniqueness(self, data: pd.DataFrame, columns: List[str]) -> bool:
        """Verifica unicidade de colunas"""
        
    def check_data_types(self, data: pd.DataFrame, expected_types: Dict[str, str]) -> bool:
        """Verifica tipos de dados"""
```

### 🔒 Funções de Segurança

#### Mascaramento de Dados
```python
def mask_sensitive_data(data: Any, field_name: str = None) -> str:
    """
    Mascara dados sensíveis para logs
    - CPF/CNPJ: 123.456.***-**
    - Email: user***@domain.com
    - Telefone: (11) 9****-****
    """
    
def is_sensitive_field(field_name: str) -> bool:
    """Verifica se campo contém dados sensíveis"""
```

## 🧪 test_data_manager.py - Gerenciador de Dados de Teste

### 📋 Responsabilidades
- Marcar dados de teste explicitamente
- Prevenir confusão com dados de produção
- Validar ambientes antes de execução
- Gerar relatórios de segurança

### 🏗️ Classes e Enums

#### Enums de Ambiente
```python
class DataEnvironment(Enum):
    PRODUCTION = "production"
    STAGING = "staging"
    TESTING = "testing"
    DEVELOPMENT = "development"
    
class DataType(Enum):
    REAL = "real"
    TEST = "test"
    MOCK = "mock"
    SAMPLE = "sample"
```

#### Marcador de Dados de Teste
```python
@dataclass
class TestDataMarker:
    data_id: str
    data_type: DataType
    environment: DataEnvironment
    created_at: datetime
    description: str
    source_module: str
    record_count: int
```

### 🔧 Funções Principais

#### Marcação de Dados
```python
def mark_as_test_data(data: Union[pd.DataFrame, List[Dict]], table_name: str, description: str = None) -> Union[pd.DataFrame, List[Dict]]:
    """
    Marca dados como teste de forma explícita
    Adiciona prefixos e metadados identificadores
    """
    
def validate_test_safety(data: Union[pd.DataFrame, List[Dict]], table_name: str) -> bool:
    """
    Valida se dados são seguros para teste
    Verifica se não contêm dados de produção
    """
```

#### Validação de Ambiente
```python
def validate_environment_safety() -> Tuple[bool, List[str]]:
    """
    Valida se ambiente é seguro para execução
    Verifica configurações e credenciais
    """
    
def generate_safety_report() -> Dict[str, Any]:
    """
    Gera relatório de segurança dos dados
    Lista todos os dados marcados como teste
    """
```

---

**Próximos Passos**: Consulte a [Documentação de Segurança](SECURITY.md) para boas práticas de segurança e proteção de dados.
