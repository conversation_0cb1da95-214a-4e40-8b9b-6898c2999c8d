# 🔄 Documentação do Pipeline ETL - Salesforce Marketing Cloud

## 📋 Visão Geral do Pipeline

O pipeline ETL implementa uma arquitetura ultra-paralela com 3 fases principais: **Extração**, **Transformação** e **Carregamento**. Cada fase é otimizada para máxima performance e confiabilidade.

## 🏗️ Arquitetura do Pipeline

```mermaid
graph TD
    A[Início do Pipeline] --> B[Extração Paralela]
    B --> B1[NewCon Clients]
    B --> B2[NewCon Products]
    B --> B3[NewCon Leads]
    B --> B4[NewCon Proposals]
    B --> B5[RD Station Leads]
    B --> B6[Orbbits Origin]
    B --> B7[Orbbits Payments]
    B --> B8[Orbbits Sales]
    B --> B9[Orbbits Prices]
    
    B1 --> C[Consolidação]
    B2 --> C
    B3 --> C
    B4 --> C
    B5 --> C
    B6 --> C
    B7 --> C
    B8 --> C
    B9 --> C
    
    C --> D[Transformação Paralela]
    D --> D1[Transform Produtos]
    D --> D2[Transform Clientes]
    D --> D3[Transform Leads]
    D --> D4[Transform Propostas]
    
    D1 --> E1[Load/Export Produtos]
    D2 --> E2[Load/Export Clientes]
    D3 --> E3[Load/Export Leads]
    D4 --> E4[Load/Export Propostas]
    
    E1 --> F[Fim do Pipeline]
    E2 --> F
    E3 --> F
    E4 --> F
```

## 🔍 Fase 1: Extração de Dados

### 📊 Fontes de Dados

#### NewCon (SQL Server)
- **Servidor Principal**: *********** (Batman)
- **Servidor Failover**: *********** (Jocker)
- **Tabelas Extraídas**:
  - `newcon_clients` (~73k registros)
  - `newcon_products` (~20k registros)
  - `newcon_leads` (volume variável)
  - `newcon_proposals` (~533k registros)

#### RD Station (API REST)
- **Endpoint**: https://crm.rdstation.com/api/v1
- **Rate Limit**: 180 req/min, 3 req/sec
- **Dados**: Leads com paginação automática
- **Limite da API**: 10.000 registros (contornado com ordenação)

#### Orbbits (MySQL)
- **Servidor**: ************
- **Tabelas Complementares**:
  - `orbbits_origin` (dados de origem)
  - `orbbits_payments` (pagamentos)
  - `orbbits_sales` (vendas)
  - `orbbits_prices` (preços)

### ⚡ Paralelização da Extração

#### Estratégia Ultra-Paralela
- **9 Extrações Simultâneas**: Cada tabela é extraída independentemente
- **Isolamento de Falhas**: Falha em uma tabela não afeta outras
- **Otimização de Recursos**: Máximo aproveitamento de CPU e rede

#### Implementação Técnica
```python
# Exemplo de extração paralela
def airflow_extract_newcon_clients_individual_task(**context):
    """Extrai apenas clientes do NewCon"""
    extractor = DataExtractor()
    data = extractor.extract_newcon_clients()
    return save_extraction_data('newcon_clients', data)

# Cada tabela tem sua função específica
extract_functions = [
    airflow_extract_newcon_clients_individual_task,
    airflow_extract_newcon_products_individual_task,
    # ... outras 7 funções
]
```

### 🔄 Tratamento de Falhas

#### Retry Automático
- **Tentativas**: 3 por padrão
- **Backoff Exponencial**: 1s, 2s, 4s, 8s...
- **Jitter**: Aleatoriedade para evitar thundering herd

#### Failover de Conexões
```python
# Exemplo de failover NewCon
try:
    conn = connect_to_batman()  # Servidor principal
except ConnectionError:
    conn = connect_to_jocker()  # Servidor failover
```

## 🔄 Fase 2: Transformação de Dados

### 📋 Consolidação de Dados

#### Processo de Consolidação
1. **Coleta**: Reúne dados de todas as 9 extrações
2. **Validação**: Verifica integridade dos dados extraídos
3. **Preparação**: Organiza dados para transformação paralela

#### Estrutura de Dados Consolidados
```python
consolidated_data = {
    'newcon_clients': DataFrame,
    'newcon_products': DataFrame,
    'newcon_leads': DataFrame,
    'newcon_proposals': DataFrame,
    'rdstation_leads': DataFrame,
    'orbbits_origin': DataFrame,
    'orbbits_payments': DataFrame,
    'orbbits_sales': DataFrame,
    'orbbits_prices': DataFrame
}
```

### 🔧 Transformações por Tabela Final

#### tb_produtos (Produtos)
**Fontes**: `newcon_products` + `orbbits_prices`
```python
def transform_produtos_only(consolidated_data):
    # 1. Extrair dados de produtos
    produtos = consolidated_data['newcon_products']
    precos = consolidated_data['orbbits_prices']
    
    # 2. Enriquecer com preços do Orbbits
    produtos_enriched = merge_price_data(produtos, precos)
    
    # 3. Aplicar transformações
    produtos_final = apply_product_transformations(produtos_enriched)
    
    # 4. Validar campos obrigatórios
    validate_required_fields(produtos_final, ['id_produto'])
    
    return produtos_final
```

**Transformações Aplicadas**:
- Limpeza de valores nulos
- Formatação de preços e datas
- Enriquecimento com dados de preços
- Validação de campos obrigatórios

#### tb_clientes (Clientes)
**Fontes**: `newcon_clients` + `orbbits_origin`
```python
def transform_clientes_only(consolidated_data):
    # 1. Dados base de clientes
    clientes = consolidated_data['newcon_clients']
    origem = consolidated_data['orbbits_origin']
    
    # 2. Enriquecer com dados de origem
    clientes_enriched = merge_origin_data(clientes, origem)
    
    # 3. Aplicar limpeza e formatação
    clientes_final = apply_client_transformations(clientes_enriched)
    
    # 4. Validar campos obrigatórios
    validate_required_fields(clientes_final, ['cnpjcpf', 'email'])
    
    return clientes_final
```

**Transformações Aplicadas**:
- Normalização de CPF/CNPJ
- Validação de emails
- Limpeza de dados de contato
- Enriquecimento com dados de origem

#### tb_leads (Leads)
**Fontes**: `newcon_leads` + `rdstation_leads`
```python
def transform_leads_only(consolidated_data):
    # 1. Combinar leads de múltiplas fontes
    newcon_leads = consolidated_data['newcon_leads']
    rd_leads = consolidated_data['rdstation_leads']
    
    # 2. Unificar estruturas de dados
    leads_unified = unify_lead_structures(newcon_leads, rd_leads)
    
    # 3. Remover duplicatas
    leads_deduped = remove_lead_duplicates(leads_unified)
    
    # 4. Aplicar transformações
    leads_final = apply_lead_transformations(leads_deduped)
    
    # 5. Validar campos obrigatórios
    validate_required_fields(leads_final, ['cnpjcpf', 'dt_simulacao'])
    
    return leads_final
```

**Transformações Aplicadas**:
- Unificação de estruturas diferentes
- Remoção de duplicatas
- Normalização de datas
- Enriquecimento de dados de simulação

#### tb_propostas (Propostas)
**Fontes**: `newcon_proposals` + `orbbits_payments` + `orbbits_sales`
```python
def transform_propostas_only(consolidated_data):
    # 1. Dados base de propostas
    propostas = consolidated_data['newcon_proposals']
    pagamentos = consolidated_data['orbbits_payments']
    vendas = consolidated_data['orbbits_sales']
    
    # 2. Enriquecer com dados financeiros
    propostas_enriched = merge_financial_data(propostas, pagamentos, vendas)
    
    # 3. Calcular métricas financeiras
    propostas_calculated = calculate_financial_metrics(propostas_enriched)
    
    # 4. Aplicar transformações
    propostas_final = apply_proposal_transformations(propostas_calculated)
    
    # 5. Validar campos obrigatórios
    validate_required_fields(propostas_final, ['idproposta', 'email'])
    
    return propostas_final
```

**Transformações Aplicadas**:
- Enriquecimento com dados financeiros
- Cálculo de métricas de performance
- Normalização de valores monetários
- Validação de integridade financeira

### 🔍 Validação de Qualidade

#### Verificações Automáticas
```python
def validate_data_quality(data, table_name):
    checks = [
        check_required_fields(data, table_name),
        check_data_types(data, table_name),
        check_value_ranges(data, table_name),
        check_business_rules(data, table_name),
        check_referential_integrity(data, table_name)
    ]
    return all(checks)
```

#### Campos Obrigatórios por Tabela
- **tb_produtos**: `id_produto`
- **tb_clientes**: `cnpjcpf`, `email`
- **tb_leads**: `cnpjcpf`, `dt_simulacao`
- **tb_propostas**: `idproposta`, `email`

## 📤 Fase 3: Carregamento/Exportação

### 🎯 Modos de Saída

#### Modo Salesforce (Produção)
```python
def load_to_salesforce(data, table_name):
    # 1. Criar cliente Salesforce
    sf_client = create_salesforce_client()
    
    # 2. Dividir em lotes
    batches = create_batches(data, batch_size=2000)
    
    # 3. Enviar lotes paralelos
    results = []
    for batch in batches:
        result = sf_client.upsert_data_extension(
            external_key=get_external_key(table_name),
            data=batch
        )
        results.append(result)
    
    # 4. Verificar status (se habilitado)
    if VERIFY_STATUS:
        verify_batch_status(results)
    
    return results
```

#### Modo CSV (Teste/Validação)
```python
def export_to_csv(data, table_name):
    # 1. Gerar nome do arquivo com timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{table_name}_{timestamp}.csv"
    filepath = os.path.join(CSV_OUTPUT_DIR, filename)
    
    # 2. Exportar dados
    data.to_csv(filepath, index=False, encoding='utf-8')
    
    # 3. Validar arquivo gerado
    validate_csv_file(filepath)
    
    return filepath
```

### 🔄 Processamento em Lotes

#### Estratégia de Lotes
- **Tamanho Padrão**: 2.000 registros (limite Salesforce)
- **Processamento Paralelo**: Múltiplos lotes simultâneos
- **Rate Limiting**: Respeita limites da API

#### Monitoramento de Lotes
```python
def monitor_batch_processing(batches):
    for batch_id, batch in enumerate(batches):
        logger.info(f"Processando lote {batch_id+1}/{len(batches)}")
        logger.info(f"Registros no lote: {len(batch)}")
        
        # Processar lote
        result = process_batch(batch)
        
        # Log resultado
        if result.success:
            logger.info(f"✅ Lote {batch_id+1} processado com sucesso")
        else:
            logger.error(f"❌ Falha no lote {batch_id+1}: {result.error}")
```

### 📊 Data Extensions de Destino

#### Configuração das Data Extensions
```python
DATA_EXTENSIONS = {
    'tb_produtos': {
        'external_key': 'FCC1DCA7-D286-458D-BDCC-D050C1BA61A8',
        'estimated_records': 20502,
        'batch_count': 11,
        'priority': 1
    },
    'tb_clientes': {
        'external_key': 'B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E',
        'estimated_records': 73498,
        'batch_count': 37,
        'priority': 2
    },
    'tb_leads': {
        'external_key': 'EC0B7BFF-EC89-4A4D-914B-749F14B6F861',
        'estimated_records': 0,  # Variável
        'batch_count': 0,        # Dinâmico
        'priority': 3
    },
    'tb_propostas': {
        'external_key': '36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA',
        'estimated_records': 533238,
        'batch_count': 267,
        'priority': 4
    }
}
```

## ⏱️ Performance e Métricas

### 📈 Tempos de Execução Esperados

| Fase | Tempo (Modo Paralelo) | Tempo (Modo Sequencial) |
|------|----------------------|-------------------------|
| **Extração** | 15-25 min | 45-75 min |
| **Transformação** | 5-10 min | 15-25 min |
| **Carregamento** | 10-20 min | 30-50 min |
| **Total** | **30-55 min** | **90-150 min** |

### 📊 Volumes de Dados

| Tabela | Volume Estimado | Lotes | Tempo Estimado |
|--------|----------------|-------|----------------|
| **tb_produtos** | ~20k registros | 11 lotes | 2-3 min |
| **tb_clientes** | ~73k registros | 37 lotes | 5-8 min |
| **tb_leads** | Variável | Dinâmico | 3-10 min |
| **tb_propostas** | ~533k registros | 267 lotes | 15-20 min |

### 🎯 Otimizações Implementadas

#### Paralelização Máxima
- 9 extrações simultâneas
- 4 transformações paralelas
- 4 carregamentos paralelos

#### Otimizações de Rede
- Connection pooling
- Keep-alive connections
- Retry com backoff exponencial

#### Otimizações de Memória
- Processamento em chunks
- Garbage collection otimizado
- Streaming de dados grandes

## 🔍 Monitoramento e Logs

### 📝 Logs Estruturados
```python
# Exemplo de log estruturado
logger.info("Iniciando extração", extra={
    'phase': 'extraction',
    'table': 'newcon_clients',
    'estimated_records': 73498,
    'start_time': datetime.now().isoformat()
})
```

### 📊 Métricas Coletadas
- Tempo de execução por fase
- Volume de registros processados
- Taxa de sucesso/falha
- Uso de recursos (CPU, memória)
- Latência de rede

### 🚨 Alertas Automáticos
- Falhas de conexão
- Timeouts de processamento
- Volumes anômalos de dados
- Falhas de validação

---

**Próximos Passos**: Consulte a [Documentação Técnica dos Módulos](MODULES.md) para detalhes de implementação de cada componente.
