# 🚀 Guia de Instalação - ETL Salesforce Marketing Cloud

## 📋 Pré-requisitos

### Sistema Operacional
- **Linux**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Python**: 3.8 ou superior
- **Memória**: <PERSON><PERSON>imo 4GB RAM (Recomendado: 8GB+)
- **Disco**: Mínimo 10GB livres
- **Rede**: Acesso às APIs e bancos de dados

### Softwares Necessários
- **Apache Airflow 2.5+**
- **Git** (para versionamento)
- **Drivers de banco de dados** (ver seção específica)

## 🔧 Instalação Passo a Passo

### 1. Preparação do Ambiente

#### Ubuntu/Debian
```bash
# Atualizar sistema
sudo apt-get update && sudo apt-get upgrade -y

# Instalar dependências do sistema
sudo apt-get install -y \
    python3-dev \
    python3-pip \
    python3-venv \
    libpq-dev \
    default-libmysqlclient-dev \
    unixodbc-dev \
    build-essential \
    git \
    curl \
    wget
```

#### CentOS/RHEL
```bash
# Atualizar sistema
sudo yum update -y

# Instalar dependências do sistema
sudo yum install -y \
    python3-devel \
    python3-pip \
    postgresql-devel \
    mysql-devel \
    unixODBC-devel \
    gcc \
    gcc-c++ \
    git \
    curl \
    wget
```

### 2. Configuração do Python

```bash
# Criar ambiente virtual
python3 -m venv /opt/airflow/venv

# Ativar ambiente virtual
source /opt/airflow/venv/bin/activate

# Atualizar pip
pip install --upgrade pip setuptools wheel
```

### 3. Instalação do Apache Airflow

```bash
# Definir variáveis de ambiente
export AIRFLOW_HOME=/opt/airflow
export AIRFLOW_VERSION=2.5.3
export PYTHON_VERSION="$(python --version | cut -d " " -f 2 | cut -d "." -f 1-2)"
export CONSTRAINT_URL="https://raw.githubusercontent.com/apache/airflow/constraints-${AIRFLOW_VERSION}/constraints-${PYTHON_VERSION}.txt"

# Instalar Airflow
pip install "apache-airflow==${AIRFLOW_VERSION}" --constraint "${CONSTRAINT_URL}"

# Inicializar banco de dados do Airflow
airflow db init

# Criar usuário admin
airflow users create \
    --username admin \
    --firstname Admin \
    --lastname User \
    --role Admin \
    --email <EMAIL> \
    --password admin123
```

### 4. Drivers de Banco de Dados

#### SQL Server (NewCon)
```bash
# Instalar driver ODBC para SQL Server
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
sudo apt-get update
sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17 mssql-tools

# Adicionar ao PATH
echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc
source ~/.bashrc
```

#### PostgreSQL (DW Corporativo)
```bash
# Driver já incluído nas dependências do sistema
# Verificar instalação
python -c "import psycopg2; print('PostgreSQL driver OK')"
```

#### MySQL (Orbbits)
```bash
# Driver já incluído nas dependências do sistema
# Verificar instalação
python -c "import mysql.connector; print('MySQL driver OK')"
```

### 5. Instalação das Dependências Python

```bash
# Navegar para o diretório do projeto
cd /opt/airflow/dags/salesforce_integration

# Instalar dependências
pip install -r requirements.txt

# Verificar instalação
python -c "
import pandas as pd
import requests
import pymssql
import psycopg2
import mysql.connector
from rdstation.crm import CRMClient
print('✅ Todas as dependências instaladas com sucesso!')
"
```

### 6. Configuração de Variáveis de Ambiente

#### Criar arquivo .env
```bash
# Criar arquivo de configuração
cat > /opt/airflow/.env << 'EOF'
# =============================================================================
# CONFIGURAÇÕES DE BANCO DE DADOS
# =============================================================================

# NewCon (SQL Server)
NEWCON_HOST_BATMAN=***********
NEWCON_HOST_JOCKER=***********
NEWCON_PORT=1433
NEWCON_DATABASE=dbNewCon
NEWCON_USER=seu_usuario
NEWCON_PASSWORD=sua_senha
NEWCON_DRIVER=ODBC Driver 17 for SQL Server

# DW Corporativo (PostgreSQL)
DW_HOST=***********
DW_PORT=5432
DW_DATABASE=postgres
DW_USER=seu_usuario
DW_PASSWORD=sua_senha

# Orbbits (MySQL)
ORBBITS_HOST=************
ORBBITS_PORT=3306
ORBBITS_DATABASE=seu_database
ORBBITS_USER=seu_usuario
ORBBITS_PASSWORD=sua_senha

# =============================================================================
# SALESFORCE MARKETING CLOUD
# =============================================================================

SALESFORCE_CLIENT_ID=qfdxgdxstw6wlnn0pnl8usyr
SALESFORCE_CLIENT_SECRET=seu_client_secret
SALESFORCE_AUTH_URI=https://mc5jxdhk62pllmhs0nc0r--9qgb4.auth.marketingcloudapis.com/
SALESFORCE_REST_URI=https://mc5jxdhk62pllmhs0nc0r--9qgb4.rest.marketingcloudapis.com/

# =============================================================================
# RD STATION
# =============================================================================

RDSTATION_TOKEN=63dcebb7505962001bdfec12

# =============================================================================
# CONFIGURAÇÕES DE OPERAÇÃO
# =============================================================================

# Modo de saída: 'salesforce' ou 'csv'
SALESFORCE_OUTPUT_MODE=csv

# Modo de verificação: true (seguro) ou false (rápido)
SALESFORCE_VERIFY_STATUS=true

# Ambiente: production, staging, testing, development
ENVIRONMENT=production

# Debug mode
DEBUG=false

# Dry run mode
DRY_RUN=false

EOF
```

#### Configurar permissões
```bash
# Proteger arquivo de configuração
chmod 600 /opt/airflow/.env
chown airflow:airflow /opt/airflow/.env

# Carregar variáveis no sistema
source /opt/airflow/.env
```

### 7. Configuração do Airflow

#### Configurar airflow.cfg
```bash
# Editar configuração do Airflow
nano $AIRFLOW_HOME/airflow.cfg

# Principais configurações a ajustar:
# [core]
# dags_folder = /opt/airflow/dags
# load_examples = False
# max_active_runs_per_dag = 1
# parallelism = 32
# max_active_tasks_per_dag = 16

# [webserver]
# web_server_port = 8080
# base_url = http://localhost:8080

# [scheduler]
# catchup_by_default = False
```

### 8. Teste da Instalação

#### Verificar conexões de banco
```bash
cd /opt/airflow/dags/salesforce_integration

# Testar conexões
python -c "
from database_connections import test_all_connections
if test_all_connections():
    print('✅ Todas as conexões de banco funcionando!')
else:
    print('❌ Problemas nas conexões de banco')
"
```

#### Verificar configuração do Salesforce
```bash
# Testar configuração Salesforce
python -c "
from salesforce_client import validate_salesforce_config, test_salesforce_connection
is_valid, missing = validate_salesforce_config()
if is_valid:
    print('✅ Configuração Salesforce válida!')
    if test_salesforce_connection():
        print('✅ Conexão Salesforce funcionando!')
    else:
        print('❌ Falha na conexão Salesforce')
else:
    print(f'❌ Configuração Salesforce inválida: {missing}')
"
```

#### Testar pipeline completo (modo CSV)
```bash
# Executar teste com amostra pequena
export SALESFORCE_OUTPUT_MODE=csv
python etl_main.py --test-sample 100 --dry-run

# Verificar arquivos gerados
ls -la carga_fria/
```

### 9. Inicialização dos Serviços

#### Criar serviços systemd

**Airflow Webserver**
```bash
sudo cat > /etc/systemd/system/airflow-webserver.service << 'EOF'
[Unit]
Description=Airflow webserver daemon
After=network.target

[Service]
Environment=AIRFLOW_HOME=/opt/airflow
User=airflow
Group=airflow
Type=notify
ExecStart=/opt/airflow/venv/bin/airflow webserver
Restart=on-failure
RestartSec=5s
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF
```

**Airflow Scheduler**
```bash
sudo cat > /etc/systemd/system/airflow-scheduler.service << 'EOF'
[Unit]
Description=Airflow scheduler daemon
After=network.target

[Service]
Environment=AIRFLOW_HOME=/opt/airflow
User=airflow
Group=airflow
Type=notify
ExecStart=/opt/airflow/venv/bin/airflow scheduler
Restart=always
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF
```

#### Iniciar serviços
```bash
# Recarregar systemd
sudo systemctl daemon-reload

# Habilitar e iniciar serviços
sudo systemctl enable airflow-webserver airflow-scheduler
sudo systemctl start airflow-webserver airflow-scheduler

# Verificar status
sudo systemctl status airflow-webserver airflow-scheduler
```

### 10. Verificação Final

```bash
# Verificar se Airflow está rodando
curl http://localhost:8080/health

# Verificar DAG no Airflow
airflow dags list | grep etl_salesforce

# Testar execução manual
airflow dags trigger etl_salesforce_marketing_cloud_table_parallel
```

## 🔍 Verificação de Problemas

### Logs importantes
- **Airflow**: `$AIRFLOW_HOME/logs/`
- **ETL**: `logs/etl_consolidated.log`
- **Sistema**: `/var/log/syslog`

### Comandos úteis
```bash
# Verificar status dos serviços
sudo systemctl status airflow-*

# Reiniciar serviços
sudo systemctl restart airflow-webserver airflow-scheduler

# Verificar logs em tempo real
tail -f $AIRFLOW_HOME/logs/scheduler/latest/*.log
```

## ✅ Checklist de Instalação

- [ ] Sistema operacional atualizado
- [ ] Python 3.8+ instalado
- [ ] Ambiente virtual criado
- [ ] Apache Airflow instalado e configurado
- [ ] Drivers de banco instalados
- [ ] Dependências Python instaladas
- [ ] Variáveis de ambiente configuradas
- [ ] Conexões de banco testadas
- [ ] Configuração Salesforce validada
- [ ] Pipeline testado em modo CSV
- [ ] Serviços systemd configurados
- [ ] Airflow webserver acessível
- [ ] DAG visível no Airflow

---

**Próximos Passos**: Consulte o [Manual de Operação](OPERATION.md) para aprender a executar e monitorar o sistema.
