# ⚙️ Documentação de Configurações - ETL Salesforce Marketing Cloud

## 📋 Visão Geral

Este documento detalha todas as configurações disponíveis no sistema ETL, incluindo variáveis de ambiente, parâmetros de conexão e configurações operacionais.

## 🔧 Arquivo de Configuração Principal

O arquivo `config.py` centraliza todas as configurações do sistema. Ele utiliza a biblioteca `python-decouple` para carregar variáveis de ambiente com valores padrão.

### Estrutura do config.py
```python
from decouple import config

# Exemplo de uso
DATABASE_HOST = config('DB_HOST', default='localhost')
DATABASE_PORT = config('DB_PORT', default=5432, cast=int)
DEBUG_MODE = config('DEBUG', default=False, cast=bool)
```

## 🗄️ Configurações de Banco de Dados

### NewCon (SQL Server)

#### Configuração Principal (<PERSON>)
```bash
# Servidor primário
NEWCON_HOST_BATMAN=***********
NEWCON_PORT=1433
NEWCON_DATABASE=dbNewCon
NEWCON_USER=seu_usuario
NEWCON_PASSWORD=sua_senha_segura
NEWCON_DRIVER="ODBC Driver 17 for SQL Server"
NEWCON_TIMEOUT=30
```

#### Configuração Secundária (Jocker - Failover)
```bash
# Servidor de failover
NEWCON_HOST_JOCKER=***********
# Demais configurações iguais ao primário
```

#### Configurações de Pool
```bash
NEWCON_pool_size=3          # Conexões simultâneas
NEWCON_MAX_OVERFLOW=10      # Conexões extras em pico
NEWCON_RETRY_ATTEMPTS=3     # Tentativas de reconexão
NEWCON_RETRY_DELAY=1        # Delay entre tentativas (segundos)
```

### DW Corporativo (PostgreSQL)

```bash
DW_HOST=***********
DW_PORT=5432
DW_DATABASE=postgres
DW_USER=seu_usuario
DW_PASSWORD=sua_senha_segura
DW_TIMEOUT=30
DW_pool_size=3
DW_MAX_OVERFLOW=10
DW_RETRY_ATTEMPTS=3
DW_RETRY_DELAY=1
```

### Orbbits (MySQL)

```bash
ORBBITS_HOST=************
ORBBITS_PORT=3306
ORBBITS_DATABASE=seu_database
ORBBITS_USER=seu_usuario
ORBBITS_PASSWORD=sua_senha_segura
ORBBITS_TIMEOUT=30
ORBBITS_pool_size=3
ORBBITS_MAX_OVERFLOW=10
ORBBITS_RETRY_ATTEMPTS=3
ORBBITS_RETRY_DELAY=1
```

## 🌐 Configurações Salesforce Marketing Cloud

### Autenticação
```bash
# Credenciais de aplicação
SALESFORCE_CLIENT_ID=qfdxgdxstw6wlnn0pnl8usyr
SALESFORCE_CLIENT_SECRET=seu_client_secret_aqui

# URLs de API
SALESFORCE_AUTH_URI=https://mc5jxdhk62pllmhs0nc0r--9qgb4.auth.marketingcloudapis.com/
SALESFORCE_REST_URI=https://mc5jxdhk62pllmhs0nc0r--9qgb4.rest.marketingcloudapis.com/
```

### Configurações de Performance
```bash
SALESFORCE_TIMEOUT=300              # Timeout de requisições (segundos)
SALESFORCE_BATCH_SIZE=2000          # Registros por lote
SALESFORCE_RATE_LIMIT=200           # Requisições por minuto
SALESFORCE_RETRY_ATTEMPTS=3         # Tentativas de retry
SALESFORCE_RETRY_DELAY=2            # Delay entre retries (segundos)
SALESFORCE_EXPONENTIAL_BACKOFF=true # Backoff exponencial
SALESFORCE_POLLING_INTERVAL=5       # Intervalo de polling (segundos)
SALESFORCE_MAX_POLLING_TIME=300     # Tempo máximo de polling (segundos)
```

### Modo de Operação
```bash
# Verificação de status dos lotes
SALESFORCE_VERIFY_STATUS=true       # true=Seguro, false=Rápido

# Modo de saída
SALESFORCE_OUTPUT_MODE=salesforce    # 'salesforce' ou 'csv'

# Diretório para CSVs (quando OUTPUT_MODE=csv)
CSV_OUTPUT_DIR=/opt/airflow/dags/salesforce_integration/carga_fria
```

## 📡 Configurações RD Station

### API de Acesso
```bash
RDSTATION_TOKEN=63dcebb7505962001bdfec12
RDSTATION_BASE_URL=https://crm.rdstation.com/api/v1
```

### Rate Limiting
```bash
RDSTATION_RATE_LIMIT=180            # Requisições por minuto
RDSTATION_RATE_LIMIT_PER_SECOND=3   # Requisições por segundo
RDSTATION_TIMEOUT=30                # Timeout de requisições
RDSTATION_RETRY_ATTEMPTS=3          # Tentativas de retry
RDSTATION_RETRY_DELAY=1             # Delay entre retries
```

### Paginação
```bash
RDSTATION_PAGE_SIZE=200             # Registros por página
RDSTATION_MAX_PAGES=1000            # Máximo de páginas
RDSTATION_MAX_OFFSET_LIMIT=10000    # Limite de offset da API
```

## 🎯 Data Extensions (Tabelas Destino)

### Configuração das Tabelas
```python
DATA_EXTENSIONS = {
    'tb_produtos': {
        'external_key': 'FCC1DCA7-D286-458D-BDCC-D050C1BA61A8',
        'required_fields': ['id_produto'],
        'estimated_records': 20502,
        'batch_count': 11,
        'priority': 1,
    },
    'tb_clientes': {
        'external_key': 'B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E',
        'required_fields': ['cnpjcpf', 'email'],
        'estimated_records': 73498,
        'batch_count': 37,
        'priority': 2,
    },
    'tb_leads': {
        'external_key': 'EC0B7BFF-EC89-4A4D-914B-749F14B6F861',
        'required_fields': ['cnpjcpf', 'dt_simulacao'],
        'estimated_records': 0,  # Variável
        'batch_count': 0,        # Dinâmico
        'priority': 3,
    },
    'tb_propostas': {
        'external_key': '36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA',
        'required_fields': ['idproposta', 'email'],
        'estimated_records': 533238,
        'batch_count': 267,
        'priority': 4,
    }
}
```

## 📊 Configurações de Processamento

### Tamanhos de Lote
```bash
BATCH_SIZE_SALESFORCE=2000          # Lotes para Salesforce
BATCH_SIZE_DATABASE=10000           # Lotes para consultas de banco
BATCH_SIZE_RDSTATION=200            # Lotes para RD Station
BATCH_SIZE_MEMORY=50000             # Limite de memória
```

### Configurações de Retry
```bash
RETRY_MAX_ATTEMPTS=3                # Máximo de tentativas
RETRY_BASE_DELAY=1                  # Delay base (segundos)
RETRY_MAX_DELAY=60                  # Delay máximo (segundos)
RETRY_EXPONENTIAL_BASE=2            # Base do backoff exponencial
RETRY_JITTER=true                   # Adicionar jitter aleatório
```

### Timeouts
```bash
TIMEOUT_DATABASE_QUERY=300          # Timeout para consultas (segundos)
TIMEOUT_API_REQUEST=60              # Timeout para APIs (segundos)
TIMEOUT_FILE_OPERATION=30           # Timeout para arquivos (segundos)
TIMEOUT_TOTAL_PIPELINE=7200         # Timeout total do pipeline (segundos)
```

## 📝 Configurações de Logging

### Configuração Básica
```bash
LOG_LEVEL=INFO                      # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_PATH=logs/etl_consolidated.log
LOG_MAX_FILE_SIZE=10485760          # 10MB
LOG_BACKUP_COUNT=5                  # Arquivos de backup
LOG_CONSOLE_OUTPUT=true             # Saída no console
LOG_JSON_FORMAT=false               # Formato JSON
```

### Níveis de Log Detalhados
- **DEBUG**: Informações muito detalhadas para desenvolvimento
- **INFO**: Operações normais e marcos importantes
- **WARNING**: Situações que requerem atenção mas não impedem execução
- **ERROR**: Erros que impedem execução de partes do sistema
- **CRITICAL**: Falhas que impedem execução completa

## 📈 Configurações de Monitoramento

### Métricas e Alertas
```bash
MONITORING_PROGRESS_BAR=true        # Barras de progresso
MONITORING_METRICS=true             # Coleta de métricas
MONITORING_METRICS_INTERVAL=10      # Intervalo de coleta (segundos)
MONITORING_ALERTS=true              # Sistema de alertas
MONITORING_ALERT_EMAIL=<EMAIL>
MONITORING_WEBHOOK_URL=https://hooks.slack.com/...
```

## 🌍 Configurações de Ambiente

### Ambiente de Execução
```bash
ENVIRONMENT=production              # production, staging, testing, development
DEBUG=false                         # Modo debug
DRY_RUN=false                      # Modo dry-run (não modifica dados)
PARALLEL_EXECUTION=true            # Execução paralela
MAX_WORKERS=4                      # Máximo de workers paralelos
MEMORY_LIMIT_MB=2048               # Limite de memória (MB)
```

### Ambientes Suportados
- **production**: Ambiente de produção com todas as validações
- **staging**: Ambiente de homologação para testes finais
- **testing**: Ambiente de testes com dados limitados
- **development**: Ambiente de desenvolvimento local

## 🔒 Configurações de Segurança

### Proteção de Dados
```bash
SECURITY_MASK_SENSITIVE_DATA=true   # Mascarar dados sensíveis nos logs
SECURITY_LOG_SENSITIVE_DATA=false   # Não logar dados sensíveis
SECURITY_ENCRYPT_CREDENTIALS=true   # Criptografar credenciais
SECURITY_AUDIT_TRAIL=true          # Trilha de auditoria
```

### Dados Considerados Sensíveis
- Senhas e tokens de API
- CPF/CNPJ completos
- Endereços de email
- Números de telefone
- Dados financeiros

## 🔄 Sequência de Execução

### Ordem de Processamento
```python
EXECUTION_SEQUENCE = [
    'tb_produtos',    # Prioridade 1 - Mais rápido
    'tb_clientes',    # Prioridade 2 - Volume médio
    'tb_leads',       # Prioridade 3 - Volume variável
    'tb_propostas',   # Prioridade 4 - Maior volume
]
```

## 🛠️ Funções de Configuração

### Funções Auxiliares Disponíveis
```python
# Obter configuração de banco específico
config = get_database_config('newcon')

# Obter configuração de Data Extension
de_config = get_data_extension_config('tb_produtos')

# Obter tamanho de lote para contexto
batch_size = get_batch_size('salesforce_upload')

# Verificar modo dry-run
if is_dry_run():
    print("Executando em modo dry-run")

# Verificar modo debug
if is_debug():
    print("Modo debug ativado")

# Obter ordem de execução
order = get_execution_order()
```

### Validação de Configurações
```python
# Validar variáveis obrigatórias
is_valid, missing_vars = validate_required_env_vars()

# Validar configuração completa
validate_configuration()  # Levanta exceção se inválida
```

## 📋 Checklist de Configuração

### Variáveis Obrigatórias
- [ ] `NEWCON_USER` e `NEWCON_PASSWORD`
- [ ] `DW_USER` e `DW_PASSWORD`
- [ ] `ORBBITS_DATABASE`, `ORBBITS_USER` e `ORBBITS_PASSWORD`
- [ ] `SALESFORCE_CLIENT_SECRET`

### Configurações Recomendadas para Produção
- [ ] `SALESFORCE_VERIFY_STATUS=true`
- [ ] `ENVIRONMENT=production`
- [ ] `DEBUG=false`
- [ ] `LOG_LEVEL=INFO`
- [ ] `SECURITY_MASK_SENSITIVE_DATA=true`
- [ ] `MONITORING_ALERTS=true`

### Configurações para Desenvolvimento
- [ ] `SALESFORCE_OUTPUT_MODE=csv`
- [ ] `ENVIRONMENT=development`
- [ ] `DEBUG=true`
- [ ] `LOG_LEVEL=DEBUG`
- [ ] `DRY_RUN=true` (para testes iniciais)

## 🔧 Personalização de Configurações

### Sobrescrever Configurações
```bash
# Via variáveis de ambiente (recomendado)
export SALESFORCE_BATCH_SIZE=1000

# Via arquivo .env
echo "SALESFORCE_BATCH_SIZE=1000" >> .env

# Via código (não recomendado para produção)
# Editar config.py diretamente
```

### Configurações por Ambiente
```bash
# Criar arquivos específicos por ambiente
.env.production
.env.staging
.env.development

# Carregar arquivo específico
source .env.production
```

---

**Próximos Passos**: Consulte a [Documentação Técnica dos Módulos](MODULES.md) para detalhes de implementação.
