# 🎮 Manual de Operação - ETL Salesforce Marketing Cloud

## 📋 Visão Geral das Operações

Este manual descreve como executar, monitorar e gerenciar o pipeline ETL no dia a dia. O sistema oferece múltiplas formas de execução para diferentes cenários.

## 🚀 Formas de Execução

### 1. Execução via Airflow (Recomendada para Produção)

#### Interface Web
1. Acesse: `http://localhost:8080`
2. Login: `admin` / `admin123`
3. Navegue para DAGs
4. Localize: `etl_salesforce_marketing_cloud_table_parallel`
5. Clique no botão ▶️ para executar

#### Linha de Comando
```bash
# Ativar DAG
airflow dags unpause etl_salesforce_marketing_cloud_table_parallel

# Executar manualmente
airflow dags trigger etl_salesforce_marketing_cloud_table_parallel

# Verificar status
airflow dags state etl_salesforce_marketing_cloud_table_parallel $(date +%Y-%m-%d)

# Listar execuções
airflow dags list-runs -d etl_salesforce_marketing_cloud_table_parallel
```

### 2. Execução Direta via Python

#### Execução Completa
```bash
cd /opt/airflow/dags/salesforce_integration

# Modo produção (carrega no Salesforce)
export SALESFORCE_OUTPUT_MODE=salesforce
python etl_main.py

# Modo teste (gera CSVs)
export SALESFORCE_OUTPUT_MODE=csv
python etl_main.py

# Modo dry-run (não modifica dados)
python etl_main.py --dry-run

# Teste com amostra pequena
python etl_main.py --test-sample 1000
```

#### Execução Seletiva por Tabela
```bash
# Apenas produtos
python etl_main.py --tables produtos

# Apenas clientes e leads
python etl_main.py --tables clientes,leads

# Todas exceto propostas
python etl_main.py --exclude propostas
```

## ⚙️ Configuração de Modos de Operação

### 🎯 Modo de Saída

#### Salesforce (Produção)
```bash
export SALESFORCE_OUTPUT_MODE=salesforce
```
- Carrega dados diretamente no Salesforce Marketing Cloud
- Usa Data Extensions configuradas
- Requer credenciais válidas do SFMC

#### CSV (Teste/Validação)
```bash
export SALESFORCE_OUTPUT_MODE=csv
```
- Gera arquivos CSV no diretório `carga_fria/`
- Permite revisão manual antes da carga
- Ideal para validação de dados

### 🔍 Modo de Verificação

#### Modo Seguro (Recomendado)
```bash
export SALESFORCE_VERIFY_STATUS=true
```
- Verifica status de todos os lotes enviados
- Execução mais lenta mas confiável
- Detecta falhas de carregamento

#### Modo Rápido
```bash
export SALESFORCE_VERIFY_STATUS=false
```
- Não verifica status dos lotes
- Execução mais rápida
- Usar apenas em ambientes de teste

## 📊 Monitoramento e Logs

### 📈 Interface do Airflow

#### Dashboard Principal
- **Graph View**: Visualiza dependências entre tarefas
- **Tree View**: Histórico de execuções
- **Gantt View**: Timeline de execução
- **Task Duration**: Performance das tarefas

#### Logs Detalhados
1. Clique na tarefa desejada
2. Selecione "Log"
3. Monitore progresso em tempo real

### 📝 Logs do Sistema

#### Localização dos Logs
```bash
# Logs principais do ETL
tail -f /opt/airflow/dags/salesforce_integration/logs/etl_consolidated.log

# Logs do Airflow
tail -f $AIRFLOW_HOME/logs/scheduler/latest/*.log

# Logs do sistema
tail -f /var/log/syslog | grep airflow
```

#### Níveis de Log
- **INFO**: Operações normais
- **WARNING**: Situações que requerem atenção
- **ERROR**: Falhas que impedem execução
- **DEBUG**: Informações detalhadas (apenas em modo debug)

### 📊 Métricas de Performance

#### Tempos Esperados (Modo Paralelo)
| Fase | Tempo Estimado | Volume |
|------|----------------|--------|
| **Extração Total** | 15-25 min | 9 tabelas simultâneas |
| **Transformação** | 5-10 min | 4 tabelas paralelas |
| **Carregamento** | 10-20 min | 4 Data Extensions |
| **Total** | 30-55 min | ~630k registros |

#### Indicadores de Saúde
```bash
# Verificar uso de memória
ps aux | grep python | grep etl

# Verificar conexões de rede
netstat -an | grep :1433  # SQL Server
netstat -an | grep :5432  # PostgreSQL
netstat -an | grep :3306  # MySQL

# Verificar espaço em disco
df -h /opt/airflow
```

## 🔧 Operações de Manutenção

### 🔄 Reinicialização de Serviços

```bash
# Reiniciar Airflow completo
sudo systemctl restart airflow-webserver airflow-scheduler

# Reiniciar apenas scheduler
sudo systemctl restart airflow-scheduler

# Reiniciar apenas webserver
sudo systemctl restart airflow-webserver

# Verificar status
sudo systemctl status airflow-*
```

### 🧹 Limpeza de Logs

```bash
# Limpar logs antigos do Airflow (>30 dias)
find $AIRFLOW_HOME/logs -name "*.log" -mtime +30 -delete

# Limpar logs do ETL (>7 dias)
find /opt/airflow/dags/salesforce_integration/logs -name "*.log" -mtime +7 -delete

# Limpar arquivos CSV antigos (>3 dias)
find /opt/airflow/dags/salesforce_integration/carga_fria -name "*.csv" -mtime +3 -delete
```

### 📦 Backup de Configurações

```bash
# Backup das configurações
tar -czf /backup/etl-config-$(date +%Y%m%d).tar.gz \
    /opt/airflow/.env \
    /opt/airflow/airflow.cfg \
    /opt/airflow/dags/salesforce_integration/*.py

# Backup dos logs importantes
tar -czf /backup/etl-logs-$(date +%Y%m%d).tar.gz \
    /opt/airflow/dags/salesforce_integration/logs/
```

## 🚨 Procedimentos de Emergência

### ⚠️ Falha Total do Pipeline

1. **Verificar Status dos Serviços**
   ```bash
   sudo systemctl status airflow-*
   ```

2. **Verificar Logs de Erro**
   ```bash
   tail -100 /opt/airflow/dags/salesforce_integration/logs/etl_consolidated.log
   ```

3. **Reiniciar Serviços**
   ```bash
   sudo systemctl restart airflow-scheduler airflow-webserver
   ```

4. **Executar Teste Rápido**
   ```bash
   export SALESFORCE_OUTPUT_MODE=csv
   python etl_main.py --test-sample 100 --dry-run
   ```

### 🔌 Falha de Conexão

#### Banco de Dados
```bash
# Testar conexões
python -c "from database_connections import test_all_connections; test_all_connections()"

# Verificar conectividade de rede
ping ***********  # NewCon Batman
ping ***********  # NewCon Jocker
ping ***********  # DW Corporativo
ping ************ # Orbbits
```

#### Salesforce
```bash
# Testar conexão Salesforce
python -c "from salesforce_client import test_salesforce_connection; test_salesforce_connection()"

# Verificar credenciais
python -c "from salesforce_client import validate_salesforce_config; print(validate_salesforce_config())"
```

### 📊 Falha Parcial (Uma Tabela)

1. **Identificar Tabela com Problema**
   - Verificar logs do Airflow
   - Localizar tarefa com falha

2. **Executar Tabela Individualmente**
   ```bash
   # Exemplo para produtos
   python etl_main.py --tables produtos --dry-run
   ```

3. **Verificar Dados de Saída**
   ```bash
   # Verificar CSV gerado
   head -10 carga_fria/tb_produtos_*.csv
   wc -l carga_fria/tb_produtos_*.csv
   ```

## 📅 Rotinas de Operação

### 🌅 Rotina Diária

1. **Verificar Execução Automática**
   - Acessar Airflow UI
   - Verificar última execução
   - Confirmar sucesso de todas as tarefas

2. **Validar Volumes de Dados**
   ```bash
   # Verificar contadores nos logs
   grep "Total de registros" logs/etl_consolidated.log | tail -4
   ```

3. **Monitorar Performance**
   - Verificar tempos de execução
   - Identificar degradação de performance

### 📊 Rotina Semanal

1. **Análise de Logs**
   ```bash
   # Resumo de execuções da semana
   grep "Pipeline concluído" logs/etl_consolidated.log | tail -7
   ```

2. **Limpeza de Arquivos**
   ```bash
   # Executar limpeza de logs e CSVs antigos
   ./scripts/cleanup.sh
   ```

3. **Backup de Configurações**
   ```bash
   # Backup semanal
   ./scripts/backup.sh
   ```

### 🗓️ Rotina Mensal

1. **Revisão de Performance**
   - Analisar métricas de tempo
   - Identificar oportunidades de otimização

2. **Atualização de Dependências**
   ```bash
   # Verificar atualizações disponíveis
   pip list --outdated
   ```

3. **Teste de Disaster Recovery**
   - Simular falhas
   - Testar procedimentos de recuperação

## 📞 Contatos e Escalação

### Níveis de Suporte

1. **Nível 1 - Operação**
   - Monitoramento básico
   - Reinicialização de serviços
   - Execução manual

2. **Nível 2 - Técnico**
   - Análise de logs
   - Troubleshooting avançado
   - Ajustes de configuração

3. **Nível 3 - Desenvolvimento**
   - Modificações de código
   - Otimizações de performance
   - Novos recursos
   
---

**Próximos Passos**: Consulte o [Guia de Troubleshooting](TROUBLESHOOTING.md) para resolução de problemas específicos.
