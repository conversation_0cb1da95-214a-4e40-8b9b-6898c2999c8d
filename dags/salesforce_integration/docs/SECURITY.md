# 🔒 Documentação de Segurança e Boas Práticas - ETL Salesforce Marketing Cloud

## 📋 Visão Geral de Segurança

Este documento estabelece as diretrizes de segurança, boas práticas e recomendações para operação segura do sistema ETL em ambiente de produção.

## 🔐 Gerenciamento de Credenciais

### 🔑 Variáveis de Ambiente Sensíveis

#### Credenciais Obrigatórias
```bash
# NUNCA commitar estes valores no código
NEWCON_PASSWORD=senha_super_segura_aqui
DW_PASSWORD=senha_dw_corporativo
ORBBITS_PASSWORD=senha_orbbits_mysql
SALESFORCE_CLIENT_SECRET=client_secret_salesforce
RDSTATION_TOKEN=token_rd_station
```

#### Boas Práticas para Credenciais
1. **Usar Gerenciadores de Secrets**
   ```bash
   # AWS Secrets Manager
   aws secretsmanager get-secret-value --secret-id prod/etl/salesforce
   
   # Azure Key Vault
   az keyvault secret show --vault-name etl-vault --name salesforce-secret
   
   # HashiCorp Vault
   vault kv get secret/etl/salesforce
   ```

2. **Rotação Regular de Credenciais**
   - Senhas de banco: A cada 90 dias
   - Tokens de API: A cada 30 dias
   - Client Secrets: A cada 180 dias

3. **Princípio do Menor Privilégio**
   ```sql
   -- Exemplo para usuário NewCon (apenas SELECT)
   CREATE USER etl_user WITH PASSWORD 'senha_segura';
   GRANT SELECT ON schema.tabela TO etl_user;
   -- NÃO dar permissões de INSERT, UPDATE, DELETE
   ```

### 🛡️ Proteção de Credenciais no Sistema

#### Mascaramento em Logs
```python
# Sistema automaticamente mascara dados sensíveis
logger.info(f"Conectando com usuário: {mask_sensitive_data(username)}")
# Output: "Conectando com usuário: user***"

# Configuração de mascaramento
SECURITY_MASK_SENSITIVE_DATA=true
SECURITY_LOG_SENSITIVE_DATA=false
```

#### Criptografia de Credenciais
```python
# Sistema suporta criptografia de credenciais em repouso
SECURITY_ENCRYPT_CREDENTIALS=true

# Credenciais são automaticamente criptografadas antes do armazenamento
encrypted_password = encrypt_credential(password, master_key)
```

## 🔒 Proteção de Dados Pessoais (LGPD/GDPR)

### 📊 Dados Sensíveis Identificados

#### Dados Pessoais
- **CPF/CNPJ**: Identificadores únicos de pessoas físicas/jurídicas
- **Email**: Dados de contato pessoal
- **Telefone**: Dados de contato pessoal
- **Endereço**: Localização pessoal

#### Dados Financeiros
- **Valores de propostas**: Informações financeiras sensíveis
- **Dados de pagamento**: Informações bancárias
- **Histórico de vendas**: Padrões de consumo

### 🛡️ Medidas de Proteção Implementadas

#### Mascaramento Automático
```python
def mask_sensitive_data(data: Any, field_name: str = None) -> str:
    """
    Mascara dados sensíveis automaticamente:
    - CPF: 123.456.***-**
    - Email: user***@domain.com
    - Telefone: (11) 9****-****
    """
```

#### Auditoria de Acesso
```python
# Sistema mantém trilha de auditoria completa
SECURITY_AUDIT_TRAIL=true

# Logs incluem:
# - Quem acessou os dados
# - Quando foi acessado
# - Que dados foram processados
# - Resultado da operação
```

#### Minimização de Dados
- Apenas campos necessários são extraídos
- Dados são processados em lotes para reduzir exposição
- Retenção limitada de logs (7 dias por padrão)

## 🌐 Segurança de Rede

### 🔥 Configuração de Firewall

#### Portas Necessárias
```bash
# Entrada (apenas do servidor Airflow)
# Nenhuma porta de entrada necessária

# Saída (para APIs e bancos)
1433/tcp    # SQL Server (NewCon)
5432/tcp    # PostgreSQL (DW)
3306/tcp    # MySQL (Orbbits)
443/tcp     # HTTPS (Salesforce, RD Station)
```

#### Regras de Firewall Recomendadas
```bash
# Permitir apenas IPs específicos para bancos
iptables -A OUTPUT -d *********** -p tcp --dport 1433 -j ACCEPT  # NewCon Batman
iptables -A OUTPUT -d *********** -p tcp --dport 1433 -j ACCEPT  # NewCon Jocker
iptables -A OUTPUT -d *********** -p tcp --dport 5432 -j ACCEPT  # DW
iptables -A OUTPUT -d ************ -p tcp --dport 3306 -j ACCEPT # Orbbits

# Bloquear todo o resto
iptables -A OUTPUT -p tcp --dport 1433 -j REJECT
iptables -A OUTPUT -p tcp --dport 5432 -j REJECT
iptables -A OUTPUT -p tcp --dport 3306 -j REJECT
```

### 🔐 Conexões Seguras

#### SSL/TLS para Bancos
```python
# PostgreSQL com SSL
DW_SSL_MODE=require
DW_SSL_CERT=/path/to/client-cert.pem
DW_SSL_KEY=/path/to/client-key.pem
DW_SSL_CA=/path/to/ca-cert.pem

# SQL Server com criptografia
NEWCON_ENCRYPT=true
NEWCON_TRUST_SERVER_CERTIFICATE=false

# MySQL com SSL
ORBBITS_SSL_MODE=REQUIRED
ORBBITS_SSL_CA=/path/to/ca.pem
```

#### HTTPS para APIs
```python
# Todas as APIs usam HTTPS obrigatoriamente
SALESFORCE_AUTH_URI=https://...  # Sempre HTTPS
RDSTATION_BASE_URL=https://...   # Sempre HTTPS

# Verificação de certificados habilitada
VERIFY_SSL_CERTIFICATES=true
```

## 🏢 Segurança em Ambiente de Produção

### 👤 Controle de Acesso

#### Usuários e Permissões
```bash
# Usuário dedicado para ETL
sudo useradd -r -s /bin/false etl-user
sudo usermod -aG etl-group etl-user

# Permissões de arquivo
chmod 600 /opt/airflow/.env          # Apenas owner pode ler
chmod 750 /opt/airflow/dags/         # Owner e grupo podem executar
chown -R etl-user:etl-group /opt/airflow/
```

#### Isolamento de Processos
```bash
# Executar Airflow como usuário dedicado
sudo systemctl edit airflow-scheduler
[Service]
User=etl-user
Group=etl-group
PrivateTmp=true
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
```

### 🔍 Monitoramento de Segurança

#### Logs de Segurança
```python
# Eventos de segurança são logados separadamente
security_logger = logging.getLogger('security')

# Eventos monitorados:
# - Tentativas de autenticação
# - Acessos a dados sensíveis
# - Falhas de conexão
# - Mudanças de configuração
```

#### Alertas de Segurança
```bash
# Configurar alertas para eventos críticos
MONITORING_SECURITY_ALERTS=true
SECURITY_ALERT_EMAIL=<EMAIL>
SECURITY_WEBHOOK_URL=https://hooks.slack.com/security

# Eventos que geram alertas:
# - Falhas de autenticação repetidas
# - Acesso a dados fora do horário
# - Volumes anômalos de dados
# - Tentativas de acesso não autorizadas
```

## 🧪 Segurança em Ambientes de Teste

### 🔒 Isolamento de Dados

#### Dados de Teste Seguros
```python
# Usar dados sintéticos ou anonimizados
TEST_DATA_MODE=synthetic
ANONYMIZE_PERSONAL_DATA=true

# Marcar dados de teste explicitamente
from test_data_manager import mark_as_test_data

test_data = mark_as_test_data(
    data=sample_data,
    table_name='tb_clientes',
    description='Dados sintéticos para teste'
)
```

#### Ambientes Separados
```bash
# Configurações por ambiente
.env.production     # Dados reais, credenciais de produção
.env.staging        # Dados anonimizados, credenciais de teste
.env.development    # Dados sintéticos, credenciais locais

# Carregar configuração apropriada
source .env.${ENVIRONMENT}
```

### 🚫 Prevenção de Vazamentos

#### Validação de Ambiente
```python
def validate_environment_safety():
    """
    Valida se ambiente é seguro:
    - Verifica se não está usando dados de produção em teste
    - Confirma configurações de segurança
    - Valida credenciais apropriadas
    """
    if ENVIRONMENT == 'production' and TEST_MODE:
        raise SecurityError("Não é possível usar modo teste em produção")
```

## 📋 Checklist de Segurança

### 🔐 Credenciais e Autenticação
- [ ] Todas as senhas são complexas (12+ caracteres, mista)
- [ ] Credenciais armazenadas em gerenciador de secrets
- [ ] Rotação regular de credenciais implementada
- [ ] Princípio do menor privilégio aplicado
- [ ] Autenticação multifator habilitada onde possível

### 🛡️ Proteção de Dados
- [ ] Mascaramento de dados sensíveis em logs
- [ ] Criptografia de credenciais em repouso
- [ ] Trilha de auditoria completa habilitada
- [ ] Retenção de logs configurada adequadamente
- [ ] Dados pessoais identificados e protegidos

### 🌐 Segurança de Rede
- [ ] Firewall configurado com regras restritivas
- [ ] Conexões SSL/TLS habilitadas para todos os bancos
- [ ] HTTPS obrigatório para todas as APIs
- [ ] Verificação de certificados habilitada
- [ ] IPs de origem restritos

### 🏢 Ambiente de Produção
- [ ] Usuário dedicado para ETL criado
- [ ] Permissões de arquivo configuradas corretamente
- [ ] Isolamento de processos implementado
- [ ] Monitoramento de segurança ativo
- [ ] Alertas de segurança configurados

### 🧪 Ambientes de Teste
- [ ] Dados de teste sintéticos ou anonimizados
- [ ] Ambientes completamente separados
- [ ] Validação de segurança de ambiente
- [ ] Prevenção de vazamentos implementada
- [ ] Marcação explícita de dados de teste

## 🚨 Procedimentos de Incidente

### 🔍 Detecção de Incidentes

#### Indicadores de Comprometimento
- Falhas de autenticação repetidas
- Acessos fora do horário normal
- Volumes anômalos de dados processados
- Conexões de IPs não autorizados
- Alterações não autorizadas em configurações

#### Monitoramento Automático
```bash
# Script de monitoramento de segurança
#!/bin/bash
# security_monitor.sh

# Verificar tentativas de login falhadas
grep "Authentication failed" /var/log/auth.log | tail -10

# Verificar conexões suspeitas
netstat -an | grep ESTABLISHED | grep -v "10.201\|10.211\|10.204\|10.220"

# Verificar processos ETL anômalos
ps aux | grep python | grep etl | awk '{if($3>50) print "High CPU:", $0}'
```

### 🚨 Resposta a Incidentes

#### Procedimento de Emergência
1. **Isolamento Imediato**
   ```bash
   # Parar todos os serviços ETL
   sudo systemctl stop airflow-scheduler airflow-webserver
   
   # Bloquear conexões de rede
   sudo iptables -A OUTPUT -j REJECT
   ```

2. **Preservação de Evidências**
   ```bash
   # Backup de logs críticos
   tar -czf incident-$(date +%Y%m%d-%H%M%S).tar.gz \
       /var/log/auth.log \
       /opt/airflow/logs/ \
       dags/salesforce_integration/logs/
   ```

3. **Notificação**
   ```bash
   # Notificar equipe de segurança
   echo "INCIDENTE DE SEGURANÇA ETL - $(date)" | \
   mail -s "URGENTE: Incidente ETL" <EMAIL>
   ```

4. **Investigação**
   - Analisar logs de acesso
   - Verificar integridade dos dados
   - Identificar escopo do comprometimento
   - Documentar timeline do incidente

5. **Recuperação**
   - Corrigir vulnerabilidades identificadas
   - Restaurar serviços com configurações seguras
   - Implementar monitoramento adicional
   - Atualizar procedimentos de segurança

### 📞 Contatos de Emergência

#### Equipe de Resposta a Incidentes
- **CISO**: <EMAIL> / +55 11 9999-0001
- **Equipe de Segurança**: <EMAIL> / +55 11 9999-0002
- **Administrador de Sistema**: <EMAIL> / +55 11 9999-0003
- **Desenvolvedor Principal**: <EMAIL> / +55 11 9999-0004

#### Escalação
1. **Nível 1**: Operador detecta incidente
2. **Nível 2**: Administrador de sistema
3. **Nível 3**: Equipe de segurança
4. **Nível 4**: CISO e diretoria

## 📚 Recursos Adicionais

### 📖 Documentação de Referência
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [ISO 27001](https://www.iso.org/isoiec-27001-information-security.html)
- [LGPD - Lei Geral de Proteção de Dados](https://www.planalto.gov.br/ccivil_03/_ato2015-2018/2018/lei/l13709.htm)

### 🛠️ Ferramentas de Segurança Recomendadas
- **Gerenciamento de Secrets**: HashiCorp Vault, AWS Secrets Manager
- **Monitoramento**: ELK Stack, Splunk, Datadog
- **Análise de Vulnerabilidades**: Nessus, OpenVAS
- **Backup Seguro**: Veeam, Commvault

---

**Importante**: Esta documentação deve ser revisada trimestralmente e atualizada conforme mudanças no ambiente e ameaças de segurança.
