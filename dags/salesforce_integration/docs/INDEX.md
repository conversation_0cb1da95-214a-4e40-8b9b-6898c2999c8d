# 📚 Índice da Documentação - ETL Salesforce Marketing Cloud

## 🎯 Guia de Navegação

Esta documentação está organizada para atender diferentes perfis de usuários e cenários de uso. Use este índice para encontrar rapidamente a informação que você precisa.

## 👥 Por Perfil de Usuário

### 🔧 Administrador de Sistema
**Responsável por instalar, configurar e manter o ambiente**

1. **[Guia de Instalação](INSTALLATION.md)** - Setup completo do ambiente
2. **[Configurações](CONFIGURATION.md)** - Todas as configurações do sistema
3. **[Segurança](SECURITY.md)** - Boas práticas e segurança
4. **[Troubleshooting](TROUBLESHOOTING.md)** - Resolução de problemas

### 👨‍💼 Operador/Analista
**Responsável por executar e monitorar o pipeline**

1. **[Manual de Operação](OPERATION.md)** - Como executar e monitorar
2. **[Troubleshooting](TROUBLESHOOTING.md)** - Resolução de problemas comuns
3. **[README Principal](../README.md)** - Visão geral e quick start

### 👨‍💻 Desenvolvedor
**Responsável por manter e evoluir o código**

1. **[Módulos Técnicos](MODULES.md)** - Documentação detalhada dos módulos
2. **[Pipeline ETL](PIPELINE.md)** - Fluxo detalhado do pipeline
3. **[Configurações](CONFIGURATION.md)** - Configurações técnicas
4. **[Segurança](SECURITY.md)** - Aspectos de segurança no código

### 🔒 Equipe de Segurança
**Responsável por auditoria e compliance**

1. **[Segurança](SECURITY.md)** - Documentação completa de segurança
2. **[Configurações](CONFIGURATION.md)** - Configurações de segurança
3. **[Módulos Técnicos](MODULES.md)** - Aspectos técnicos de segurança

## 🎯 Por Cenário de Uso

### 🚀 Primeira Instalação
**Configurando o sistema pela primeira vez**

1. **[README Principal](../README.md)** - Visão geral do projeto
2. **[Guia de Instalação](INSTALLATION.md)** - Instalação passo a passo
3. **[Configurações](CONFIGURATION.md)** - Configuração das variáveis
4. **[Manual de Operação](OPERATION.md)** - Primeiro teste de execução

### 🔧 Manutenção Rotineira
**Operação diária do sistema**

1. **[Manual de Operação](OPERATION.md)** - Procedimentos operacionais
2. **[Troubleshooting](TROUBLESHOOTING.md)** - Problemas comuns
3. **[README Principal](../README.md)** - Comandos de emergência

### 🐛 Resolução de Problemas
**Sistema apresentando falhas**

1. **[Troubleshooting](TROUBLESHOOTING.md)** - Guia completo de problemas
2. **[Manual de Operação](OPERATION.md)** - Procedimentos de emergência
3. **[Configurações](CONFIGURATION.md)** - Verificação de configurações
4. **[Segurança](SECURITY.md)** - Procedimentos de incidente

### 🔄 Desenvolvimento e Evolução
**Modificando ou expandindo o sistema**

1. **[Módulos Técnicos](MODULES.md)** - Arquitetura e APIs
2. **[Pipeline ETL](PIPELINE.md)** - Fluxo detalhado
3. **[Configurações](CONFIGURATION.md)** - Configurações técnicas
4. **[Segurança](SECURITY.md)** - Boas práticas de desenvolvimento

### 🔒 Auditoria de Segurança
**Revisão de segurança e compliance**

1. **[Segurança](SECURITY.md)** - Documentação completa
2. **[Configurações](CONFIGURATION.md)** - Configurações de segurança
3. **[Módulos Técnicos](MODULES.md)** - Implementação de segurança

## 📋 Documentos por Categoria

### 📖 Documentação Geral
- **[README Principal](../README.md)** - Visão geral e introdução
- **[INDEX](INDEX.md)** - Este índice de navegação

### 🛠️ Instalação e Configuração
- **[Guia de Instalação](INSTALLATION.md)** - Setup completo do ambiente
- **[Configurações](CONFIGURATION.md)** - Todas as configurações do sistema

### 🎮 Operação e Monitoramento
- **[Manual de Operação](OPERATION.md)** - Como executar e monitorar
- **[Troubleshooting](TROUBLESHOOTING.md)** - Resolução de problemas

### 🧩 Documentação Técnica
- **[Pipeline ETL](PIPELINE.md)** - Fluxo detalhado do pipeline
- **[Módulos Técnicos](MODULES.md)** - Documentação detalhada dos módulos

### 🔒 Segurança e Compliance
- **[Segurança](SECURITY.md)** - Boas práticas e segurança

## 🔍 Busca Rápida por Tópico

### Configurações
- **Bancos de Dados**: [CONFIGURATION.md](CONFIGURATION.md#configurações-de-banco-de-dados)
- **Salesforce**: [CONFIGURATION.md](CONFIGURATION.md#configurações-salesforce-marketing-cloud)
- **RD Station**: [CONFIGURATION.md](CONFIGURATION.md#configurações-rd-station)
- **Variáveis de Ambiente**: [CONFIGURATION.md](CONFIGURATION.md#configurações-de-ambiente)

### Operação
- **Execução via Airflow**: [OPERATION.md](OPERATION.md#execução-via-airflow-recomendada-para-produção)
- **Execução Direta**: [OPERATION.md](OPERATION.md#execução-direta-via-python)
- **Modos de Operação**: [OPERATION.md](OPERATION.md#configuração-de-modos-de-operação)
- **Monitoramento**: [OPERATION.md](OPERATION.md#monitoramento-e-logs)

### Problemas Comuns
- **Falhas de Conexão**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md#falhas-de-conexão)
- **Problemas de Dados**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md#problemas-de-dados)
- **Performance**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md#problemas-de-performance)
- **Airflow**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md#problemas-do-airflow)

### Arquitetura Técnica
- **Extração de Dados**: [MODULES.md](MODULES.md#data_extractorspy---extração-de-dados)
- **Transformação**: [MODULES.md](MODULES.md#data_transformerspy---transformação-de-dados)
- **Cliente Salesforce**: [MODULES.md](MODULES.md#salesforce_clientpy---cliente-salesforce)
- **Pipeline Completo**: [PIPELINE.md](PIPELINE.md)

### Segurança
- **Credenciais**: [SECURITY.md](SECURITY.md#gerenciamento-de-credenciais)
- **Proteção de Dados**: [SECURITY.md](SECURITY.md#proteção-de-dados-pessoais-lgpdgdpr)
- **Rede**: [SECURITY.md](SECURITY.md#segurança-de-rede)
- **Incidentes**: [SECURITY.md](SECURITY.md#procedimentos-de-incidente)

## 📞 Contatos e Suporte

### Contatos
- **Desenvolvedor Principal**: Sevira Marcos
- **Equipe ETL**: <EMAIL>
- **Suporte Técnico**: <EMAIL>

### Escalação
1. **Nível 1**: Consultar documentação
2. **Nível 2**: Contatar suporte técnico
3. **Nível 3**: Escalar para desenvolvimento
4. **Nível 4**: Contatar equipe de segurança (se necessário)

## 🔄 Atualizações da Documentação

### Histórico de Versões
- **v1.0** (18/07/2025): Documentação inicial completa
- **v1.1** (TBD): Atualizações baseadas em feedback

### Como Contribuir
1. Identifique lacunas ou erros na documentação
2. Crie issue no sistema de controle de versão
3. Proponha melhorias ou correções
4. Submeta pull request com alterações

### Responsáveis pela Manutenção
- **Documentação Técnica**: Equipe de Desenvolvimento
- **Documentação Operacional**: Equipe de Operações
- **Documentação de Segurança**: Equipe de Segurança

---

**💡 Dica**: Use Ctrl+F (ou Cmd+F no Mac) para buscar termos específicos neste índice e encontrar rapidamente a documentação relevante.

**📱 Acesso Mobile**: Esta documentação é otimizada para visualização em dispositivos móveis para consulta em campo.

**🔖 Favoritos**: Marque as páginas mais utilizadas nos seus favoritos para acesso rápido.
