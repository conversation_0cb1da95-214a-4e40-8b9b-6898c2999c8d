# ETL Salesforce Marketing Cloud - Documentação Completa

## 📋 Visão Geral

Este projeto implementa um pipeline ETL (Extract, Transform, Load) ultra-otimizado para integração entre múltiplas fontes de dados e o Salesforce Marketing Cloud. O sistema foi projetado com arquitetura paralela para máxima performance e confiabilidade.

### 🎯 Objetivo Principal
Sincronizar dados de clientes, leads, produtos e propostas de diferentes sistemas (NewCon, RD Station, Orbbits) com o Salesforce Marketing Cloud através de Data Extensions.

### 🏗️ Arquitetura do Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   EXTRAÇÃO      │    │  TRANSFORMAÇÃO  │    │  CARREGAMENTO   │
│   (Paralela)    │───▶│   (Paralela)    │───▶│   (Paralelo)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
    ┌────▼────┐              ┌───▼───┐              ┌────▼────┐
    │9 Tabelas│              │4 Final│              │4 Data   │
    │Simultân.│              │Tables │              │Extensions│
    └─────────┘              └───────┘              └─────────┘
```

### 📊 Fontes de Dados

| Fonte | Tipo | Tabelas | Volume Estimado |
|-------|------|---------|-----------------|
| **NewCon** | SQL Server | 4 tabelas | ~630k registros |
| **RD Station** | API REST | 1 endpoint | Variável |
| **Orbbits** | MySQL | 4 tabelas | Complementar |

### 🎯 Destinos (Data Extensions)

| Data Extension | External Key | Campos Obrigatórios | Volume |
|----------------|--------------|-------------------|--------|
| **tb_produtos** | FCC1DCA7-D286-458D-BDCC-D050C1BA61A8 | id_produto | ~20k |
| **tb_clientes** | B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E | cnpjcpf, email | ~73k |
| **tb_leads** | EC0B7BFF-EC89-4A4D-914B-749F14B6F861 | cnpjcpf, dt_simulacao | Variável |
| **tb_propostas** | 36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA | idproposta, email | ~533k |

## 🚀 Características Principais

### ⚡ Performance Ultra-Otimizada
- **Paralelização Máxima**: 9 extrações + 4 transformações + 4 carregamentos simultâneos
- **Redução de Tempo**: 70% (de 60-90min para 20-30min)
- **Isolamento de Falhas**: Falha em uma tabela não afeta outras

### 🔄 Modos de Operação
- **Modo Salesforce**: Carrega dados diretamente no SFMC
- **Modo CSV**: Gera arquivos para revisão antes da carga
- **Modo Dry-Run**: Executa pipeline sem modificar dados
- **Modo Teste**: Limita volume para testes rápidos

### 🛡️ Confiabilidade
- **Retry Automático**: Tentativas com backoff exponencial
- **Rate Limiting**: Respeita limites das APIs
- **Failover**: Conexões secundárias para bancos críticos
- **Validação**: Verificação de integridade em cada etapa

### 📈 Monitoramento
- **Logs Estruturados**: Rastreamento detalhado de cada operação
- **Progress Tracking**: Barras de progresso em tempo real
- **Métricas**: Estatísticas de performance e volume
- **Alertas**: Notificações de falhas e anomalias

## 📁 Estrutura do Projeto

```
salesforce_integration/
├── 📄 README.md                          # Esta documentação
├── 📄 requirements.txt                    # Dependências Python
├── 📄 config.py                          # Configurações centralizadas
├── 📄 dag_etl_salesforce_table_parallel.py # DAG principal do Airflow
├── 📄 etl_main.py                        # Orquestrador principal
├── 📄 data_extractors.py                 # Extração de dados
├── 📄 data_transformers.py               # Transformação de dados
├── 📄 salesforce_client.py               # Cliente Salesforce
├── 📄 database_connections.py            # Gerenciador de conexões
├── 📄 utils.py                           # Utilitários compartilhados
├── 📄 test_data_manager.py               # Gerenciador de dados de teste
├── 📁 docs/                              # Documentação completa
│   ├── 📄 INSTALLATION.md                # Guia de instalação
│   ├── 📄 OPERATION.md                   # Manual de operação
│   ├── 📄 TROUBLESHOOTING.md             # Guia de problemas
│   ├── 📄 CONFIGURATION.md               # Configurações do sistema
│   ├── 📄 PIPELINE.md                    # Fluxo do pipeline ETL
│   ├── 📄 MODULES.md                     # Documentação técnica
│   └── 📄 SECURITY.md                    # Segurança e boas práticas
└── 📁 carga_fria/                        # Arquivos CSV de saída
    ├── tb_produtos_YYYYMMDD_HHMMSS.csv
    ├── tb_clientes_YYYYMMDD_HHMMSS.csv
    ├── tb_leads_YYYYMMDD_HHMMSS.csv
    └── tb_propostas_YYYYMMDD_HHMMSS.csv
```

## 🔧 Tecnologias Utilizadas

### Core
- **Python 3.8+**: Linguagem principal
- **Apache Airflow 2.5+**: Orquestração de workflows
- **Pandas**: Manipulação de dados
- **Requests**: Cliente HTTP para APIs

### Conectores de Banco
- **pymssql**: SQL Server (NewCon)
- **psycopg2**: PostgreSQL (DW Corporativo)
- **mysql-connector-python**: MySQL (Orbbits)

### APIs Externas
- **rdstation**: Cliente RD Station CRM
- **Salesforce Marketing Cloud REST API**: Integração SFMC

### Monitoramento
- **structlog**: Logs estruturados
- **psutil**: Métricas do sistema
- **tqdm**: Barras de progresso

## 🚦 Status do Projeto

| Componente | Status | Versão | Última Atualização |
|------------|--------|--------|-------------------|
| **Pipeline Principal** | ✅ Produção | 4.0 | 2025-01-17 |
| **Extração Paralela** | ✅ Otimizada | 3.2 | 2025-01-17 |
| **Transformação** | ✅ Validada | 2.8 | 2025-01-17 |
| **Cliente Salesforce** | ✅ Estável | 2.5 | 2025-01-17 |
| **Monitoramento** | ✅ Completo | 1.9 | 2025-01-17 |

## 📚 Documentação Adicional

- **[Guia de Instalação](docs/INSTALLATION.md)**: Setup completo do ambiente
- **[Manual de Operação](docs/OPERATION.md)**: Como executar e monitorar
- **[Troubleshooting](docs/TROUBLESHOOTING.md)**: Resolução de problemas
- **[Configurações](docs/CONFIGURATION.md)**: Todas as configurações do sistema
- **[Pipeline ETL](docs/PIPELINE.md)**: Fluxo detalhado do pipeline
- **[Módulos Técnicos](docs/MODULES.md)**: Documentação detalhada dos módulos
- **[Segurança](docs/SECURITY.md)**: Boas práticas e segurança

## 🚀 Quick Start

### 1. Instalação Rápida
```bash
# Clonar projeto
cd /opt/airflow/dags/salesforce_integration

# Instalar dependências
pip install -r requirements.txt

# Configurar variáveis de ambiente
cp .env.example .env
nano .env  # Editar com suas credenciais
```

### 2. Teste Rápido
```bash
# Teste com dados limitados em modo CSV
export SALESFORCE_OUTPUT_MODE=csv
python etl_main.py --test-sample 100 --dry-run

# Verificar arquivos gerados
ls -la carga_fria/
```

### 3. Execução via Airflow
```bash
# Ativar DAG
airflow dags unpause etl_salesforce_marketing_cloud_table_parallel

# Executar manualmente
airflow dags trigger etl_salesforce_marketing_cloud_table_parallel
```

## 🆘 Suporte de Emergência

### Contatos
- **Desenvolvedor Principal**: Sevira Marcos
- **Equipe ETL**: <EMAIL>
- **Suporte Técnico**: <EMAIL>

### Procedimentos de Emergência
1. **Falha Total**: Verificar logs em `logs/etl_consolidated.log`
2. **Falha Parcial**: Executar tabelas individualmente
3. **Problemas de Conexão**: Verificar status dos serviços
4. **Dados Corrompidos**: Usar modo CSV para validação

### Comandos de Emergência
```bash
# Parar pipeline
sudo systemctl stop airflow-scheduler airflow-webserver

# Verificar logs
tail -f logs/etl_consolidated.log

# Teste rápido de conectividade
python -c "from database_connections import test_all_connections; test_all_connections()"

# Executar tabela individual
python etl_main.py --tables produtos --dry-run
```

## 📊 Métricas de Performance

### Tempos Esperados (Modo Paralelo)
- **Extração**: 15-25 min (9 tabelas simultâneas)
- **Transformação**: 5-10 min (4 tabelas paralelas)
- **Carregamento**: 10-20 min (4 Data Extensions)
- **Total**: 30-55 min (~630k registros)

### Volumes por Tabela
- **tb_produtos**: ~20k registros (2-3 min)
- **tb_clientes**: ~73k registros (5-8 min)
- **tb_leads**: Variável (3-10 min)
- **tb_propostas**: ~533k registros (15-20 min)

---

**Última Atualização**: 18/07/2025  
**Versão da Documentação**: 1.0  
**Autor**: Laranjo
