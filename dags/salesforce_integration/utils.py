"""
ETL Consolidado - Utilitários Compartilhados
Funções auxiliares para logging, validações, formatação e operações comuns.
"""

import os
import re
import time
import json
import logging
import functools
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable, Union
from decimal import Decimal
from logging.handlers import RotatingFileHandler
import random

from salesforce_integration.config import LOGGING_CONFIG, RETRY_CONFIGS, SECURITY_CONFIG

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

def setup_logging(
    log_level: str = None,
    log_file: str = None,
    console_output: bool = None,
    json_format: bool = None
) -> logging.Logger:
    """Configura sistema de logging estruturado"""
    
    # Usa configurações do config.py se não especificado
    log_level = log_level or LOGGING_CONFIG['level']
    log_file = log_file or LOGGING_CONFIG['file_path']
    console_output = console_output if console_output is not None else LOGGING_CONFIG['console_output']
    json_format = json_format if json_format is not None else LOGGING_CONFIG['json_format']
    
    # Cria diretório de logs se não existir
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Configura logger principal
    logger = logging.getLogger('etl_consolidated')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Remove handlers existentes
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Formato de log
    if json_format:
        formatter = JsonFormatter()
    else:
        formatter = logging.Formatter(LOGGING_CONFIG['format'])
    
    # Handler para arquivo com rotação
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=LOGGING_CONFIG['max_file_size'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Handler para console
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

class JsonFormatter(logging.Formatter):
    """Formatter para logs em JSON"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Adiciona informações extras se disponíveis
        if hasattr(record, 'extra'):
            log_entry.update(record.extra)
        
        return json.dumps(log_entry)

# =============================================================================
# DECORADORES DE RETRY
# =============================================================================

def retry_decorator(
    max_attempts: int = None,
    delay: float = None,
    exponential_backoff: bool = True,
    exceptions: tuple = (Exception,),
    on_retry: Optional[Callable] = None
):
    """Decorator para retry automático com backoff exponencial"""
    
    max_attempts = max_attempts or RETRY_CONFIGS['max_attempts']
    delay = delay or RETRY_CONFIGS['base_delay']
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt < max_attempts - 1:
                        # Calcula delay com backoff exponencial
                        current_delay = delay
                        if exponential_backoff:
                            current_delay = delay * (RETRY_CONFIGS['exponential_base'] ** attempt)
                        
                        # Adiciona jitter se habilitado
                        if RETRY_CONFIGS['jitter']:
                            current_delay += random.uniform(0, current_delay * 0.1)
                        
                        # Limita delay máximo
                        current_delay = min(current_delay, RETRY_CONFIGS['max_delay'])
                        
                        if on_retry:
                            on_retry(attempt + 1, max_attempts, current_delay, e)
                        
                        time.sleep(current_delay)
                    else:
                        logging.error(f"Função {func.__name__} falhou após {max_attempts} tentativas: {e}")
            
            raise last_exception
        
        return wrapper
    return decorator

# =============================================================================
# VALIDAÇÕES DE DADOS
# =============================================================================

def validate_email(email: str) -> bool:
    """Valida formato de email"""
    if not email or not isinstance(email, str):
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email.strip()) is not None

def validate_cpf(cpf: str) -> bool:
    """Valida CPF brasileiro"""
    if not cpf or not isinstance(cpf, str):
        return False
    
    # Remove formatação
    cpf = re.sub(r'[^\d]', '', cpf)
    
    # Verifica se tem 11 dígitos
    if len(cpf) != 11:
        return False
    
    # Verifica se não são todos dígitos iguais
    if cpf == cpf[0] * 11:
        return False
    
    # Validação dos dígitos verificadores
    def calculate_digit(cpf_digits, weight):
        total = sum(int(digit) * weight for digit, weight in zip(cpf_digits, weight))
        remainder = total % 11
        return 0 if remainder < 2 else 11 - remainder
    
    # Primeiro dígito
    first_weights = range(10, 1, -1)
    first_digit = calculate_digit(cpf[:9], first_weights)
    
    # Segundo dígito
    second_weights = range(11, 1, -1)
    second_digit = calculate_digit(cpf[:10], second_weights)
    
    return cpf[9] == str(first_digit) and cpf[10] == str(second_digit)

def validate_cnpj(cnpj: str) -> bool:
    """Valida CNPJ brasileiro"""
    if not cnpj or not isinstance(cnpj, str):
        return False
    
    # Remove formatação
    cnpj = re.sub(r'[^\d]', '', cnpj)
    
    # Verifica se tem 14 dígitos
    if len(cnpj) != 14:
        return False
    
    # Verifica se não são todos dígitos iguais
    if cnpj == cnpj[0] * 14:
        return False
    
    # Validação dos dígitos verificadores
    def calculate_digit(cnpj_digits, weights):
        total = sum(int(digit) * weight for digit, weight in zip(cnpj_digits, weights))
        remainder = total % 11
        return 0 if remainder < 2 else 11 - remainder
    
    # Primeiro dígito
    first_weights = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
    first_digit = calculate_digit(cnpj[:12], first_weights)
    
    # Segundo dígito
    second_weights = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
    second_digit = calculate_digit(cnpj[:13], second_weights)
    
    return cnpj[12] == str(first_digit) and cnpj[13] == str(second_digit)

def validate_phone(phone: str) -> bool:
    """Valida número de telefone brasileiro"""
    if not phone or not isinstance(phone, str):
        return False
    
    # Remove formatação
    phone = re.sub(r'[^\d]', '', phone)
    
    # Verifica se tem 10 ou 11 dígitos (com ou sem celular)
    if len(phone) not in [10, 11]:
        return False
    
    # Verifica se não começa com 0
    if phone[0] == '0':
        return False
    
    # Para números de 11 dígitos, o terceiro dígito deve ser 9 (celular)
    if len(phone) == 11 and phone[2] != '9':
        return False
    
    return True

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> tuple:
    """Valida se campos obrigatórios estão presentes e não vazios"""
    missing_fields = []
    
    for field in required_fields:
        if field not in data or not data[field] or str(data[field]).strip() == '':
            missing_fields.append(field)
    
    is_valid = len(missing_fields) == 0
    return is_valid, missing_fields

# =============================================================================
# FORMATAÇÃO DE DADOS
# =============================================================================

def format_cpf(cpf: str) -> str:
    """Formata CPF com máscara XXX.XXX.XXX-XX"""
    if not cpf:
        return ''
    
    # Remove formatação existente
    cpf = re.sub(r'[^\d]', '', cpf)
    
    # Aplica máscara se válido
    if len(cpf) == 11:
        return f"{cpf[:3]}.{cpf[3:6]}.{cpf[6:9]}-{cpf[9:]}"
    
    return cpf

def format_cnpj(cnpj: str) -> str:
    """Formata CNPJ com máscara XX.XXX.XXX/XXXX-XX"""
    if not cnpj:
        return ''
    
    # Remove formatação existente
    cnpj = re.sub(r'[^\d]', '', cnpj)
    
    # Aplica máscara se válido
    if len(cnpj) == 14:
        return f"{cnpj[:2]}.{cnpj[2:5]}.{cnpj[5:8]}/{cnpj[8:12]}-{cnpj[12:]}"
    
    return cnpj

def format_phone(phone: str) -> str:
    """Formata telefone com máscara (XX) XXXXX-XXXX ou (XX) XXXX-XXXX"""
    if not phone:
        return ''
    
    # Remove formatação existente
    phone = re.sub(r'[^\d]', '', phone)
    
    # Aplica máscara baseada no tamanho
    if len(phone) == 11:  # Celular
        return f"({phone[:2]}) {phone[2:7]}-{phone[7:]}"
    elif len(phone) == 10:  # Fixo
        return f"({phone[:2]}) {phone[2:6]}-{phone[6:]}"
    
    return phone

def format_date_salesforce(date_value: Union[str, datetime]) -> str:
    """Formata data para o padrão Salesforce: MM/DD/YYYY HH:MM:SS AM/PM"""
    if not date_value:
        return ''
    
    # Converte string para datetime se necessário
    if isinstance(date_value, str):
        # Tenta diferentes formatos de entrada
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d',
            '%d/%m/%Y %H:%M:%S',
            '%d/%m/%Y',
            '%Y-%m-%d %H:%M:%S.%f'
        ]
        
        for fmt in formats:
            try:
                date_value = datetime.strptime(date_value, fmt)
                break
            except ValueError:
                continue
        else:
            return ''  # Nenhum formato funcionou
    
    # Formata para o padrão Salesforce
    return date_value.strftime('%m/%d/%Y %I:%M:%S %p')

def format_currency(value: Union[str, float, Decimal]) -> str:
    """Formata valor monetário brasileiro"""
    if not value:
        return 'R$ 0,00'
    
    # Converte para float se necessário
    if isinstance(value, str):
        value = float(value.replace(',', '.'))
    
    return f"R$ {value:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')

def sanitize_string(text: str, max_length: int = None) -> str:
    """Remove caracteres especiais e limita tamanho"""
    if not text:
        return ''
    
    # Remove caracteres especiais
    text = re.sub(r'[^\w\s@._-]', '', str(text))
    
    # Remove espaços extras
    text = ' '.join(text.split())
    
    # Limita tamanho se especificado
    if max_length and len(text) > max_length:
        text = text[:max_length].strip()
    
    return text

def mask_sensitive_data(text: str, mask_char: str = '*') -> str:
    """Mascara dados sensíveis para logs"""
    if not text or not SECURITY_CONFIG['mask_sensitive_data']:
        return text
    
    text = str(text)
    
    # Mascara CPF
    text = re.sub(r'(\d{3})(\d{3})(\d{3})(\d{2})', r'\1.***.\3-**', text)
    
    # Mascara CNPJ
    text = re.sub(r'(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})', r'\1.***.***/**\4-\5', text)
    
    # Mascara email
    text = re.sub(r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', r'\1****@\2', text)
    
    # Mascara telefone
    text = re.sub(r'(\(\d{2}\)\s?)(\d{4,5})(\d{4})', r'\1***\3', text)
    
    return text

# =============================================================================
# CÁLCULOS E MÉTRICAS
# =============================================================================

def calculate_progress(current: int, total: int) -> Dict[str, Any]:
    """Calcula progresso e métricas"""
    if total == 0:
        return {
            'percentage': 0,
            'current': current,
            'total': total,
            'remaining': 0,
            'progress_bar': '[]'
        }
    
    percentage = (current / total) * 100
    remaining = total - current
    
    # Barra de progresso simples
    bar_length = 20
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    progress_bar = f'[{bar}] {percentage:.1f}%'
    
    return {
        'percentage': round(percentage, 2),
        'current': current,
        'total': total,
        'remaining': remaining,
        'progress_bar': progress_bar
    }

def calculate_eta(start_time: datetime, current: int, total: int) -> Dict[str, Any]:
    """Calcula tempo estimado de conclusão"""
    if current == 0 or total == 0:
        return {
            'eta_seconds': 0,
            'eta_formatted': '00:00:00',
            'elapsed_seconds': 0,
            'elapsed_formatted': '00:00:00'
        }
    
    elapsed_time = datetime.now() - start_time
    elapsed_seconds = elapsed_time.total_seconds()
    
    # Calcula ETA baseado no progresso atual
    rate = current / elapsed_seconds
    remaining_items = total - current
    eta_seconds = remaining_items / rate if rate > 0 else 0
    
    # Formata tempos
    elapsed_formatted = format_duration(elapsed_seconds)
    eta_formatted = format_duration(eta_seconds)
    
    return {
        'eta_seconds': int(eta_seconds),
        'eta_formatted': eta_formatted,
        'elapsed_seconds': int(elapsed_seconds),
        'elapsed_formatted': elapsed_formatted
    }

def format_duration(seconds: float) -> str:
    """Formata duração em segundos para HH:MM:SS"""
    if seconds < 0:
        return '00:00:00'
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

def calculate_batch_size(total_records: int, max_batch_size: int = 2000) -> Dict[str, int]:
    """Calcula distribuição de lotes"""
    if total_records == 0:
        return {
            'batch_size': max_batch_size,
            'total_batches': 0,
            'records_per_batch': 0,
            'last_batch_size': 0
        }
    
    batch_size = min(max_batch_size, total_records)
    total_batches = (total_records + batch_size - 1) // batch_size
    last_batch_size = total_records % batch_size or batch_size
    
    return {
        'batch_size': batch_size,
        'total_batches': total_batches,
        'records_per_batch': batch_size,
        'last_batch_size': last_batch_size
    }

# =============================================================================
# UTILITÁRIOS DIVERSOS
# =============================================================================

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Divide lista em chunks menores"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def flatten_dict(d: Dict[str, Any], separator: str = '.') -> Dict[str, Any]:
    """Aplaina dicionário aninhado"""
    def _flatten(obj, parent_key=''):
        items = []
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_key = f"{parent_key}{separator}{key}" if parent_key else key
                items.extend(_flatten(value, new_key).items())
        else:
            return {parent_key: obj}
        return dict(items)
    
    return _flatten(d)

def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Merge profundo de dicionários"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result

def safe_cast(value: Any, target_type: type, default: Any = None) -> Any:
    """Conversão segura de tipos"""
    try:
        if target_type == bool:
            if isinstance(value, str):
                return value.lower() in ('true', '1', 'yes', 'on')
            return bool(value)
        return target_type(value)
    except (ValueError, TypeError):
        return default

def get_memory_usage() -> Dict[str, float]:
    """Obtém uso de memória atual"""
    import psutil
    
    process = psutil.Process()
    memory_info = process.memory_info()
    
    return {
        'rss_mb': memory_info.rss / 1024 / 1024,
        'vms_mb': memory_info.vms / 1024 / 1024,
        'percent': process.memory_percent()
    }

def create_temp_file(content: str, suffix: str = '.tmp') -> str:
    """Cria arquivo temporário"""
    import tempfile
    
    with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
        f.write(content)
        return f.name

# =============================================================================
# CLASSES AUXILIARES
# =============================================================================

class ProgressTracker:
    """Classe para rastrear progresso de operações"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = datetime.now()
        self.logger = logging.getLogger(__name__)
    
    def update(self, increment: int = 1):
        """Atualiza progresso"""
        self.current += increment
        
        if self.current <= self.total:
            progress = calculate_progress(self.current, self.total)
            eta = calculate_eta(self.start_time, self.current, self.total)
            
            self.logger.info(
                f"{self.description}: {progress['progress_bar']} "
                f"({self.current}/{self.total}) "
                f"ETA: {eta['eta_formatted']}"
            )
    
    def finish(self):
        """Finaliza rastreamento"""
        elapsed = datetime.now() - self.start_time
        self.logger.info(
            f"{self.description} concluído em {format_duration(elapsed.total_seconds())}"
        )

class DataQualityChecker:
    """Classe para verificar qualidade de dados"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.issues = []
    
    def check_required_fields(self, data: List[Dict[str, Any]], required_fields: List[str]) -> tuple:
        """Verifica campos obrigatórios"""
        all_valid = True
        all_missing = []

        for i, record in enumerate(data):
            is_valid, missing = validate_required_fields(record, required_fields)
            if not is_valid:
                all_valid = False
                all_missing.extend(missing)
                self.issues.append(f"Registro {i}: campos obrigatórios ausentes: {missing}")

        return all_valid, all_missing
    
    def check_data_types(self, data: List[Dict[str, Any]], type_mapping: Dict[str, type]):
        """Verifica tipos de dados"""
        for i, record in enumerate(data):
            for field, expected_type in type_mapping.items():
                if field in record and not isinstance(record[field], expected_type):
                    self.issues.append(f"Registro {i}: campo '{field}' deve ser {expected_type.__name__}")
    
    def check_duplicates(self, data: List[Dict[str, Any]], unique_fields: List[str]):
        """Verifica duplicatas"""
        seen = set()
        for i, record in enumerate(data):
            key = tuple(str(record.get(field, '')) for field in unique_fields)
            if key in seen:
                self.issues.append(f"Registro {i}: duplicata encontrada nos campos {unique_fields}")
            seen.add(key)
    
    def get_quality_report(self) -> Dict[str, Any]:
        """Gera relatório de qualidade"""
        return {
            'total_issues': len(self.issues),
            'issues': self.issues,
            'quality_score': max(0, 100 - len(self.issues))
        }

# =============================================================================
# INICIALIZAÇÃO
# =============================================================================

# Configura logging padrão
logger = setup_logging()

# TODO: AGENTE_2 usar estas funções para validação de dados extraídos
# TODO: AGENTE_3 usar formatadores para transformações
# TODO: AGENTE_4 usar progress tracker para monitoramento de carregamento

# =============================================================================
# MAIN PARA TESTES
# =============================================================================

if __name__ == "__main__":
    # Testa utilitários
    print("=== TESTE DE UTILITÁRIOS ===")
    
    # Teste de validações
    print("\n1. Validações:")
    print(f"CPF válido: {validate_cpf('123.456.789-09')}")
    print(f"Email válido: {validate_email('<EMAIL>')}")
    print(f"Telefone válido: {validate_phone('(11) 99999-9999')}")
    
    # Teste de formatações
    print("\n2. Formatações:")
    print(f"CPF formatado: {format_cpf('12345678909')}")
    print(f"Data SF: {format_date_salesforce('2025-01-17 10:30:00')}")
    print(f"Moeda: {format_currency(1234.56)}")
    
    # Teste de progresso
    print("\n3. Progresso:")
    progress = calculate_progress(75, 100)
    print(f"Progresso: {progress['progress_bar']}")
    
    # Teste de qualidade
    print("\n4. Qualidade de dados:")
    test_data = [
        {'nome': 'João', 'email': '<EMAIL>'},
        {'nome': 'Maria', 'email': 'invalid-email'},
        {'nome': '', 'email': '<EMAIL>'}
    ]
    
    checker = DataQualityChecker()
    checker.check_required_fields(test_data, ['nome', 'email'])
    report = checker.get_quality_report()
    print(f"Relatório de qualidade: {report}")
    
    print("\n✅ Todos os testes concluídos!")