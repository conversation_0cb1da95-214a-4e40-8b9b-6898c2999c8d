
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

instances = [
    Bronze('newcon').add_tables([
        'conpv004'
    ])
    # Silver('propostas').sys('newcon')
    # .query(
    #     Query.Direct_Queries.newcon_propostas
    # ),
    # Custom( func = ETL_marketshare,
    #     times_per_day = 1
    # )
    
    # Silver('nbs_seminovos').query(
    #     Query.Bronze_to_silver.select_silver_nbs_seminovos
    # )
    # Silver('seminovos').sys('dealer')
    # .query(
    #     Query.Direct_Queries.dealer_seminovos
    # ),
    # Gold("faturamento_seminovos")
    # .query(
    #     Query.Gold.faturamento_seminovos
    # ),
    # Silver('fiscal_iss_retido').sys('dealer')
    # .query(
    #     Query.Direct_Queries.dealer_fiscal_iss_retido
    # ),
    # Silver('contabil_iss_retido').sys('dealer')
    # .query(
    #     Query.Direct_Queries.dealer_contabil_iss_retido
    # ),
    # Gold("consorcio_producao")
    # .query(
    #     Query.Gold.consorcio_producao
    # )     
    # Planilha('dealer_prefeitura_iss_retido.xlsx')
    # .file_id('3324782a-825a-44ad-8864-6b3c5ac32e46')
    # .tab().kwargs(
    #     table_name='dealer_prefeitura_iss_retido'
    # ), 
    # Gold("faturamento_seminovos")
    # .query(
    #     Query.Gold.faturamento_seminovos
    # ),  
    # Custom(func= process_mefepm,
    #        times_per_day = 1
    # ),
]
 

list = []


for obj in instances:
    list.append(obj.build())

for obj in ensure_flat_list(list):
    obj.execute()