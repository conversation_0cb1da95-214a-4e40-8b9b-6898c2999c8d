from Auxiliares_ETL_v2.functions import *

class Etl():
    def __init__(self, **kwargs):
        self._times_per_day = kwargs.get("_times_per_day", times_per_day_default)
        self._column_types = kwargs.get("_column_types", {})
        self._sys = kwargs.get("_sys", "dw_corporativo")
        self.obj_name = kwargs.get("obj_name").lower()
        self.schema = SCHEMA_CRIACAO_INSERT
        self._type = kwargs.get("_type")
        self.func = kwargs.get("func")
        self.id_columns = kwargs.get("id_columns")
        self.config = kwargs

    def choose_path(self):
        self.spark = False
        self.exec_type = False
        self.first_run = False

        if self._type in ("bronze", "silver", "gold", "view") and executing_script != "LocalRun.py" and not self.id_columns:

            # Obtém o contexto da execução
            context = get_current_context()
            
            # Obtém a instância da tarefa (ti)
            ti = context["ti"]

            # Puxa os dados armazenados no XCom pela task anterior
            data = ti.xcom_pull(task_ids="get_aux_tables")

            if data:
                types = []
                for row in data:
                    schema, tabela, tempo_execucao, type = row  # Ajuste os índices conforme sua tabela
                    if schema == self.schema and tabela == self.obj_name:
                        if type:
                            types.append((type, tempo_execucao))

                if len(types) == 0:
                    self.exec_type = "pandas"
                    self.first_run = True

                if len(types) == 1:
                    self.spark = True
                    self.exec_type = "spark"

                elif len(types) == 2:
                    exec_type, _ = min(types, key=lambda x: x[1])
                    if exec_type  == "spark":
                        self.spark = True
            else:
                self.exec_type = "pandas"
                self.first_run = True

    def extract_data(self):
        self.origin_conn = get_conn(self)

        self._query = self.config.get("_query")
        
        if self.first_run:
            self._structure_check = "empty"
        else:
            # Compara a estrutura das tabelas de destino e origem, para definir se irá recriar a tabela no destino, ou irá para o ETL, caso haja incompatibilidade
            compare_data_structure(self)

        # Verifica se a estrutura da tabela se manteve para a tabela bronze com a opção de atualização incremental
        if self._type == "bronze" and self.id_columns and self._structure_check == "equal":
            # Cria a consulta para atualização incremental, baseado nas colunas de data e id passados
            set_incremental_query(self)
            
        # Cria o objeto que contém os dados em chunks, para posteriormente iterá-lo
        self.chunk_iterator = fetch_data_in_chunks(self)

        return self

    def prepare_to_load(self):

        # Adquire o primeiro chunk para fins de tratamento dos dados e/ou preparação da tabela destino
        self.data = next(self.chunk_iterator)

        # Faz processos de tranformação dos dados, caso existam
        transform_data(self)

        # Caso não passe pelo check de estrutura de tabela, faz a preparação da tabela e/ou interrompe o processo do ETL
        if self._structure_check != "equal":
            prepare_table(self)
            
        # Caso passe pelo check de estrutura de tabela, deleta os registros necessários para casos de atualização incremental ou congelamento de dados
        if self._structure_check == "equal" and not self.data.empty:
            delete_from_destin(self)

        return self

    def load_data(self):

        # Insere o primeiro chunk, utilizado para fins específicos, na tabela de destino
        insert_data_in_chunks(self, self.data)   

        for self.data in self.chunk_iterator:
            # Faz o processo de transformação dos dados para cada chunk
            transform_data(self)

            # Insere os dados do chunk no dw corporativo
            insert_data_in_chunks(self, self.data)    

        return self
    
    def monitor_data_quality(self):
        if self._type != "custom":
            self.check_data_quality = None
            self.merge_type = None

            self.row_count_destin = pd.read_sql_query(
                f'SELECT COUNT(*) FROM {self.schema}.{self.obj_name}', 
                self.target_conn
            ).iloc[0, 0]
            
            self.row_count_destin = int(self.row_count_destin) if self.row_count_destin else None

        if self._type == "bronze" and not self.config.get("custom_query") and not self.spark:
            self.row_count_origin = pd.read_sql_query(
                f'select count(*) from ({self.config.get("_query")}) subquery',
                self.origin_conn
            ).iloc[0, 0]
            # Convertendo para tipos nativos do Python
            self.row_count_origin = int(self.row_count_origin) if self.row_count_origin else None
            self.dif_origem = self.row_count_origin - self.row_count_destin if self.row_count_origin and self.row_count_destin else None
            self.merge_type = "date_merge" if self.config.get("date_columns") and self.id_columns else \
                            "id_merge" if self.id_columns else None

            # Verificação de qualidade dos dados
            if self.row_count_origin and self.row_count_destin:
                # Calcula a diferença em porcentagem
                diff_percentage = abs(self.dif_origem) / self.row_count_origin * 100
                if diff_percentage > 2:
                    error_message = (
                        f"Data quality check failed: difference between origin and destination is {diff_percentage:.2f}%, "
                        f"which exceeds the 2% threshold. Origin: {self.row_count_origin}, Destination: {self.row_count_destin}."
                    )
                    self.check_data_quality = 'Failed'
                    raise ValueError(error_message)
                else:
                    self.check_data_quality = 'Passed'

            # Log do sucesso do monitoramento
            logger.info(f"Data quality check passed for {self.schema}.{self.obj_name}. Origin: {self.row_count_origin}, Destination: {self.row_count_destin}.")

    def monitor_task_data(self, context):
        """Registra informações de execução das tasks no banco de dados PostgreSQL."""
        if self._type != "custom":
            log_conn = psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST)
            
            task_instance = context['task_instance']
            start_time = task_instance.start_date
            end_time = task_instance.end_date
            start_time = make_aware(start_time, timezone=tz) if start_time and is_naive(start_time) else start_time
            end_time = make_aware(end_time, timezone=tz) if end_time and is_naive(end_time) else end_time

            # Definir o período com base na hora
            hora = start_time.hour if start_time else None
            periodo = definir_periodo(hora)

            duration = (end_time - start_time).total_seconds() if end_time and start_time else None
            state = task_instance.state
        
        if self.exec_type and state == 'success':
            logger.info(f"Callback de tipoExeucaco chamado para {context['task_instance'].task_id}")
            # Conecta ao banco de dados e insere o log
            with log_conn as conn:
                with conn.cursor() as cursor:
                    query = """
                        INSERT INTO dbdwcorporativo.aux_tipoExecucao (
                            schema, nome_tabela, tempo_execucao, tipo_execucao
                        )
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (schema, nome_tabela, tipo_execucao) DO NOTHING;
                    """
                    cursor.execute(query, (self.schema, self.obj_name, 
                                        duration, self.exec_type
                                        ))
                    conn.commit()

        if self._type != "custom":
            print(f"Callback de log chamado para {context['task_instance'].task_id}")

            # Pegando informações da task instance
            dag_id = task_instance.dag_id
            task_id = task_instance.task_id
            execution_date = task_instance.execution_date

            # Verifica se as datas são timezone-aware
            tz = pytz.timezone("America/Sao_Paulo")
            execution_date = make_aware(execution_date, timezone=tz) if is_naive(execution_date) else execution_date
            try_number = task_instance.try_number
            host = task_instance.hostname

            # Conecta ao banco de dados e insere o log
            with log_conn as conn:
                with conn.cursor() as cursor:
                    query = """
                        INSERT INTO dbdwcorporativo.task_logs (
                            dag_id, task_id, execution_date, start_time, end_time, 
                            duration_seconds, state, try_number, host, 
                            "schema", "table", row_count, check_data_quality, merge_type, periodo
                        )
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (task_id, execution_date) DO NOTHING;
                    """
                    cursor.execute(query, (dag_id, task_id, execution_date, start_time, 
                                            end_time, duration, state, try_number, host, 
                                            self.schema, self.obj_name, self.row_count_destin,
                                            self.check_data_quality, self.merge_type, periodo
                                        ))
                    conn.commit()
        
        if self._type != "custom":
            log_conn.close()

    def spark_etl(self):
        spark = SparkSession.builder \
            .master("local") \
            .appName("shared_session_etl") \
            .config("spark.driver.memory", "2g") \
            .config("spark.executor.memory", "2g") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.cleaner.periodicGC.interval", "5min") \
            .config("spark.local.dir", "/tmp/spark-temp") \
            .config("spark.jars.packages", (
                "org.postgresql:postgresql:42.2.19," + 
                "com.microsoft.sqlserver:mssql-jdbc:8.4.1.jre8," +
                "com.oracle.database.jdbc:ojdbc8:19.8.0.0"
            )) \
            .getOrCreate()

        # Database configurations
        jdbc_url, driver, user, password = get_jdbc_url(self._sys)
        db_properties = {
            "user": user,
            "password": password,
            "driver": driver
        }

        df_postgres = spark.read \
            .format("jdbc") \
            .option("url", jdbc_url) \
            .option("query", self.config.get("_query")) \
            .option("user", db_properties["user"]) \
            .option("password", db_properties["password"]) \
            .option("driver", db_properties["driver"]) \
            .load()
        
        df_postgres = df_postgres.toDF(*[normalize_column_name(col) for col in df_postgres.columns])

        jdbc_url = f"jdbc:postgresql://{DW_CORPORATIVO_HOST}:{DW_CORPORATIVO_PORT}/{DW_CORPORATIVO_DBNAME}"
        db_properties = {
            "user": DW_CORPORATIVO_USERNAME,
            "password": DW_CORPORATIVO_PASSWORD,
            "driver": "org.postgresql.Driver"
        }
        
        table_name = f'{self.schema}.{self.obj_name}'

        df_postgres.write.format("jdbc") \
            .option("url", jdbc_url) \
            .option("dbtable", f"{table_name}_staging") \
            .option("user", db_properties["user"]) \
            .option("password", db_properties["password"]) \
            .option("driver", db_properties["driver"]) \
            .mode("overwrite") \
            .save()
        
        grant_query = ""

        # Get permissions from aux_permissions_tables
        self.cur.execute(f"""
            SELECT username, table_name, grant_type, active 
            FROM dbdwcorporativo.aux_permissions_tables 
            WHERE (table_name = '*' OR lower('{table_name}') LIKE '%' || lower(table_name) || '%') 
            AND active = true
        """)
        permissions = self.cur.fetchall()
        
        # Add permissions to grant_query
        for user, table, permission_type, active in permissions:
            grant_query += f"{permission_type} ON TABLE {table_name} TO {user};\n"

        logger.info(f"Giving permissions to users: {grant_query}")
        self.cur.execute(f"DROP TABLE IF EXISTS {table_name};")
        self.cur.execute(f"ALTER TABLE {f'{table_name}_staging'} RENAME TO {self.obj_name};")
        self.cur.execute(grant_query)
        logger.info(f"Completed ETL task for object: {self.schema}.{self.obj_name}")

        return self

    def execute(self):
        self.choose_path()
        self.target_conn = psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST)
        self.row_count_etl = 0
        self.chunksize = 50000
        try:
            with self.target_conn:
                with self.target_conn.cursor() as self.cur:
                    logger.info("Removing idle connections and blocking locks...")
                    try:
                        self.cur.execute("""
                                SELECT pg_terminate_backend(pid)
                                FROM pg_stat_activity
                                WHERE (state = 'idle' AND now() - state_change > interval '2 minutes') or
                                now() - query_start > interval '1 hour'
                            """)
                        # self.cur.execute("""
                        #         SELECT 
                        #             pg_terminate_backend(blocking_locks.pid) AS blocking_pid
                        #         FROM 
                        #             pg_catalog.pg_locks blocked_locks
                        #         JOIN 
                        #             pg_catalog.pg_stat_activity blocked_activity ON blocked_locks.pid = blocked_activity.pid
                        #         JOIN 
                        #             pg_catalog.pg_locks blocking_locks ON (
                        #                 blocking_locks.locktype = blocked_locks.locktype
                        #                 AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
                        #                 AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
                        #                 AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
                        #                 AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
                        #                 AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
                        #                 AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
                        #                 AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
                        #                 AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
                        #                 AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
                        #                 AND blocking_locks.pid != blocked_locks.pid
                        #             )
                        #         JOIN 
                        #             pg_catalog.pg_stat_activity blocking_activity ON blocking_locks.pid = blocking_activity.pid
                        #         WHERE 
                        #             NOT blocked_locks.granted;
                        #                 """)
                        logger.info("Idle connections and blocking locks removed.")
                    except:
                        logger.info("Failed to remove idle connections")

                    if self.spark:
                        self \
                        .spark_etl() \
                        .monitor_data_quality()

                    elif self._type == "custom":
                        self.origin_conn = get_conn(self)
                        self.func(self = self, **self.config)

                    else:
                        self \
                        .extract_data() \
                        .prepare_to_load() \
                        .load_data() \
                        .monitor_data_quality()

        except Exception as e:
            self.target_conn.rollback()  # Rollback in case of error
            logger.error(f"Error in ETL task for object: {self.obj_name}", exc_info=True)
            raise e
    
        finally:
            # Only close origin_conn if it was successfully created
            if not ((self._type == 'planilha' or self.config.get("conn_type") == "planilha") or self.config.get("conn_type") == 'api' or self.spark):
                if hasattr(self, 'origin_conn') and self.origin_conn is not None:
                    self.origin_conn.close()
            if hasattr(self, 'target_conn') and self.target_conn is not None:
                self.target_conn.close()
            logger.info(f"Completed ETL task for object: {self.schema}.{self.obj_name}")

class Bronze(Etl):
    def __init__(self, sys, **kwargs):
        self._type = 'bronze'
        self._sys = sys
        self.tables = []
        if kwargs:
            super().__init__(**kwargs)

    def add_tables(self, table_list):
        for table in table_list:
            if isinstance(table, dict):
                for table_name, kwargs in table.items():
                    date_columns = kwargs.get('date_columns', [])
                    id_columns = kwargs.get('id_columns', [])
                    normal_columns = kwargs.get('normal_columns', [])
                    columns = id_columns + date_columns + normal_columns if normal_columns else ["*"]                    
                    times_per_day = kwargs.get('times_per_day', times_per_day_default)
                    query = kwargs.get('query')
                    self.tables.append({
                        'name': table_name,
                        'columns': columns,
                        'times_per_day': times_per_day,
                        'id_columns': id_columns,
                        'date_columns': date_columns,
                        'normal_columns': normal_columns,
                        'query': query
                    })
            else:
                self.tables.append({
                    'name': table,
                    'columns': '*',
                    'times_per_day': times_per_day_default
                })

        bronze_list = []
        for table in self.tables:
            if table.get('query'):
                query = table.get('query')
                self.custom_query = True
            else:
                columns = ', '.join(table['columns']) if table['columns'] != '*' else '*'
                query = f"SELECT {columns} FROM {table['name']}"

            self._query = query
            self._times_per_day = table.get('times_per_day')
            self.normal_columns = table.get('normal_columns')
            self.id_columns = table.get('id_columns')
            self.date_columns = table.get('date_columns')
            self._sys = self._sys
            self._origin_table_name = table['name']
            self.obj_name = f"bronze_{self._sys}_{table['name']}"

            bronze_list.append(Bronze(sys = self._sys, **vars(self)))

        self.bronze_list = bronze_list

        return self
    
    def build(self):
        return self.bronze_list

class Silver(Etl):
    def __init__(self, to_table):
        self._type = 'silver'
        self.to_table = to_table
        self._times_per_day = times_per_day_default 
        self._column_types = {} 
        self.configs = []

    def times_per_day(self, times_per_day):
        self._times_per_day = times_per_day
        return self

    def column_types(self, **column_types):
        self._column_types = column_types
        return self

    def index(self, index):
        self._index = index
        return self

    def sys(self, sys):
        self._sys = sys
        return self

    def query(self, query):
        self._query = query
        if hasattr(self, '_sys'):
            self.obj_name = f"silver_{self._sys}_{self.to_table}"
        else:
            self.obj_name = f"silver_{self.to_table}"
        super().__init__(**vars(self))
        return self

    def sys_table(self, sys, from_table):
        self.current_config = {
            'sys': sys,
            'from_table': f'dbdwcorporativo.bronze_{sys}_{from_table}',
            'column_dict': {}
        }
        self.configs.append(self.current_config)
        return self

    def columns(self, **column_dict):
        if hasattr(self, 'current_config'):
            self.current_config['column_dict'].update(column_dict)
        return self

    def build(self):

        if hasattr(self, '_query'):
            return self
        
        complete_column_set = set()
        for config in self.configs:
            complete_column_set.update(config['column_dict'].keys())

        queries = []
        for config in self.configs:
            columns = ", ".join([
                f"{config['column_dict'].get(col, 'NULL')}::text AS {col}"
                for col in complete_column_set
            ])
            query = f"SELECT {columns}, '{config['sys']}' as sistema FROM {config['from_table']}"
            queries.append(query)

        union_query = " UNION ALL ".join(queries)

        self.obj_name = f"silver_{self.to_table}"
        self._query = union_query

        super().__init__(**vars(self))

        return self

class Gold(Etl):
    def __init__(self, to_table):
        self._type = 'gold'
        self.to_table = to_table
        self._times_per_day = times_per_day_default
        self._column_types = {} 
        self.configs = []

    def times_per_day(self, times_per_day):
        self._times_per_day = times_per_day
        return self
    
    def sys(self, sys):
        self._sys = sys
        return self

    def column_types(self, column_types):
        self._column_types = column_types
        return self

    def index(self, index):
        self._index = index
        return self
    
    def query(self, query):
        self._query = query
        self.obj_name = f"gold_{self.to_table}"
        return self
    
    def build(self):
        super().__init__(**vars(self))
        return self

class View(Etl):
    def __init__(self, to_table):
        self._type = 'view'
        self.to_table = to_table
        self._times_per_day = times_per_day_default
        self._column_types = {} 
        self.configs = []

    def times_per_day(self, times_per_day):
        self._times_per_day = times_per_day
        return self

    def column_types(self, column_types):
        self._column_types = column_types
        return self

    def index(self, index):
        self._index = index
        return self
    
    def query(self, query):
        self._query = query
        self.obj_name = f"view_{self.to_table}"
        return self
    
    def build(self):
        super().__init__(**vars(self))
        return self
    
class Freeze(Etl):
    def __init__(self, to_table):
        self._type = 'freeze'
        self.to_table = to_table
        self._times_per_day = 1
        self._column_types = {} 
        self._scale = None

    def scale(self):
        self._scale = True
        return self

    def sys(self, _sys):
        self._sys = _sys
        return self

    def column_types(self, column_types):
        self._column_types = column_types
        return self

    def query(self, query):
        self._query = query
        if hasattr(self, '_sys'):
            self.obj_name = f"freeze_{self._sys}_{self.to_table}"
        else:
            self.obj_name = f"freeze_{self.to_table}"
        return self
    
    def build(self):
        super().__init__(**vars(self))
        return self

class Planilha(Etl):
    def __init__(self, file_name, **kwargs):
        self._type = 'planilha'
        self.file_name = file_name
        self._file_id = None
        self._folder_id = None
        self._times_per_day = times_per_day_default
        self.tabs = []  # Lista que vai conter as "abas" criadas dinamicamente
        if kwargs:
            super().__init__(file_name = file_name, **kwargs)

    def times_per_day(self, times_per_day):
        self._times_per_day = times_per_day
        return self
    
    def file_id(self, file_id):
        self._file_id = file_id
        return self
    
    def folder_id(self, folder_id):
        self._folder_id = folder_id
        return self

    def tab(self, tab_name = 0):
        # Cria um dicionário de configuração para a "aba"
        self.tab_name = tab_name
        return self

    def kwargs(self, **kwargs):

        config = {
            "tab_name": self.tab_name,
            "file_id": self._file_id,
            "folder_id": self._folder_id,
            "_times_per_day": self._times_per_day,
            "_type": self._type,
            "obj_name": f"planilha_{kwargs.get('table_name')}",
            **kwargs
        }

        self.tabs.append(Planilha(file_name= self.file_name, **config))
        return self

    def build(self):
        return self.tabs

class Custom(Etl):
    def __init__(self, func, **kwargs):
        super().__init__(obj_name = f"custom_{func.__name__}", _type = "custom", func = func, _times_per_day = kwargs.get("times_per_day", times_per_day_default), **kwargs)
    
    def build(self):
        return self
  
def create_etl_tasks(dag, object_lists, triggers=None):
    """
    Creates and organizes ETL tasks in an Airflow DAG with dynamic priority weights based on historical execution times.
    
    This function:
    1. Calculates task priorities based on historical execution times
    2. Creates parallel ETL tasks with appropriate scheduling
    3. Sets up task dependencies and monitoring
    4. Handles task skipping based on time of day
    5. Sets up triggers for dependent DAGs
    
    Args:
        dag (DAG): The Airflow DAG object to add tasks to
        object_lists (list or list of lists): List of ETL objects to process. Can be a single list or nested lists
        triggers (list, optional): List of DAG IDs to trigger after completion
        
    Returns:
        tuple: (start_etl_task, end_etl_task, dag_stats) - The start, end, and statistics tasks
    """
    
    # ===== 1. Input Validation and Setup =====
    if not all(isinstance(item, list) for item in object_lists):
        object_lists = [object_lists]
        sufix = ''
    else:
        sufix = True

    # ===== 2. Database Connection Setup =====
    conn = psycopg2.connect(
        host=DW_CORPORATIVO_HOST,
        dbname="airflow",
        user=AIRFLOW_USERNAME,
        password=AIRFLOW_PASSWORD,
        port=DW_CORPORATIVO_PORT
    )

    # ===== 3. Priority Weight Calculation =====
    # Query to get average duration of last 3 successful runs per task
    query = f"""
    SELECT
        task_id,
        AVG(duration) AS avg_duration
    FROM (
        SELECT
            task_id,
            duration,
            ROW_NUMBER() OVER (PARTITION BY task_id ORDER BY start_date DESC) as rn
        FROM task_instance
        WHERE state = 'success'
        AND dag_id = '{dag.dag_id}'
        and duration > 0
    ) sub
    WHERE rn <= 3
    GROUP BY task_id
    ORDER BY avg_duration DESC;
    """

    # Execute query and calculate priority weights
    with conn.cursor() as cur:
        cur.execute(query)
        results = cur.fetchall()

    # Normalize durations to priority weights (1-100)
    max_duration = max([row[1] for row in results]) if results else 1
    priority_weights = {
        task_id: int((duration / max_duration) * 100)
        for task_id, duration in results
    }
    default_priority = 50

    # ===== 4. Task Creation =====
    # Create start and auxiliary tasks
    start_etl_task = EmptyOperator(task_id=f'start_etl', dag=dag)
    get_aux_tables = PythonOperator(
        task_id=f"get_aux_tables",
        python_callable=get_aux_tables_func
    )
    get_aux_tables >> start_etl_task

    # Helper function for skipped tasks
    def skip_task_message():
        raise AirflowSkipException("Task skipped as it was not scheduled for this time.")

    # ===== 5. Process Each Object List =====
    for idx, object_list in enumerate(object_lists, start=1):
        if sufix:
            sufix = f'_{idx}'
        if idx > 1:
            start_etl_task = end_etl_task

        end_etl_task = EmptyOperator(task_id=f'end_etl{sufix}', dag=dag, trigger_rule='all_done') 
        
        # Build and flatten object list
        obj_list = [obj.build() for obj in object_list]
        for obj in ensure_flat_list(obj_list):
            task_id = f'{SCHEMA_CRIACAO_INSERT}_{obj.obj_name}' 
            current_hour = (datetime.now().hour - 3) % 24
            
            # if (obj._times_per_day == 1 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10)) or \
            #     (obj._times_per_day == 2 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14)) or \
            #     (obj._times_per_day == 3 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 16)) or \
            #     (obj._times_per_day == 4 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20)) or \
            #     obj._times_per_day == 5 or SCHEMA_CRIACAO_INSERT.lower() == "staging":

            # Determine if task should run based on time of day
            should_run = (
                (obj._times_per_day == 1 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9)) or
                (obj._times_per_day == 2 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 13)) or
                (obj._times_per_day == 3 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15)) or
                (obj._times_per_day == 4 and current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17)) or
                obj._times_per_day == 5 or 
                SCHEMA_CRIACAO_INSERT.lower() == "staging"
            )

            # Create task with appropriate callable and priority
            task = PythonOperator(
                task_id=task_id,
                python_callable=obj.execute if should_run else skip_task_message,
                priority_weight=priority_weights.get(task_id, default_priority),
                trigger_rule='all_done',
                on_success_callback=obj.monitor_task_data if should_run else None,
                on_failure_callback=obj.monitor_task_data if should_run else None,
                provide_context=True,
                dag=dag
            )
            
            # Set task dependencies
            start_etl_task >> task >> end_etl_task

    # ===== 6. Statistics and Monitoring =====
    dag_stats = PythonOperator(
        task_id=f"log_statistics",
        python_callable=log_dag_statistics,
        provide_context=True
    )

    # ===== 7. Trigger Setup =====
    if triggers:
        for trigger_dag_id in triggers:
            trigger_task = TriggerDagRunOperator(
                task_id=f'trigger_{trigger_dag_id.lower()}',
                trigger_dag_id=trigger_dag_id,
                trigger_rule='all_done',
                dag=dag
            )
            end_etl_task >> trigger_task >> dag_stats

    # ===== 8. Failure Checking =====
    check_falhas_task = PythonOperator(
        task_id=f'check_failed_tasks',
        python_callable=check_failed_tasks,
        trigger_rule='all_done',
        dag=dag,
    )

    end_etl_task >> check_falhas_task >> dag_stats

    return start_etl_task, end_etl_task, dag_stats