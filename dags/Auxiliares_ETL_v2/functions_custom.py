from Auxiliares_ETL_v2.functions import *
from psycopg2.extras import execute_batch

def etl_planilhas_automotivo(**kwargs):
    self = kwargs.get("self")
    df_merged = pd.DataFrame({
        "metas_objetivos_id": [],
        "sistema_origem": [],
        "cod_empresa": [],
        "periodo": [],
        "valor": [],
        "tipo_registro": [],
        "meta_objetivo_subitem": []
    })

    partial_dfs = []
    index = kwargs.get("index")
    
    # Calcula o total de abas a serem processadas
    total_tabs = sum(len(file_info.get("pages", {})) for file_info in index.values())
    processed_tabs = 0

    print(f"Iniciando processamento de {total_tabs} abas...\n")

    for file_name, file_info in index.items():
        file_id = file_info.get("file_id")
        self.config["file_id"] = file_info.get("file_id")
        print(f"Planilha: {file_name} (ID: {file_id})")

        for tab_name, tab_info in file_info.get("pages").items():
            processed_tabs += 1
            try:
                self.config["tab_name"] = tab_name
                metas_objetivos_id = tab_info.get("metas_objetivos_id")
                sistema_origem = 'DEALERNETWF' if str(tab_info.get("cod_empresa", "")).startswith('2') else 'NBS'
                cod_empresa = tab_info.get("cod_empresa")
                
                print(f" - Processando aba {processed_tabs}/{total_tabs}: {tab_name}")
                
                for df in fetch_data_in_chunks(self):
                    
                    if metas_objetivos_id:
                        # Dicionário mapeando column_name para tipo_registro
                        column_to_tipo = {
                            "local": "R",
                            "realizado": "R",
                            "realizado_perc": "RP",
                            "objetivo": "O",
                            "nacional": "N",
                            "nacional_perc": "NP",
                            "captado": "CP",
                            "local_acumulado_trimestre": "RT",
                            "nacional_acumulado_trimestre": "NT",
                            "a_faturar": "AF",
                            "projetado": "PJ",
                            "faixa": "FX",
                            "pontuacao": "PT",
                            "bonus": "BN",
                            "montadora": "O",
                            "objetivo_montadora": "O",
                            "transito": "T",
                            "concessionaria": "R",
                            "aguardando_liberacao": "AL",
                            "proj_realizado": "PR",
                            "proj_faixa": "PF",
                            "proj_pontuacao": "PP",
                            "pendente": "P",
                            "cariacica": "C",
                            "estoque_loja": "R",
                            "loja": "R",
                            "loja_acumulado_trimestre": "RT",
                            "oportunidades": "OP",
                            "leads": "LD",
                            "vendas": "VD",
                            "local_total": "LT",
                            "nacional_total": "NT",
                            "local_comparativo": "LC",
                            "nacional_comparativo": "NC",
                            "formato": "F",
                            "comissao": "CO",
                            "objetivo_lojas": "O",
                            "objetivo_frotistas": "OF"
                        }

                        # Função simplificada
                        def add_to_partial_dfs(column_name, tipo_registro):
                            if column_name in df.columns:
                                partial_dfs.append(pd.DataFrame({
                                    "metas_objetivos_id": metas_objetivos_id,
                                    "sistema_origem": sistema_origem,
                                    "cod_empresa": cod_empresa,
                                    "periodo": df["periodo"],
                                    "valor": df[column_name],
                                    "tipo_registro": tipo_registro,
                                    "meta_objetivo_subitem": df.get("meta_objetivo_subitem", pd.Series([None] * len(df)))
                                }))

                        # Iterar sobre o dicionário para adicionar os dados
                        for column_name, tipo_registro in column_to_tipo.items():
                            add_to_partial_dfs(column_name, tipo_registro)

                    else:
                        partial_dfs.append(df)

            except Exception as e:
                # Log de erro para a aba atual
                print(f"ERRO: Planilha {file_name}, aba {tab_name}. Detalhes: {e}")
                continue

        print(f"Planilha {file_name} processada.\n")

    df_merged = pd.concat(partial_dfs, ignore_index=True) if partial_dfs else pd.DataFrame()

    # Fazendo o tratamento dos dados
    df_merged["periodo"] = pd.to_numeric(df_merged["periodo"], errors="coerce") #ALTERACAO RAFAEL 07/10/2025
    df_merged = df_merged[df_merged["periodo"].notnull()]
    df_merged = df_merged[(df_merged["periodo"] % 100 >= 1) & (df_merged["periodo"] % 100 <= 12)]
    df_merged["periodo"] = df_merged["periodo"].astype(int)
    df_merged["valor"] = pd.to_numeric(df_merged["valor"], errors='coerce')
    df_merged = df_merged[df_merged["valor"].notnull()]
    df_merged = df_merged[["metas_objetivos_id","sistema_origem","cod_empresa","periodo","valor","tipo_registro","meta_objetivo_subitem"]]

    self.obj_name = "planilha_metas_objetivos_valores"
    
    query = f'TRUNCATE {self.schema}.{self.obj_name};'
    self.cur.execute(query)
    
    # Inserindo no PostgreSQL
    insert_data_in_chunks(self, df_merged)

    print(f"\nProcessamento finalizado!")
    print(f"Tabela {self.schema}.{self.obj_name} populada com sucesso!")
    print(f"Total de linhas processadas: {len(df_merged)}")

def etl_dados_mef(**kwargs):
    self = kwargs.get("self")
    index = kwargs.get("index")
    treatment = kwargs.get("treatment")

    try:
        # Conectar à rede
        logger.info("Iniciando conexão com o servidor SMB.")
        register_session(LINUX_SERVER, username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

        for base_path, base_path_info in index.items():
            for final_paths, file_info in base_path_info.items():
                try:
                    # Construir caminhos de arquivos
                    file_paths = (
                        [f'{base_path}{final_paths}'] 
                        if isinstance(final_paths, str) 
                        else [f'{base_path}{idx}' for idx in final_paths]
                    )
                    logger.info(f"Base path: {base_path}, Paths: {file_paths}")

                    self.tab_name = file_info.get("tab_name", 0)
                    self.dtype = file_info.get("dtype", {})
                    self.columns = file_info.get("columns")

                    # DataFrame para acumular todos os dados
                    df_combined = pd.DataFrame()

                    # Verificar se a conexão foi estabelecida corretamente
                    for file_path in file_paths:
                        self.file_path = file_path

                        try:
                            for entry in scandir(file_path):
                                self.file_path = file_path
                                self.entry = entry
                                self.ignore_files = True if not file_info.get("ignore") else False if entry.name.endswith(file_info.get("ignore", "")) else True
                                if entry.is_file() and entry.name.endswith(file_info.get("files")) and self.ignore_files:
                                    logger.info(f"Lendo arquivo: {entry.name}")
                                    for df in fetch_data_in_chunks(self):
                                        if self.columns:
                                            df.columns = self.columns
                                        df_combined = pd.concat([df_combined, df], ignore_index=True)
                                        logger.info(f"Arquivo '{entry.name}' lido com sucesso.")
                        except FileNotFoundError:
                            logger.error(f"Caminho ou arquivo não encontrado: {file_path}")
                            continue
                        except SMBException as e:
                            logger.error(f"Erro SMB ao acessar {file_path}: {str(e)}")
                            continue

                    if not df_combined.empty:
                        # Converter a coluna 'Amount' para string
                        if 'Amount' in df_combined.columns:
                            df_combined['Amount'] = (
                                df_combined['Amount']
                                .astype(str)
                                .str.strip()
                                .str.replace('.', '', regex=False)
                                .str.replace(',', '.', regex=False)
                                .replace('-', '0', regex=False)
                                .astype(float)
                            )
                            logger.info("'Amount' processada com sucesso.")

                        # Ajustar valores da coluna 'Filial'
                        if 'Filial' in df_combined.columns:
                            df_combined['Filial'] = (
                                df_combined['Filial']
                                .astype(str)
                                .replace("2901290000412", "02901290000412")
                                .replace("2901290000170", "02901290000170")
                            )
                            logger.info("'Filial' processada com sucesso.")

                        if treatment:
                            # Normalizar nomes das colunas
                            df_combined.columns = (
                                df_combined.columns
                                .str.replace(' ', '_')
                                .str.replace('.', '_')
                                .str.replace('-', '_')
                                .str.replace('Í', 'i')
                                .str.replace(':', '_')
                            )
                            
                        if 'Years' in df_combined.columns:
                            df_combined = df_combined[df_combined['Years'].notnull()]

                        # Preparar tabela para criação
                        table_name = file_info.get("table_name")
                        self.obj_name = f"planilha_{table_name.lower()}"

                        self.data = df_combined

                        # Criar tabela no PostgreSQL            
                        create_table_query = dataframe_to_create_table_query(self)
                        logger.info(f"Recreating table with statement:\n{create_table_query}")
                        self.cur.execute(create_table_query)
                        
                        # Inserir dados no PostgreSQL
                        insert_data_in_chunks(self, df_combined)
                        logger.info(f"Tabela {table_name} criada e dados inseridos com sucesso!")
                except Exception as e:
                    logger.error(f"Erro ao processar final_paths '{final_paths}': {traceback.format_exc()}")
                    raise e
    except Exception as e:
        logger.critical(f"Erro crítico no ETL: {traceback.format_exc()}")
        raise e

def etl_dados_inter(**kwargs):
    self = kwargs.get("self")
    index = kwargs.get("index")

    self._sys = 'newcon'
    container_name = 'files'

    erros = []  # Lista para armazenar informações de erros

    for csv_name, data_proc in index.items():
        try:
            logger.info(f"Iniciando processamento para o arquivo '{csv_name}.csv'.")

            # Conectar ao banco de dados
            logger.info(f"Executando consulta '{data_proc}' no banco de dados.")
            with get_conn(self) as conn:
                df = pd.read_sql_query(f"exec {data_proc}", conn)
            logger.info(f"Consulta '{data_proc}' executada com sucesso.")

            # Salvar DataFrame como CSV em memória
            logger.info(f"Convertendo DataFrame para CSV no arquivo '{csv_name}.csv'.")
            csv_data = df.to_csv(index=False)
            logger.info(f"DataFrame convertido com sucesso para o arquivo '{csv_name}.csv'.")

            # Conectar ao Azure Blob Storage
            logger.info("Estabelecendo conexão com o Azure Blob Storage.")
            blob_service_client = BlobServiceClient(account_url=BLOB_URL, credential=BLOB_CREDENTIAL)
            container_client = blob_service_client.get_container_client(container_name)
            logger.info(f"Conexão com Azure Blob Storage estabelecida. Container: {container_name}")

            # Upload do arquivo CSV para o Blob
            logger.info(f"Realizando upload do arquivo '{csv_name}.csv' para o Blob Storage.")
            blob_client = container_client.get_blob_client(f"{csv_name}.csv")
            blob_client.upload_blob(BytesIO(csv_data.encode()), overwrite=True)
            logger.info(f"Arquivo '{csv_name}.csv' enviado com sucesso para o Blob Storage.")

        except Exception as e:
            # Registrar erro nos logs e armazenar para análise posterior
            logger.error(f"Erro ao processar a consulta '{data_proc}' e enviar o arquivo '{csv_name}.csv': {e}")
            erros.append({
                "csv_name": csv_name,
                "data_proc": data_proc,
                "error_message": str(e)
            })

    # Se houver erros, retornar um resumo das falhas
    if erros:
        erro_resumo = "\n".join(
            [f"CSV: {erro['csv_name']}, Proc: {erro['data_proc']}, Erro: {erro['error_message']}" for erro in erros]
        )
        logger.error(f"Ocorreram erros em algumas iterações:\n{erro_resumo}")
        raise Exception(f"Ocorreram erros em algumas iterações:\n{erro_resumo}")

    logger.info("Processamento concluído com sucesso para todos os arquivos.")

def etl_vianuvem_insert(**kwargs):
    self = kwargs.get("self")
    current_hour = (datetime.now().hour - 3) % 24

    # Define intervalos com base no horário atual
    intervalos = [(21, 11), (11, 1)] if current_hour == 0 else [(0, 0)]

    for delta_inicio, delta_fim in intervalos:
        data_atual = datetime.now()
        initial_date = (data_atual - timedelta(days=delta_inicio)).strftime("%d/%m/%Y")
        final_date = (data_atual - timedelta(days=delta_fim)).strftime("%d/%m/%Y")

        for user in SEARCH_USERS:
            self.request = self.config['requests']['consulta_documentos']
            self.vars = {
                'initialDate': initial_date,
                'finalDate': final_date,
                'searchFor': user
            }

            data = next(fetch_data_in_chunks(self))

            if not isinstance(data, dict) or 'documents' not in data:
                logger.error("Erro na resposta da API", exc_info=True)
                continue

            items = data['documents']
            if not items:
                print(f"Nenhum documento encontrado para o usuário {user} no intervalo {initial_date} a {final_date}")
                continue

            df = pd.DataFrame([{
                'iddocument': item['idDocument'],
                'iduser': item['idUser'],
                'createdate': item['createDate'],
                'establishmentname': item['establishmentName'],
                'username': item['userName'],
                'idsituationcheck': item['idSituationCheck'],
                'idestablishment': item['idEstablishment'],
                'currentcheck': item['currentCheck'],
                'establishmentcnpj': item['establishmentCnpj']
            } for item in items])

            if df.empty:
                continue

            df['createdate'] = pd.to_datetime(df['createdate'], format='%d/%m/%Y %H:%M:%S').dt.strftime('%Y-%m-%d')

            # Filtra documentos apenas do dia atual, se necessário
            if initial_date == final_date:
                filtro_data = datetime.now().strftime("%Y-%m-%d")
                df = df.query(f'createdate == "{filtro_data}"')

            # Inserção no banco de dados
            for _, row in df.iterrows():
                merge_sql = sql.SQL("""
                    INSERT INTO {schema}.{table} (iddocument, iduser, createdate, establishmentname, username, 
                        idsituationcheck, idestablishment, currentcheck, establishmentcnpj)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (iddocument) DO NOTHING
                """).format(
                    schema=sql.Identifier(SCHEMA_CRIACAO_INSERT),
                    table=sql.Identifier("api_vianuvem_documentos_lancados")
                )
                self.cur.execute(merge_sql, (
                    row['iddocument'], row['iduser'], row['createdate'], row['establishmentname'],
                    row['username'], row['idsituationcheck'], row['idestablishment'],
                    row['currentcheck'], row['establishmentcnpj']
                ))

def etl_vianuvem_update(**kwargs):
    self = kwargs.get("self")

    query = sql.SQL("""
        SELECT iddocument FROM {schema}.{table}
        WHERE idsituationcheck <> 'S' AND currentcheck LIKE '%TI%'
    """).format(
        schema=sql.Identifier(self.schema),
        table=sql.Identifier('api_vianuvem_documentos_lancados')
    )

    self.cur.execute(query)
    documents_list = [linha[0] for linha in self.cur.fetchall()]

    if not documents_list:
        print("Lista vazia, não há documentos para atualizar.")
        return

    print("Consultando situação de aprovação atual dos documentos")
    self.request = self.config['requests']['consulta_documentos_atualizar']
    self.vars = {'documentId': documents_list}
    data = next(fetch_data_in_chunks(self))

    if not isinstance(data, dict) or 'documents' not in data:
        logger.error("Erro na resposta da API", exc_info=True)
        return

    items = data['documents']
    if not items:
        print("Nenhum documento encontrado para atualização.")
        return

    df = pd.DataFrame([{
        'iddocument': item['idDocument'],
        'establishmentname': item['establishmentName'],
        'idsituationcheck': item['idSituationCheck'],
        'idestablishment': item['idEstablishment'],
        'currentcheck': item['currentCheck'],
        'establishmentcnpj': item['establishmentCnpj']
    } for item in items])

    if df.empty:
        print("Dataframe vazio, possível erro na API")
        return

    # Atualização no banco de dados
    for _, row in df.iterrows():
        update_sql = sql.SQL("""
            UPDATE {schema}.{table}
            SET currentcheck = %s, idsituationcheck = %s
            WHERE iddocument = %s
        """).format(
            schema=sql.Identifier(SCHEMA_CRIACAO_INSERT),
            table=sql.Identifier('api_vianuvem_documentos_lancados')
        )
        self.cur.execute(update_sql, (row['currentcheck'], row['idsituationcheck'], row['iddocument']))


#region MeetTime

def merge_data(self, data):
    """Realiza a exclusão e reinserção dos dados no destino."""
    logging.info(f"Iniciando merge_data para o objeto {self.obj_name}.")
    
    # Configuração de colunas para exclusão e reinserção
    self.id_columns = ['current_prospection_id' if 'leads' in self.obj_name else \
            'prospection_id' if 'activities' in self.obj_name else 'id']
    delete_from_destin(self)
    logging.info(f"Dados antigos deletados para {self.obj_name}.")
    
    # Reorganizar DataFrame com base nas colunas da tabela
    self.cur.execute(f"""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = '{SCHEMA_CRIACAO_INSERT}' AND table_name = '{self.obj_name}'
        ORDER BY ordinal_position;
    """)
    table_columns = [row[0] for row in self.cur.fetchall()]
    data.columns = data.columns.str.lower()

    # Verificar colunas ausentes e reorganizar o DataFrame
    missing_columns = [col for col in table_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"As seguintes colunas estão ausentes no DataFrame: {missing_columns}")

    data = data[table_columns]
    column_list = ', '.join(table_columns)
    insert_data_in_chunks(self, data, column_list)
    logging.info(f"Dados inseridos com sucesso na tabela {self.obj_name}.")

def process_meetime(**kwargs):
    """Processa os dados da API Meetime."""
    dia_anterior = datetime.now() - timedelta(days=4)
    data = dia_anterior.strftime('%Y-%m-%d')
    self = kwargs.get("self")

    logging.info("Iniciando o processamento dos dados da Meetime.")
    for request, info in self.config['requests'].items():
        all_data = []
        total_pages = 0
        start = 0
        if request == 'coleta_prospections_updated':
            continue

        logging.info(f"Processando requisição: {request}.")

        self.obj_name = 'api_meetime_prospections' if 'coleta_prospections' in request else \
        'api_meetime_leads' if 'coleta_leads' in request else \
        'api_meetime_activities' if 'coleta_activities' in request else None

        second_table = 'api_meetime_prospections' if request == 'coleta_leads_updated' else None

        while True:
            self.request = info
            self.vars = {'start': start, 'data': data}
            logging.info(f"Requisição enviada para {self.obj_name}, página {total_pages + 1}.")
            data_dict = next(fetch_data_in_chunks(self))

            if not data_dict.get('data'):
                logging.warning(f"Nenhum dado encontrado na página {total_pages + 1} para {self.obj_name}.")
                break

            all_data.extend(data_dict['data'])
            total_pages += 1
            logging.info(f"Página {total_pages} salva para {self.obj_name}.")
            start += 100

        # Cria DataFrame após extrair os dados
        df = pd.DataFrame(all_data)
        logging.info(f"Total de páginas processadas para {self.obj_name}: {total_pages}.")
        self.data = df
        merge_data(self, df)

        # Processar tabela auxiliar, se necessário
        if second_table:
            logging.info(f"Processando tabela auxiliar: {second_table}.")
            todos_os_dados = []
            total_ids = len(self.data['current_prospection_id'].tolist())
            for idx, id in enumerate(self.data['current_prospection_id'].tolist(), 1):
                logging.info(f"Processando o {idx}/{total_ids} ID: {id}.")
                
                self.request = self.config['requests']['coleta_prospections_updated']
                self.vars = {'id': id}
                
                try:
                    dados = next(fetch_data_in_chunks(self))
                    if isinstance(dados, list):
                        todos_os_dados.extend(dados)
                        logging.info(f"{len(dados)} registros foram recuperados para o ID {id}.")
                    else:
                        todos_os_dados.append(dados)
                        logging.info(f"1 registro foi recuperado para o ID {id}.")
                except Exception as e:
                    logging.error(f"Erro ao processar o ID {id}: {str(e)}")
                    continue  # Para continuar o loop mesmo em caso de erro
                
            logging.info(f"Total de registros recuperados: {len(todos_os_dados)}.")

            df_aux = pd.DataFrame(item['data'] for item in todos_os_dados)
            df_aux.columns = ['data']
            df_aux['info_dict'] = df_aux['data'].apply(type).value_counts()
            df_aux = pd.json_normalize(df_aux['data'])

            self.obj_name = second_table
            merge_data(self, df_aux)

    logging.info("Processamento concluído com sucesso.")

def etl_meetime_insert(**kwargs):
    process_meetime(**kwargs)

def etl_meetime_update(**kwargs):
    process_meetime(**kwargs)
#endregion

#region BotMaker

#region V1
# Função para remover emojis
def remove_emojis(text):
    if not isinstance(text, str):
        return text
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # Emojis de rosto
        "\U0001F300-\U0001F5FF"  # Símbolos e pictogramas diversos
        "\U0001F680-\U0001F6FF"  # Transporte e símbolos relacionados
        "\U0001F1E0-\U0001F1FF"  # Bandeiras (códigos de país)
        "\U00002700-\U000027BF"  # Símbolos diversos
        "\U000024C2-\U0001F251"  # Símbolos adicionais
        "]+", 
        flags=re.UNICODE
    )
    return emoji_pattern.sub(r'', text)

def gerar_accesstoken(client_id, secret_id, refresh_token):
    # URL da API
    url = BOTMAKER_URL_ACCESSTOKEN

    # Dados para enviar na requisição POST
    payload = {  
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json',
        'clientId': client_id,
        'secretId': secret_id,
        'refreshToken': refresh_token
    }

    # Fazer a requisição POST
    response = requests.post(url, json=payload, headers=headers)

    # Verificar se a requisição foi bem-sucedida
    if response.status_code == 200:
        # Obter o JSON retornado
        data = response.json()
        return data.get('accessToken')

    else:
        logger.error(f"Erro na requisição do accessToken: {response.status_code}", exc_info=True)
        raise KeyError

def consulta_sessoes(access_token, initial_date, final_date):
    # URL da API
    url = BOTMAKER_URL_BASE + '/sessions'

    # Parâmetros para a requisição GET
    params = {
        'from': initial_date,
        'to': final_date,
        'include-variables': 'true'
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json',
        'access-token': access_token
    }
    
    all_data = []  # Lista para armazenar todos os dados
    current_url = url  # URL inicial da API
    first_request = True

    # Loop para lidar com paginação
    while current_url:
        if first_request:
            # Primeira requisição, inclui parâmetros
            response = requests.get(current_url, params=params, headers=headers)
            first_request = False  # Após a primeira requisição, desativa o uso de params
        else:
            # Requisições subsequentes, apenas URL e headers
            response = requests.get(current_url, headers=headers)

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Obter o JSON retornado
            data = response.json()

            # Verificar se o JSON retornado é um dicionário
            if isinstance(data, dict) and 'items' in data:
                # Obter a lista de itens
                items = data['items']

                # Selecionar campos específicos do JSON
                for item in items:
                    all_data.append({
                        'id': item.get('id', None),
                        'creationTime': item.get('creationTime', None),
                        'firstName': item.get('chat', {}).get('firstName', None),
                        'lastName': item.get('chat', {}).get('lastName', None),
                        'protocolo': item.get('chat', {}).get('variables', {}).get('protocolo', None),
                        'botParaAtribuicao': item.get('chat', {}).get('variables', {}).get('botParaAtribuicao', None),
                        'cpf_cnpj': item.get('chat', {}).get('variables', {}).get('cpf_cnpj', None),
                        'roteadorValorGerado': item.get('roteadorValorGerado', None),
                        'typification': item.get('chat', {}).get('variables', {}).get('typification', None),
                        'quemEncerrou': item.get('chat', {}).get('variables', {}).get('quemEncerrou', None),
                        'emailOperador': item.get('chat', {}).get('variables', {}).get('emailOperador', None),
                        'csatAgente': item.get('chat', {}).get('variables', {}).get('csatAgente', None),
                        'comentarioBot': item.get('chat', {}).get('variables', {}).get('comentarioBot', None),
                        'csatBot': item.get('chat', {}).get('variables', {}).get('csatBot', None),
                        'RegistroRateio': item.get('chat', {}).get('variables', {}).get('RegistroRateio', None),
                        'tags': item.get('chat', {}).get('tags', None)
                    })
            else:
                logger.error(f"Erro na geração da lista de dados", exc_info=True)
                raise KeyError

            # Atualizar a URL para a próxima página
            current_url = data.get('nextPage', None)
            if current_url == "null":  # Tratar a string "null" como None
               current_url = None

        else:
            logger.error(f"Erro na requisição: {response.status_code}", exc_info=True)
            raise KeyError

    # Criar um DataFrame com todos os dados coletados
    df = pd.DataFrame(all_data)

    # Remover emojis de todo o DataFrame
    df = df.applymap(remove_emojis)

    # Converter o formato para datetime com fuso horário UTC
    df['creationTime'] = pd.to_datetime(df['creationTime'], utc=True)
    # Ajustar o fuso horário para Horário Padrão de Brasília
    df['creationTime'] = df['creationTime'] - pd.Timedelta(hours=3)
    # Remover informações de fuso horário (opcional, para salvar no PostgreSQL como TIMESTAMP)
    df['creationTime'] = df['creationTime'].dt.tz_localize(None)

    # Converter a coluna 'tags' de lista para string (concatenando os elementos)
    df['tags'] = df['tags'].apply(lambda x: ', '.join(x) if isinstance(x, list) else x)

    return df

def retorna_parquet_sessoes():
        
        print('Iniciando pesquisa pelo arquivo parquet no FileServer')
        
        # Configuração de credenciais
        server = "nova.mmbq.local"
        share = "\BI$\Botmaker\CX"

        smbclient.register_session(server, username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

        try:
            # Caminho completo do compartilhamento e pasta
            smb_path = f"\\\\{server}\\{share}".rstrip("\\")
            
            # Lista os arquivos na pasta
            print(f"Listando arquivos em: {smb_path}")
            entries = smbclient.listdir(smb_path)
            print("Arquivos disponíveis:", entries)
            
            # Filtra o arquivo parquet desejado (modifique conforme necessário)
            parquet_file = next((entry for entry in entries if entry.endswith('sessoes_cxia_cxbot.parquet')), None)
            if not parquet_file:
                print("Nenhum arquivo Parquet encontrado.")
            else:
                print(f"Lendo o arquivo Parquet: {parquet_file}")
                
                # Caminho completo do arquivo
                file_path = f"{smb_path}\\{parquet_file}"
                
                # Abre o arquivo usando o smbclient
                with smbclient.open_file(file_path, mode='rb') as f:
                    file_content = f.read()
                
                # Lê o conteúdo do arquivo Parquet em um DataFrame
                buffer = BytesIO(file_content)
                df = pd.read_parquet(buffer)

                # Converter a coluna para datetime (se necessário)
                df['creationTime'] = pd.to_datetime(df['creationTime'])
                
                print("DataFrame carregado com sucesso!")
                print(df.head())  # Exibe as primeiras linhas do DataFrame

                return df

            # Encerre a conexão após o uso
            smbclient.delete_session(server)
            print(f"Conexão com o servidor {server} encerrada.")

        except Exception as e:
            print(f"Erro: {e}")

def atualiza_parquet_sessoes(**kwargs):

    df_parquet_desatualizado = retorna_parquet_sessoes()
    
    max_date_parquet = df_parquet_desatualizado['creationTime'].max()
    max_date_parquet = max_date_parquet + timedelta(hours=3)
    # Converter para o formato ISO 8601 com o sufixo 'Z'
    max_date_parquet = max_date_parquet.strftime('%Y-%m-%dT%H:%M:%SZ')
    print(f'Maior data retornada: {max_date_parquet}')

    access_token = gerar_accesstoken(BOTMAKER_CLIENT_ID, BOTMAKER_SECRET_ID, BOTMAKER_REFRESH_TOKEN)
    print(f"Token gerado: {access_token}")

    current_hour = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    print(f'Consultando sessões entre: {max_date_parquet} e {current_hour}')
    df_sessoes = (consulta_sessoes(access_token, max_date_parquet, current_hour))
    # Filtrar registros onde a coluna 'tags' contenha 'BotdeCXIA' ou 'BotdeCXFixo'
    df_sessoes = df_sessoes[df_sessoes['tags'].str.contains('BotdeCXIA|BotdeCXFixo', case=False, na=False)]
    # Filtrar linhas sessões que já existam no Parquet Original
    df_sessoes_atualizado = df_sessoes[~df_sessoes['id'].isin(df_parquet_desatualizado['id'])]

    print('DataFrame de sessões atualizado gerado com sucesso.')
    print(df_sessoes_atualizado.head())

    # Combinar os dataframes
    df_final = pd.concat([df_parquet_desatualizado, df_sessoes_atualizado], ignore_index=True)


    # Configuração de credenciais
    server = "nova.mmbq.local"
    share = "\BI$\Botmaker\CX"

    smbclient.register_session(server, username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

    try:
        # Caminho completo do compartilhamento e arquivo
        smb_path = f"\\\\{server}\\{share}".rstrip("\\")
        parquet_file_name = "sessoes_cxia_cxbot.parquet"
        file_path = f"{smb_path}\\{parquet_file_name}"

        # Converte o DataFrame para o formato Parquet em memória
        buffer = BytesIO()
        df_final.to_parquet(buffer, index=False)
        buffer.seek(0)  # Retorna ao início do buffer para leitura

        # Salva o arquivo no servidor (substitui se já existir)
        with smbclient.open_file(file_path, mode='wb') as f:
            f.write(buffer.read())

        print(f"Arquivo {parquet_file_name} salvo com sucesso em {smb_path}!")

    except Exception as e:
        print(f"Erro ao salvar o arquivo Parquet: {e}")

    finally:
        # Encerra a conexão com o servidor
        smbclient.delete_session(server)
        print(f"Conexão com o servidor {server} encerrada.")

#endregion

#region V2


# Conectar ao PostgreSQL e buscar a maior data
def get_max_date(self):
    query = "SELECT MAX(session_creation_time) FROM dbdwbotmaker.sessoes_cxia_cxfixo"
    self.cur.execute(query)
    max_date = self.cur.fetchone()[0]
    print(f'Maior data retornada: {max_date}')
    return max_date

# Conectar ao BigQuery e buscar dados de sessões
def consulta_sessoes_cxia_cxfixo(date):
    
    query = f"""
        SELECT
            sessions.session_id
            ,FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', TIMESTAMP(sessions.session_creation_time)) AS session_creation_time
            ,FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', TIMESTAMP(sessions.session_creation_time), "America/Sao_Paulo") AS session_creation_time_converted
            ,SPLIT(sessions.session_id, "_")[SAFE_OFFSET(0)] AS chat_id
            ,protocolo.var_value AS protocolo
            ,sessions.user_first_name
            ,sessions.user_last_name
            ,origem.var_value AS bot_para_atribuicao
            ,cpfcnpj.var_value AS cpf_cnpj
            ,roteador.var_value AS roteador_valor_gerado
            ,typification.var_value AS typification
            ,quem_encerrou.var_value AS quem_encerrou
            ,email_operador.var_value AS email_operador
            ,csat_agente.var_value AS csat_agente
            ,comentario_bot.var_value AS comentario_bot
            ,csat_bot.var_value AS csat_bot
            ,registro_rateio.var_value AS registro_rateio
            ,bot_de_cxia.var_value AS bot_de_cxia
            ,bot_de_cxfixo.var_value AS bot_de_cxfixo
            ,CONCAT('https://go.botmaker.com/#/chats/', SPLIT(sessions.session_id, "_")[SAFE_OFFSET(0)]) as chat_link
        FROM
            `botmaker-bigdata.ext_metric_grupobamaq.session_metrics` AS sessions
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'protocolo'
        ) AS protocolo ON
        protocolo.session_id = sessions.session_id
        AND protocolo.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'origem'
        ) AS origem ON
        origem.session_id = sessions.session_id
        AND origem.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'cpfcnpj'
        ) AS cpfcnpj ON
        cpfcnpj.session_id = sessions.session_id
        AND cpfcnpj.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'roteadorvalorgerado'
        ) AS roteador ON
        roteador.session_id = sessions.session_id
        AND roteador.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'typification'
        ) AS typification ON
        typification.session_id = sessions.session_id
        AND typification.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'quemencerrou'
        ) AS quem_encerrou ON
        quem_encerrou.session_id = sessions.session_id
        AND quem_encerrou.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'emailoperador'
        ) AS email_operador ON
        email_operador.session_id = sessions.session_id
        AND email_operador.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'csatagente'
        ) AS csat_agente ON
        csat_agente.session_id = sessions.session_id
        AND csat_agente.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'comentariobot'
        ) AS comentario_bot ON
        comentario_bot.session_id = sessions.session_id
        AND comentario_bot.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'csatbot'
        ) AS csat_bot ON
        csat_bot.session_id = sessions.session_id
        AND csat_bot.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'registrorateio'
        ) AS registro_rateio ON
        registro_rateio.session_id = sessions.session_id
        AND registro_rateio.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'botdecxia'
        ) AS bot_de_cxia ON
        bot_de_cxia.session_id = sessions.session_id
        AND bot_de_cxia.row_num = 1
        LEFT JOIN
        (
            SELECT
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY creation_time DESC) AS row_num,
                session_id,
                creation_time,
                var_value
            FROM
                `botmaker-bigdata.ext_metric_grupobamaq.user_vars_metrics`
            WHERE
                var_name = 'botdecxfixo'
        ) AS bot_de_cxfixo ON
        bot_de_cxfixo.session_id = sessions.session_id
        AND bot_de_cxfixo.row_num = 1
        WHERE
            --sessions.session_id = 'TBUUHX1PEPMWAYOBSL6H_2025-03-14T05:15:12.182Z'
            FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', TIMESTAMP(sessions.session_creation_time)) >= '{date}'
            AND ((bot_de_cxia.var_value IS NOT NULL AND bot_de_cxia.var_value <> 'false')
                    OR (bot_de_cxfixo.var_value IS NOT NULL AND bot_de_cxfixo.var_value <> 'false')
                    OR (csat_agente.var_value IS NOT NULL)
                    OR (csat_bot.var_value IS NOT NULL)
                    OR (comentario_bot.var_value IS NOT NULL))
    """

    hook = BigQueryHook(gcp_conn_id='google_cloud_default', use_legacy_sql=False)
    
    # Executa a query e retorna resultados
    df = hook.get_pandas_df(sql=query)
    print("Consultando sessões BotMaker.")
    count_rows = df["session_id"].nunique()
    print(f"{count_rows} registros.")
    print(df.head())

    return df

# Função para inserir os dados do dataframe na tabela
def insert_dataframe(self):
    logger.info("Iniciando carga do dataframe.")
    for index, row in self.df.iterrows():
        
        merge_sql = sql.SQL("""
        INSERT INTO dbdwbotmaker.sessoes_cxia_cxfixo (session_id, session_creation_time, session_creation_time_converted, chat_id, protocolo, user_first_name,
                                                        user_last_name, bot_para_atribuicao, cpf_cnpj, roteador_valor_gerado, typification, quem_encerrou, email_operador,
                                                        csat_agente, comentario_bot, csat_bot, registro_rateio, bot_de_cxia, bot_de_cxfixo, chat_link)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (session_id) DO NOTHING
        """)
        
        self.cur.execute(merge_sql, (row["session_id"], row["session_creation_time"], row["session_creation_time_converted"], row["chat_id"], row["protocolo"], row["user_first_name"],
                                row["user_last_name"], row["bot_para_atribuicao"], row["cpf_cnpj"], row["roteador_valor_gerado"], row["typification"], row["quem_encerrou"],
                                row["email_operador"],row["csat_agente"], row["comentario_bot"], row["csat_bot"], row["registro_rateio"], row["bot_de_cxia"], row["bot_de_cxfixo"], row["chat_link"]))

    # Commit e fechamento da conexão
    self.target_conn.commit()
    logger.info("Dataframe carregado para o DW.")

def merge_sessions_botmaker(**kwargs):
    try:
        self = kwargs.get("self")
        current_hour = (datetime.now().hour - 3) % 24
        # date = '2024-12-27 00:00:00'
        
        if current_hour in (0, 1, 2, 3, 4, 5, 6, 7, 8, 9):
            
            print("Iniciando exclusão de registros dos últimos 5 dias.")
            query = "DELETE FROM dbdwbotmaker.sessoes_cxia_cxfixo WHERE cast(session_creation_time as date) >= (select max(cast(session_creation_time as date)) from dbdwbotmaker.sessoes_cxia_cxfixo) - INTERVAL '5 days'"
            self.cur.execute(query)
            linhas_deletadas = self.cur.rowcount  # Captura a quantidade de linhas afetadas
            self.target_conn.commit()
            print(f'Deletado(s) {linhas_deletadas} registro(s) dos últimos 5 dias de sessões')


            print("Consultando maior data armazenada no DW.")
            # Armazena o valor na variável 'date' para filtrar consulta
            date = get_max_date(self)

            self.df = consulta_sessoes_cxia_cxfixo(date)

            # Inserir os dados do dataframe na tabela
            insert_dataframe(self)
        else:
            print("Consultando maior data armazenada no DW.")
            # Armazena o valor na variável 'date' para filtrar consulta
            date = get_max_date(self)

            self.df = consulta_sessoes_cxia_cxfixo(date)

            # Inserir os dados do dataframe na tabela
            insert_dataframe(self)
    except Exception as e:
        raise KeyError


#endregion

#endregion

#region Api_Office365

def gerar_accesstoken(tenant_id, client_id, secret_id):
    # URL da API
    url = f'https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token'

    # Dados para enviar na requisição POST
    payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': secret_id,
        'scope': 'https://graph.microsoft.com/.default'
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    # Fazer a requisição POST
    response = requests.post(url, data=payload, headers=headers)

    # Verificar se a requisição foi bem-sucedida
    if response.status_code == 200:
        # Obter o JSON retornado
        data = response.json()
        return data.get('access_token')

    else:
        logger.error(f"Erro na requisição do accessToken: {response.status_code}", exc_info=True)
        logger.error(f"Detalhes: {response.text}")
        raise KeyError

def consulta_licensas(access_token):
    # URL da API
    url = 'https://graph.microsoft.com/v1.0/users'

    # Parâmetros para a requisição GET
    params = {
        '$select': 'id,displayName,userPrincipalName,assignedLicenses'
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {access_token}',
    }
    
    all_data = []  # Lista para armazenar todos os dados
    current_url = url  # URL inicial da API
    first_request = True

    # Loop para lidar com paginação
    while current_url:
        if first_request:
            # Primeira requisição, inclui parâmetros
            response = requests.get(current_url, params=params, headers=headers)
            first_request = False  # Após a primeira requisição, desativa o uso de params
        else:
            # Requisições subsequentes, apenas URL e headers
            response = requests.get(current_url, headers=headers)

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Obter o JSON retornado
            data = response.json()

            # Iterar pelos usuários e quebrar os assignedLicenses
            for user in data.get('value', []):
                id = user.get('id')
                display_name = user.get('displayName')
                principal_name = user.get('userPrincipalName')
                licenses = user.get('assignedLicenses', [])

                # Se não houver licenças, adiciona uma linha com skuId = None
                if not licenses:
                    all_data.append({
                        'id': id,
                        'displayName': display_name,
                        'userPrincipalName': principal_name,
                        'skuId': None
                    })
                else:
                    for license in licenses:
                        all_data.append({
                            'id': id,
                            'displayName': display_name,
                            'userPrincipalName': principal_name,
                            'skuId': license.get('skuId')
                        })


            # Atualizar a URL para a próxima página
            current_url = data.get('@odata.nextLink', None) #Variavel simplesmente não aparece ver como tratar
            if current_url == "null":  # Tratar a string "null" como None
               current_url = None

        else:
            logger.error(f"Erro na requisição: {response.status_code}", exc_info=True)
            logger.error(f"Detalhes: {response.text}")
            raise KeyError

    # Criar um DataFrame com todos os dados coletados
    df = pd.DataFrame(all_data)

    # Agrupar por usuário, concatenando os skuId separados por '/'
    df = (
        df.groupby(['id', 'displayName', 'userPrincipalName'], as_index=False)
        .agg({'skuId': lambda x: '/'.join([str(sku) for sku in x if sku]) or None})
    )

    return df

def insert_licensas_office365(**kwargs):
    self = kwargs.get("self")
    try:
        access_token = gerar_accesstoken(OFFICE365_API_LICENSAS_TENANT_ID, OFFICE365_API_LICENSAS_CLIENT_ID, OFFICE365_API_LICENSAS_SECRET_ID)
        print(access_token)
        df = consulta_licensas(access_token)
        print(df.head)

        self.obj_name = "api_office365licenses"
    
        query = f'TRUNCATE {self.schema}.{self.obj_name};'
        self.cur.execute(query)
        
        # Inserindo no PostgreSQL
        insert_data_in_chunks(self, df)
    except Exception as e:
        logger.error(e)

def merge_office365_licenses(**kwargs):
    self = kwargs.get("self")
    self.cur.execute("""
        MERGE INTO dbdwcorporativo.aux_office365licenses AS destino
        USING
            (
                select
                    id,
                    displayname,
                    userprincipalname,
                    skuid_licenses
                from
                    dbdwcorporativo.api_office365licenses
            ) AS origem
        ON
            origem.id = destino.id
        WHEN MATCHED and (COALESCE(origem.skuid_licenses, '') <> COALESCE(destino.skuid_licenses, ''))
            THEN UPDATE SET skuid_licenses = origem.skuid_licenses
        WHEN NOT MATCHED
            THEN INSERT (id,displayname,userprincipalname,skuid_licenses)
                    VALUES (origem.id,origem.displayname,origem.userprincipalname,origem.skuid_licenses);
    """)

#endregion

#region Bacen Scrapper
def print_step(message, is_error=False):
    """Standardized print function for steps"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    prefix = "ERROR" if is_error else "INFO"
    print(f"[{timestamp}] {prefix}: {message}")

def extract_year_month(filename):
    """Extract year and month from filename like '202402Consorcios.zip'"""
    try:
        date_str = filename.split('Consorcios')[0]
        year = int(date_str[:4])
        month = date_str[4:]
        return int(f"{year}{month}")
    except:
        return None

def download_file(url, filename):
    """Download a file from URL to filename"""
    try:
        response = requests.get(url)
        if response.status_code == 200:
            with open(filename, "wb") as f:
                f.write(response.content)
            return True
        else:
            print_step(f"Failed to download {url}. Status code: {response.status_code}", True)
            return False
    except Exception as e:
        print_step(f"Error downloading {url}: {str(e)}", True)
        return False

def get_base_filename(filename):
    """Extract the base filename after the last digit and remove leading underscores"""
    last_digit_index = -1
    for i, char in enumerate(filename):
        if char.isdigit():
            last_digit_index = i
    
    if last_digit_index == -1:
        return filename.replace('.csv', '')
    
    base_name = filename[last_digit_index + 1:].replace('.csv', '')
    while base_name.startswith('_'):
        base_name = base_name[1:]
    
    return base_name

def normalize_column_name(col_name):
    """Normalize column names by removing accents and special characters"""
    col_name = unicodedata.normalize('NFKD', str(col_name)).encode('ascii', 'ignore').decode('utf-8')
    if col_name.isdigit():
        col_name = f"coluna{col_name}"
    col_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
    col_name = col_name.lower()
    return col_name.lstrip('_')

def get_postgres_type(pandas_type):
    """Convert pandas data type to PostgreSQL data type"""
    type_mapping = {
        'object': 'TEXT',
        'int64': 'DOUBLE PRECISION',
        'float64': 'DOUBLE PRECISION',
        'datetime64[ns]': 'TIMESTAMP',
        'bool': 'BOOLEAN'
    }
    return type_mapping.get(str(pandas_type), 'TEXT')

def get_db_params():
    """Get database parameters from .env file"""
    return {
        'user': Variable.get("DW_CORPORATIVO_USERNAME"),
        'password': Variable.get("DW_CORPORATIVO_PASSWORD"),
        'dbname': Variable.get("DW_CORPORATIVO_DBNAME"),
        'host': Variable.get("DW_CORPORATIVO_HOST"),
        'port': Variable.get("DW_CORPORATIVO_PORT")
    }

def get_existing_periods():
    """Get list of existing yearmonth values from all tables"""
    try:
        conn = psycopg2.connect(**get_db_params())
        cur = conn.cursor()
        
        table_groups = {
            'consorcios': ["dbdwcorporativo.planilha_bens_grupos", "dbdwcorporativo.planilha_segmentos_consolidados"],
            'consorcios_adm': ["dbdwcorporativo.planilha_consorcios_saldos_consolidados", "dbdwcorporativo.planilha_consorcios_administradoras"],
            'consorcios_uf': ["dbdwcorporativo.planilha_consorcios_uf"]
        }

        # First, get and delete the last two months from each table
        for group_name, tables in table_groups.items():
            for table in tables:
                try:
                    # Get the last two months from this table
                    get_months_query = f"""
                    SELECT DISTINCT yearmonth::text 
                    FROM {table} 
                    ORDER BY yearmonth DESC 
                    LIMIT 2;
                    """
                    cur.execute(get_months_query)
                    months_to_delete = [row[0] for row in cur.fetchall()]
                    
                    if months_to_delete:
                        print_step(f"Found months to delete from {table}: {months_to_delete}")
                        
                        # Delete these months
                        delete_query = f"""
                        DELETE FROM {table}
                        WHERE yearmonth::text IN ({','.join([f"'{month}'" for month in months_to_delete])});
                        """
                        cur.execute(delete_query)
                        print_step(f"Deleted data for months {months_to_delete} from {table}")
                        conn.commit()
                    else:
                        print_step(f"No data found in {table} to delete")
                        
                except Exception as e:
                    print_step(f"Error processing {table}: {str(e)}", True)
                    conn.rollback()
                    continue
        
        # Now get the remaining periods
        periods = {}
        for group_name, tables in table_groups.items():
            try:
                query = " UNION ".join([f"SELECT DISTINCT yearmonth::integer FROM {table}" for table in tables]) + " ORDER BY yearmonth;"
                cur.execute(query)
                periods[group_name] = [int(row[0]) for row in cur.fetchall()]
                print_step(f"Found {len(periods[group_name])} existing periods in {group_name}")
                print_step(f"Periods: {sorted(periods[group_name])}")
                conn.commit()
            except Exception as e:
                print_step(f"Could not get periods for {group_name}: {str(e)}", True)
                periods[group_name] = []
                conn.rollback()
                continue
        
        return periods['consorcios'], periods['consorcios_adm'], periods['consorcios_uf']
        
    except Exception as e:
        print_step(f"Error in database connection: {str(e)}", True)
        return [], [], []
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

def create_table_if_not_exists(cursor, table_name, df):
    """Create table if it doesn't exist"""
    try:
        cursor.execute(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'dbdwcorporativo' 
                AND table_name = '{table_name.split('.')[-1]}'
            );
        """)
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            df.columns = [normalize_column_name(col) for col in df.columns]
            columns = []
            for column in df.columns:
                dtype = get_postgres_type(df[column].dtype)
                columns.append(f'"{column}" {dtype}')
            
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {', '.join(columns)}
            );
            GRANT ALL ON TABLE {table_name} TO master;
            GRANT ALL ON TABLE {table_name} TO iebt_daniel_laranjo;
            GRANT ALL ON TABLE {table_name} TO ext_leticia_furletti;
            GRANT ALL ON TABLE {table_name} TO bq_dwcorporativo_u;
            GRANT ALL ON TABLE {table_name} TO ext_rafael_neto;
            GRANT SELECT ON TABLE {table_name} TO bq_dw_read_u;
            GRANT SELECT ON TABLE {table_name} TO bq_apipass_u;
            GRANT ALL ON TABLE {table_name} TO bq_im_powerbi_u;
            GRANT ALL ON TABLE {table_name} TO bq_lucas_ulhoa_u;
            GRANT ALL ON TABLE {table_name} TO bq_vitor_barros_u;
            GRANT ALL ON TABLE {table_name} TO bq_kelvin_santos;
            """
            print_step(f"Creating table {table_name}")
            cursor.execute(create_sql)
            print_step(f"Created table {table_name} successfully")
        
        return True
    except Exception as e:
        print_step(f"Error creating table {table_name}: {str(e)}", True)
        return False

def copy_data_to_postgres(file_path, table_name, conn):
    """Copy data from CSV to PostgreSQL using COPY command"""
    try:
        encodings = ['utf-8', 'latin1', 'iso-8859-1', 'cp1252']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, sep=';')
                print_step(f"Successfully read with {encoding} encoding")
                break
            except UnicodeDecodeError:
                print_step(f"Failed with {encoding} encoding, trying next...")
                continue
        
        if df is None:
            raise Exception("Could not read file with any encoding")
        
        df.columns = [normalize_column_name(col) for col in df.columns]
        
        if 'yearmonth' in df.columns:
            df['yearmonth'] = df['yearmonth'].astype(int)
        
        cursor = conn.cursor()
        
        if not create_table_if_not_exists(cursor, table_name, df):
            raise Exception(f"Failed to create table {table_name}")
        
        csv_data = df.to_csv(index=False, sep=';', encoding='utf-8', header=False)
        buffer = StringIO(csv_data)
        
        cursor.copy_expert(
            f"""
            COPY {table_name} FROM STDIN WITH (
                FORMAT csv,
                DELIMITER ';',
                NULL '',
                ENCODING 'utf-8'
            )
            """,
            buffer
        )
        
        conn.commit()
        print_step(f"Successfully copied data to {table_name}")
        
    except Exception as e:
        print_step(f"Error copying data to {table_name}: {str(e)}", True)
        conn.rollback()
        raise

def run_scraper():
    """Main function to run the scraper"""
    print_step("Starting scraper initialization...")

    download_dir = "downloads"
    print_step(f"Creating downloads directory: {download_dir}")
    os.makedirs(download_dir, exist_ok=True)
    print_step("Downloads directory ready")

    try:
        ext_per, ext_per_adm, ext_per_uf = get_existing_periods()

        print_step("Initializing Chrome WebDriver in headless mode...")
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        print_step("WebDriver initialized successfully")

        url = "https://www.bcb.gov.br/estabilidadefinanceira/consorciobd"
        print_step(f"Navigating to URL: {url}")
        driver.get(url)

        print_step("Waiting for page to load (6 seconds)...")
        time.sleep(6)
        print_step("Page load wait completed")

        print_step(f"Current page title: {driver.title}")
        print_step(f"Current URL: {driver.current_url}")

        print_step("Checking for iframes...")
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print_step(f"Found {len(iframes)} iframes")

        print_step("Trying to find the Consorcios select element...")
        try:
            print_step("Trying in main page...")
            select_element = driver.find_element(By.ID, "Consorcios")
            print_step("Found Consorcios element in main page!")
        except NoSuchElementException:
            print_step("Not found in main page, trying iframes...")
            select_element = None
            for idx, iframe in enumerate(iframes, 1):
                try:
                    print_step(f"Trying iframe {idx}...")
                    driver.switch_to.frame(iframe)
                    select_element = driver.find_element(By.ID, "Consorcios")
                    print_step(f"Found Consorcios element in iframe {idx}!")
                    break
                except NoSuchElementException:
                    print_step(f"Not found in iframe {idx}")
                    driver.switch_to.default_content()
                except Exception as e:
                    print_step(f"Error checking iframe {idx}: {str(e)}", True)
                    driver.switch_to.default_content()

        if select_element:
            print_step("Found the Consorcios select element!")
            
            print_step("Getting options using JavaScript...")
            options_js = driver.execute_script("""
                const selects = document.querySelectorAll('#Consorcios');
                if (!selects.length) return [];
                
                const allOptions = [];
                selects.forEach((select, index) => {
                    const options = Array.from(select.options).map(opt => ({
                        text: opt.text,
                        value: opt.value,
                        html: opt.outerHTML,
                        selectIndex: index
                    }));
                    allOptions.push(...options);
                });
                
                return allOptions;
            """)
            
            print_step(f"Found {len(options_js)} options via JavaScript")
            options = options_js
        else:
            print_step("Could not find Consorcios element in any location")
            options = []

        try:
            driver.switch_to.default_content()
        except:
            pass

        if options:
            base_url = "https://www.bcb.gov.br"
            print_step(f"Using base URL: {base_url}")
            
            new_options = []
            for option in options:
                file_link = option['value']
                if file_link:
                    filename = file_link.split("/")[-1]
                    yearmonth = extract_year_month(filename)
                    yearmonth_ref = 201901
                    if "Consorcios.zip" in filename and yearmonth and yearmonth not in ext_per and yearmonth >= yearmonth_ref:
                        new_options.append(option)
                    elif "Consorcios_ADM.zip" in filename and yearmonth and yearmonth not in ext_per_adm and yearmonth >= yearmonth_ref:
                        new_options.append(option)
                    elif "Consorcios_UF.zip" in filename and yearmonth and yearmonth not in ext_per_uf and yearmonth >= yearmonth_ref:
                        new_options.append(option)
            
            print_step(f"Found {len(new_options)} new files to download")
            if new_options:
                print_step("New files to download: " + ", ".join([opt['value'].split("/")[-1] for opt in new_options]))
            
            if not new_options:
                print_step("No new periods to download. All data is up to date.")
                driver.quit()
                return
            
            for index, option in enumerate(new_options, 1):
                print_step(f"Processing option {index}/{len(new_options)}")
                print_step(f"Option text: {option['text']}")
                
                file_link = option['value']
                
                if file_link:
                    full_url = base_url + file_link
                    print_step(f"Full download URL: {full_url}")
                    
                    filename = os.path.join(download_dir, file_link.split("/")[-1])
                    
                    if download_file(full_url, filename):
                        print_step(f"Successfully downloaded to: {filename}")
                    else:
                        print_step(f"Failed to download {filename}", True)
                else:
                    print_step("Warning: No file link found for this option")
        else:
            print_step("No options found to process")

        print_step("All downloads completed")
        print_step("Closing WebDriver...")
        driver.quit()
        print_step("WebDriver closed successfully")
    except Exception as e:
        print_step(f"Error in scraper: {str(e)}", True)
        raise

def process_csvs():
    """Process CSV files from ZIP archives"""
    processed_dir = "processed_data"
    download_dir = "downloads"
    print_step(f"Creating processed_data directory: {processed_dir}")
    os.makedirs(processed_dir, exist_ok=True)
    print_step("Processed data directory ready")
    
    try:
        zip_files = glob.glob(os.path.join(download_dir, "*.zip"))
        
        if not zip_files:
            print_step("No zip files found in downloads directory")
            return
        
        print_step(f"Found {len(zip_files)} zip files to process")
        
        for zip_file in zip_files:
            print_step(f"Processing {zip_file}")
            
            try:
                with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                    zip_ref.extractall(processed_dir)
                
                print_step(f"Extracted {zip_file}")
                
                os.remove(zip_file)
                print_step(f"Deleted zip file: {zip_file}")
                
            except Exception as e:
                print_step(f"Error processing {zip_file}: {str(e)}", True)
                continue
        
        significado_files = glob.glob(os.path.join(processed_dir, "Significado*"))
        for file in significado_files:
            try:
                os.remove(file)
                print_step(f"Deleted Significado file: {file}")
            except Exception as e:
                print_step(f"Error deleting Significado file {file}: {str(e)}", True)
        
        csv_files = glob.glob(os.path.join(processed_dir, "*.csv"))
        
        if not csv_files:
            print_step("No CSV files found to process")
            return
        
        print_step(f"Found {len(csv_files)} CSV files to process")
        
        file_groups = {}
        for csv_file in csv_files:
            filename = os.path.basename(csv_file)
            base_name = get_base_filename(filename)
            
            if any(x in base_name.lower() for x in ['bens_imoveis_grupos', 'bens_moveis_grupos', 'bens_moveis']):
                group_name = 'Bens_Grupos'
            else:
                group_name = base_name
            
            if group_name not in file_groups:
                file_groups[group_name] = []
            file_groups[group_name].append(csv_file)
        
        print_step(f"Found {len(file_groups)} different types of CSV files to consolidate")
        
        for base_name, files in file_groups.items():
            print_step(f"Processing group: {base_name}")
            print_step(f"Found {len(files)} files to consolidate")
            
            consolidated_df = pd.DataFrame()
            
            for csv_file in files:
                print_step(f"Processing {csv_file}")
                
                try:
                    encodings = ['utf-8', 'latin1', 'iso-8859-1', 'cp1252']
                    df = None
                    
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(csv_file, encoding=encoding, sep=';')
                            print_step(f"Successfully read with {encoding} encoding")
                            break
                        except UnicodeDecodeError:
                            print_step(f"Failed with {encoding} encoding, trying next...")
                            continue
                    
                    if df is None:
                        raise Exception("Could not read file with any encoding")
                    
                    filename = os.path.basename(csv_file)
                    yearmonth = filename[:6]
                    
                    df['yearmonth'] = yearmonth
                    
                    consolidated_df = pd.concat([consolidated_df, df], ignore_index=True)
                    print_step(f"Added to consolidated DataFrame")
                    
                    os.remove(csv_file)
                    print_step(f"Deleted processed CSV file: {csv_file}")
                    
                except Exception as e:
                    print_step(f"Error processing {csv_file}: {str(e)}", True)
                    continue
            
            if not consolidated_df.empty:
                output_file = os.path.join(processed_dir, f"{base_name}.csv")
                if os.path.exists(output_file):
                    os.remove(output_file)
                consolidated_df.to_csv(output_file, index=False, encoding='utf-8', sep=';')
                print_step(f"Saved consolidated {base_name}.csv")
        
        print_step("Processing completed")
    finally:
        if os.path.exists(download_dir):
            for file in os.listdir(download_dir):
                file_path = os.path.join(download_dir, file)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                except Exception as e:
                    print_step(f"Error deleting {file_path}: {e}", True)
            try:
                os.rmdir(download_dir)
                print_step(f"Successfully deleted downloads directory: {download_dir}")
            except Exception as e:
                print_step(f"Error deleting downloads directory: {e}", True)

def load_to_postgres():
    """Load processed data into PostgreSQL"""
    processed_dir = "processed_data"
    try:
        db_params = get_db_params()
        
        conn = psycopg2.connect(**db_params)
        print_step("Connected to PostgreSQL database")
        
        for file in glob.glob(os.path.join(processed_dir, "*.csv")):
            try:
                print_step(f"Processing file: {file}")
                
                base_filename = os.path.splitext(os.path.basename(file))[0]
                table_name = f"dbdwcorporativo.planilha_{base_filename.lower().replace(' ', '_')}"
                
                copy_data_to_postgres(file, table_name, conn)
                
                os.remove(file)
                print_step(f"Deleted processed file: {file}")
                
            except Exception as e:
                print_step(f"Error processing file {file}: {str(e)}", True)
                continue
        
        print_step("Data loading completed successfully")
        
    except Exception as e:
        print_step(f"Error in load_to_postgres: {str(e)}", True)
        raise
    finally:
        if 'conn' in locals():
            conn.close()
        if os.path.exists(processed_dir):
            for file in os.listdir(processed_dir):
                file_path = os.path.join(processed_dir, file)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                except Exception as e:
                    print_step(f"Error deleting {file_path}: {e}", True)
            try:
                os.rmdir(processed_dir)
                print_step(f"Successfully deleted processed_data directory: {processed_dir}")
            except Exception as e:
                print_step(f"Error deleting processed_data directory: {e}", True)

def run_script(script_name, script_function):
    """Run a script function and log its output"""
    try:
        print_step(f"Starting {script_name}...")
        start_time = time.time()
        
        script_function()
        
        end_time = time.time()
        duration = end_time - start_time
        print_step(f"Completed {script_name} in {duration:.2f} seconds")
        
    except Exception as e:
        print_step(f"Error in {script_name}: {str(e)}", True)
        raise

def run_etl(**kwargs):
    """Run the complete ETL process"""
    try:
        start_time = time.time()
        print_step("Starting ETL process...")
        
        scripts = [
            ("Scraper", run_scraper),
            ("CSV Processor", process_csvs),
            ("PostgreSQL Loader", load_to_postgres)
        ]
        
        for script_name, script_function in scripts:
            run_script(script_name, script_function)
        
        end_time = time.time()
        total_duration = end_time - start_time
        print_step(f"ETL process completed in {total_duration:.2f} seconds")
        
    except Exception as e:
        print_step(f"ETL process failed: {str(e)}", True)
        raise
#endregion

#region MEF - EPM
def tem_permissao_execucao(cursor, nome_proc):
    query = f"""
        SELECT HAS_PERMS_BY_NAME('{nome_proc}', 'OBJECT', 'EXECUTE') AS tem_execucao;
    """
    cursor.execute(query)
    resultado = cursor.fetchone()
    return resultado and resultado[0] == 1

def mapear_tipo_sqlserver_para_postgres(tipo):
    # Mapeamento simplificado
    mapping = {
        int: 'INTEGER',
        float: 'DOUBLE PRECISION',
        str: 'TEXT',
        bytes: 'BYTEA',
        bool: 'BOOLEAN',
        datetime: 'TIMESTAMP',
    }
    return mapping.get(tipo, 'TEXT')  # padrão: TEXT

def process_mefepm(**kwargs):
    self = kwargs.get("self")
    self._sys = "sge"
    agora = datetime.now()
    mes_param = f"{agora.month:02d}"
    ano_param = str(agora.year)


# Parâmetro fixo de usuário (pode vir de input() se preferir)
    usuario_param = 'DwCorporat'
    procedures = [
    {'nome': 'pr_apaga_registros_EPM', 'parametros': {'usuario': usuario_param}},
    {'nome': 'pr_AlimentaMovimentoContabilDealer', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}},
    # {'nome': 'pr_AlimentaVolumeDealer', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}},
    {'nome': 'pr_CapturaMovimentoContabilNBS', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}},
    {'nome': 'pr_AplicaRegraNBS', 'parametros': {'usuario': usuario_param}},
    {'nome': 'pr_AlimentaMovimentoContabilNBS', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}},
    # {'nome': 'pr_AlimentaVolumeNBS', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}},
    {'nome': 'pr_AlimentaMovimentoContabilNewCon', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}},
    {'nome': 'pr_AlimentaMovimentoContabilIFS', 'parametros': {'ano': ano_param,'mes': mes_param,'usuario': usuario_param}}
]   
    tabela_origem_1 = 'tbTransporteVolumeEPMOracle'
    tabela_destino_1 = 'dbdwcorporativo.tbTransporteVolumeEPMOracle'
    # chunk_size = 5000  # ajuste conforme o tamanho dos dados e memória.
    tabela_origem_2 = 'tbTransporteEPMOracle'
    tabela_destino_2 = 'dbdwcorporativo.tbTransporteEPMOracle'

    try:
        # Conectar aos bancos       
        cursor_pg = self.cur

        print("Conectado aos dois bancos.\n")

        # # Coleta 1 linha da tabela de origem só pra pegar metadata
        # cursor_sql.execute(f"SELECT TOP 1 * FROM {tabela_origem_1}")
        # colunas = cursor_sql.description

        # # Monta definição das colunas para o PostgreSQL
        # definicoes_colunas = []
        # for col in colunas:
        #     nome_coluna = col[0]
        #     tipo_python = col[1]
        #     tipo_pg = mapear_tipo_sqlserver_para_postgres(tipo_python)
        #     definicoes_colunas.append(f'"{nome_coluna}" {tipo_pg}')

        # definicoes_str = ',\n    '.join([col.lower() for col in definicoes_colunas])

        create_sql = f'''
        DELETE FROM {tabela_destino_1}
        WHERE usuario_processamento ='{usuario_param}'
        AND ano = '{ano_param}' AND periodo like '%{mes_param}%';
        '''
        cursor_pg.execute(create_sql) 
        
        # # Coleta 1 linha da tabela de origem só pra pegar metadata
        # cursor_sql.execute(f"SELECT TOP 1 * FROM {tabela_origem_2}")
        # colunas = cursor_sql.description

        # # Monta definição das colunas para o PostgreSQL
        # definicoes_colunas = []
        # for col in colunas:
        #     nome_coluna = col[0]
        #     tipo_python = col[1]
        #     tipo_pg = mapear_tipo_sqlserver_para_postgres(tipo_python)
        #     definicoes_colunas.append(f'"{nome_coluna}" {tipo_pg}')

        # definicoes_str = ',\n    '.join([col.lower() for col in definicoes_colunas])
        
        create_sql = f'''
        DELETE FROM {tabela_destino_2}
        WHERE usuario_processamento ='{usuario_param}'
        AND ano = '{ano_param}' AND periodo like '%{mes_param}%';        '''

        cursor_pg.execute(create_sql) 
        print(f"✅ Tabela '{tabela_destino_1}', '{tabela_destino_2}' dados deletados no PostgreSQL com base na origem '{tabela_origem_1}', '{tabela_destino_2}'.")

    except Exception as e:
        print(f"❌ Erro: {e}")

    # Conectar aos bancos
    conn = get_conn(self)
    cursor = conn.cursor()
    print("✅ Conexão SQL estabelecida com sucesso!")
    cursor_pg = self.cur
    print("✅ Conexão Postgres estabelecida com sucesso!\n")
    for proc in procedures:
        nome_proc = proc['nome']
        parametros = proc['parametros']
        if not tem_permissao_execucao(cursor, nome_proc):
            print(f"⚠️  Usuário NÃO tem permissão para executar a procedure {nome_proc}. Pulando...")
            continue
        else:
            print(f"✅  Usuário tem permissão para executar a procedure {nome_proc}.")
        if parametros:
            params_str = ', '.join([f"@{k} = %s" for k in parametros])
            valores = tuple(parametros.values())
            print(f"Executando {nome_proc} com parâmetros: {parametros}")
            cursor.execute(f"EXEC {nome_proc} {params_str}", valores)
        else:
            print(f"Executando {nome_proc} sem parâmetros")
            cursor.execute(f"EXEC {nome_proc}")
        conn.commit()
        print(f"Procedure {nome_proc} executada com sucesso.\n")
    # Pega os dados da tabela
    self.origin_conn = conn
    self._query = f"SELECT * FROM {tabela_origem_1} where Usuario_Processamento = '{usuario_param}'"
    self.obj_name = "tbTransporteVolumeEPMOracle"
    self.schema = "dbdwcorporativo"
    total = 0
    for self.data in fetch_data_in_chunks(self):
        insert_data_in_chunks(self,self.data)
        total += len(self.data)
        print(f"🔄 {total} registros inseridos no PostgreSQL...")
    print(f"✅ Inserção finalizada. Total: {total} registros.")
        # Pega os dados da tabela
    self.origin_conn = conn
    self._query = f"SELECT * FROM {tabela_origem_2} where Usuario_Processamento = '{usuario_param}'"
    self.obj_name = "tbTransporteEPMOracle"
    total = 0
    for self.data in fetch_data_in_chunks(self):
        insert_data_in_chunks(self,self.data)
        total += len(self.data)
        print(f"🔄 {total} registros inseridos no PostgreSQL...")
    print(f"✅ Inserção finalizada. Total: {total} registros.")        

    #endregion

#region Neurotech
def update_limitecredito_dealernet(cursor):
    query = f"""
        UPDATE pessoa SET pessoa_valorlimitecredito = 0 WHERE pessoa_valorlimitecredito > 0 --and pessoa_codigo = 61335
    """
    cursor.execute(query)
    print(f"{cursor.rowcount} linha(s) atualizada(s).")

def update_limitecredito_nbs(cursor):
    query = f"""
        UPDATE cliente_diverso SET limite_credito = 0 WHERE limite_credito > 0 --and cod_cliente = 78725445553
    """
    cursor.execute(query)
    print(f"{cursor.rowcount} linha(s) atualizada(s).")

def execute_update_erp(**kwargs):
    try:
        logger.info("Iniciando update no NBS.")
        self = kwargs.get("self")
        self._sys = "nbs"
        # Conectar aos bancos
        conn = get_conn(self)
        logger.info("Conectado ao erp NBS.")
        cursor = conn.cursor()
        update_limitecredito_nbs(cursor)
        cursor.close()
        conn.commit()
    except Exception as e:
        raise KeyError

    try:
        logger.info("Iniciando update no DealernetWF.")
        self = kwargs.get("self")
        self._sys = "dealer"
        # Conectar aos bancos
        conn = get_conn(self)
        logger.info("Conectado ao erp DealernetWF.")
        cursor = conn.cursor()
        update_limitecredito_dealernet(cursor)
        cursor.close()
        conn.commit()
    except Exception as e:
        raise KeyError

#endregion

#region marketshare
def ETL_marketshare(**kwargs):
    def extract_eixo_number(filename: str) -> str:
        patterns = [
            r'Eixo\s+([A-Za-z]+)',  
            r'Eixo\s+([A-Za-z]+)\s*-', 
            r'-\s*Eixo\s+([A-Za-z]+)',  
            r'([A-Za-z]+)\s*-\s*Eixo'   
        ]
        for pat in patterns:
            m = re.search(pat, filename, re.IGNORECASE)
            if m:
                return m.group(1).strip()
        return None

    def try_read_csv(data: bytes, header_parts=["Data do Emplacamento"]):
        # Encontra a linha do cabeçalho
        header_idx = None
        
        try:
            text = data.decode('latin1')
            lines = text.splitlines()
            for i, line in enumerate(lines):
                if any(p in line for p in header_parts):
                    header_idx = i
                    print(f"✓ Cabeçalho encontrado na linha {i}")
                    break
        except Exception as e:
            print(f"Erro ao decodificar arquivo: {e}")
            raise
        
        if header_idx is None:
            raise ValueError("Cabeçalho não encontrado")
        
        # Remove o cabeçalho e lê apenas os dados com latin1
        data_lines = lines[header_idx + 1:]  # +1 para pular o cabeçalho
        
        # Cria um cabeçalho padrão
        header_line = "Data do Emplacamento;CNPJ;Razão Social;Chassi;Placa;Fabricante;Família;Modelo;Estado;Município"
        csv_content = header_line + '\n' + '\n'.join(data_lines)
        
        df = pd.read_csv(
            StringIO(csv_content),
            sep=';',
            quotechar='"',
            engine='python',
            dtype=str
        )
        
        print(f"✓ Dados lidos com latin1")
        return df, 'latin1'

    username = SHAREPOINT_USERNAME
    password = SHAREPOINT_PASSWORD 
    site_url = SITE_URL
    folder_id = "a9e9a30f-ac77-4ac6-bfd9-ce253705de2b"

    self = kwargs.get("self")

    # --- Autenticação igual ao consorcio ---
    ctx_auth = AuthenticationContext(url=site_url)
    ctx_auth.acquire_token_for_user(username, password)
    ctx = ClientContext(site_url, ctx_auth)
    web = ctx.web
    ctx.load(web)
    ctx.execute_query()

    folder = web.get_folder_by_id(folder_id)
    ctx.load(folder)
    ctx.execute_query()

    all_dfs = []

    files = folder.files
    ctx.load(files)
    ctx.execute_query()

    for f in files:
        name = f.properties.get("Name", "")
        if not name.lower().endswith(".csv"):
            continue

        print(f"Baixando {name} …")
        eixo = extract_eixo_number(name)
        response = File.open_binary(ctx, f.properties["ServerRelativeUrl"])
        data = response.content

        try:
            df, used_encoding = try_read_csv(data)
            print(f"Colunas lidas do arquivo {name}: {list(df.columns)}")
            all_dfs.append(df)
        except Exception as e:
            print(f"Erro ao processar {name}: {e}")
            continue

    if not all_dfs:
        return None

    df_final = pd.concat(all_dfs, ignore_index=True)
    # Imprime todos os estados distintos do DataFrame final
    if 'Estado' in df_final.columns:
        print(f"Estados distintos no DataFrame final: {sorted(df_final['Estado'].dropna().unique())}")
    else:
        print("Coluna 'estado' não encontrada no DataFrame final!")
    
    self._structure_check = "empty"
    self.schema = "dbdwcorporativo"
    self.obj_name = "planilha_marketshare_emplacamento"
    self.data = df_final

    prepare_table(self)
    insert_data_in_chunks(self, self.data)

    return None
#endregion

#region planilhas automotivo
def ETL_metas_consorcio(**kwargs):
    
    username = SHAREPOINT_USERNAME
    password = SHAREPOINT_PASSWORD 
    site_url = SITE_URL
    folder_id = "50d87f3f-01bd-42f0-bfde-a2394f7d5b52"
    file_id = "626D4ABD-748A-471B-9D5C-E5920861F23B"

    self = kwargs.get("self")

    ctx_auth = AuthenticationContext(url=site_url)
    ctx_auth.acquire_token_for_user(username, password)
    
    
    ctx = ClientContext(site_url, ctx_auth)
    web = ctx.web
    ctx.load(web)
    ctx.execute_query()

    folder = web.get_folder_by_id(folder_id)
    ctx.load(folder)
    ctx.execute_query()

    file = ctx.web.get_file_by_id(file_id)
    ctx.load(file)
    ctx.execute_query()

    file_content = BytesIO()
    file.download(file_content).execute_query()
    file_content.seek(0)

    excel_file = pd.ExcelFile(file_content)
    
    sheets = [sheet for sheet in excel_file.sheet_names if sheet.startswith('Objetivo - ')]
    
    dfs = []
    
    month_map = {
        'JAN': '01', 'FEV': '02', 'MAR': '03', 'ABR': '04',
        'MAI': '05', 'JUN': '06', 'JUL': '07', 'AGO': '08',
        'SET': '09', 'OUT': '10', 'NOV': '11', 'DEZ': '12'
    }
    
    for sheet in sheets:
        year = sheet.split('- ')[1].strip()
        
        df = pd.read_excel(file_content, sheet_name=sheet)
        
        df.columns = df.columns.str.strip()
        
        df = df.iloc[1:, :17]
        
        df = df.drop(['META ANUAL', 'Média', 'BU'], axis=1)
        
        df['Tipo'] = df['PDV'].apply(lambda x: 'Pos Vendas' if str(x).strip().endswith('Pos Vendas') else 'Vendas')

        df['NM_PONTO_VENDA'] = None
        df['departamento'] = 'Vendas'
        
        df['sistemaorigem'] = df['Cod_empresa_sys'].str.extract(r'([A-Za-z]+)')
        df['codempresa'] = df['Cod_empresa_sys'].str.extract(r'(\d+)')
        
        id_vars = ['PDV', 'Cod_empresa_sys', 'Tipo', 'NM_PONTO_VENDA', 'departamento', 'sistemaorigem', 'codempresa']
        value_vars = [col for col in df.columns if col not in id_vars]
        
        # Transpõe as colunas de meses para linhas
        df = pd.melt(df, 
                     id_vars=id_vars,
                     value_vars=value_vars,
                     var_name='Mes',
                     value_name='Valor')
        
        df['Data'] = df['Mes'].apply(lambda x: f"01/{month_map[x[:3]]}/{year}")
        df['Data'] = pd.to_datetime(df['Data'], format='%d/%m/%Y')
        
        df = df.dropna(subset=['Cod_empresa_sys'])
        df = df.drop(['Cod_empresa_sys'], axis=1)
        df['Valor'] = pd.to_numeric(df['Valor'], errors='coerce')

        dfs.append(df)
 
    final_df = pd.concat(dfs, ignore_index=True)
    
    self._structure_check = "empty"
    self.schema = "dbdwcorporativo"
    self.obj_name = "planilha_metas_consorcio"
    self.data = final_df

    prepare_table(self)

    insert_data_in_chunks(self, self.data)
        
    return None

#endregion

#region Contas_a_Receber Newcon

def freeze_dados_consolidados_contas_a_receber(**kwargs):
    self = kwargs.get("self")
    self.cur.execute("""
        with dias_uteis as (
            select
            cal.data
            ,cal.periodo
            ,row_number() over (order by cal.data) as rn
        from
            dbdwcorporativo.aux_calendario cal
        left join
            (
                select distinct
                    cast(dia_feriado as date) dia_feriado
                    -- ,hora_inicio
                    -- ,hora_final
                from
                    dbdwcorporativo.silver_empresaferiado
                where
                    tipo in ('CNV', 'NAC')
                    and codigo = '0001'
            ) as fer on
            fer.dia_feriado = cal.data
        where
            cal.ano = EXTRACT(YEAR FROM CURRENT_TIMESTAMP)
            and cal.mes = EXTRACT(MONTH FROM CURRENT_TIMESTAMP)
            and case
                    when cal.fimdesemana = 1 or fer.dia_feriado is not null then 0
                    else 1
                end = 1
        )

        select data from dias_uteis where rn = 2
    """)
    data = self.cur.fetchone()
    segundo_dia_util = data[0]

    hoje = date.today()
    time_execution = (datetime.now(ZoneInfo("America/Sao_Paulo")))
    
    print(f'Segundo dia útil do mês:', segundo_dia_util)
    print(f'Data atual:', hoje)
    print(f'Hora atual:', time_execution)


    if hoje == segundo_dia_util and time_execution in (16, 17):

        self._sys = "newcon"
        conn = get_conn(self)
        self.query = '''
            select
                cast(getdate() as date) data_posicao
                ,cr.id_cota
                ,cr.cd_cota
                ,cr.versao
                ,cr.cd_grupo
                ,case
                    when congr001f.st_situacao = 'A' then 'ANDAMENTO'
                    when congr001f.st_situacao = 'F' then 'FORMACAO'
                    when congr001f.st_situacao in ('E','Z','Y') then 'ENCERRADO'
                    else 'ERRO'
                end situacao_grupo
                ,cr.contemplacao
                ,cr.nm_situacao_cobranca
                ,cr.cd_situacao_cobranca
                ,sum(no_parcelavencidas) qtd_parcelas_vencidas
                ,sum(no_parcelaavencer) qtd_parcelas_a_vencer
                ,count(*) qtd_parcelas_pagas
                ,cast(sum(cr.vlfundocumumvencido) as numeric(18,2)) vlfundocomumvencido
                ,cast(sum(cr.vltaxaadministracaovencido) as numeric(18,2)) vltaxaadministracaovencido
                ,cast(sum(cr.vlsegurovencido) as numeric(18,2)) vlsegurovencido
                ,cast(sum(cr.vlmultajurovencido) as numeric(18,2)) vlmultajurovencido
                ,cast(sum(cr.vlfundocumumavencer) as numeric(18,2)) vlfundocomumavencer
                ,cast(sum(cr.vltaxaadministracaoavencer) as numeric(18,2)) vltaxaadministracaoavencer
                ,cast(sum(cr.vlseguroavencer) as numeric(18,2)) vlseguroavencer
                ,cast(sum(cr.vlmultajuroavencer) as numeric(18,2)) vlmultajuroavencer
                ,cast(sum(cr.vlcontribuicaomensalavencer) as numeric(18,2)) vlcontribuicaomensalavencer
                ,cast(sum(cr.outrosvaloresapagar) as numeric(18,2)) outrosvaloresapagar
                ,cast(sum(cr.valordiferençaapagar) as numeric(18,2)) valordiferencaapagar
                ,cast(max(cr.vl_bem_atual) as numeric(18,2)) vl_bem_atual
                ,cast(max(cr.valorapagar) as numeric(18,2)) valorapagar
            from
                tempRelatorioParcelarNaoPagas cr
                inner join
                    conve002 on
                    conve002.id_cota = cr.id_cota
                inner join
                    congr001 on
                    congr001.id_grupo = conve002.id_grupo
                inner join
                        congr001f on
                    congr001f.id_congr001f = congr001.id_congr001f
            where cr.id_cota = 155980
            group by
                cr.id_cota
                ,cr.cd_cota
                ,cr.versao
                ,cr.cd_grupo
                ,case
                    when congr001f.st_situacao = 'A' then 'ANDAMENTO'
                    when congr001f.st_situacao = 'F' then 'FORMACAO'
                    when congr001f.st_situacao in ('E','Z','Y') then 'ENCERRADO'
                    else 'ERRO'
                end
                ,cr.contemplacao
                ,cr.nm_situacao_cobranca
                ,cr.cd_situacao_cobranca'''
        
        df = pd.read_sql(self.query, conn)
        
        self.schema = "dbdwcorporativo"
        self.obj_name = "freeze_newcon_contas_a_receber_sumarizado"
        print(df.head())
        self.data = df
        insert_data_in_chunks(self, self.data)
    else:
        print("Hoje não é o segundo dia útil. Encerrando.")

#endregion
