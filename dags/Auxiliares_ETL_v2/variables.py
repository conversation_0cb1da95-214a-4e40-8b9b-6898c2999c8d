from datetime import timedelta
from Global_Vars.Conn_vars import *

# Schema para a criação e inserção de registros do ETL
SCHEMA_CRIACAO_INSERT = 'dbdwcorporativo'

# Usuarios com permissoes gerais nas tabelas
all_grants = ["master", "iebt_daniel_laranjo", "ext_leticia_furletti", "bq_dwcorporativo_u", "ext_rafael_neto"]
read_all_grants = ["bq_dw_read_u", "bq_apipass_u"]
specific_grants = ["bq_leonardo_nunes"]
specific_grants_sync_tables = ["bq_im_powerbi_u","bq_lucas_ulhoa_u","bq_vitor_barros_u", "bq_kelvin_santos"]

# Colunas que devem ser criada como tipo texto
text_columns = []

# Frequência de atualização padrão
times_per_day_default = 3

# Intervalo inicial das dags
schedule_interval = '10 6,13,15,17,19 * * *'

# Argumentos para as dags
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    # 'email': ['<EMAIL>'], 
    # 'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1)
}

sys_mapping = {
    "syonet": "ssms",
    "newcon": "ssms",
    "quiver": "ssms",
    "sge": "ssms",
    "sync": "ssms",
    "dealer": "ssms",
    "dw_bamaq": "ssms",
    "dw_corporativo": "postgres",
    "koneq": "ssms",
    "corpore": "ssms",
    "nbs": "oracle"
}