# Importações padrão da biblioteca Python
import logging
from logging.handlers import RotatingFileHandler
from datetime import date, datetime, timezone
try:
    from zoneinfo import ZoneInfo
except ImportError:
    from backports.zoneinfo import ZoneInfo
import time
import re
import unicodedata
from io import StringIO, BytesIO
import traceback
from typing import Optional
import os
import sys
import zipfile
import glob

# Bibliotecas de fuso horário e manipulação de datas
import pytz
from dateutil import parser

# Bibliotecas para manipulação de dados
import pandas as pd

# Conexões com bancos de dados
import psycopg2
from psycopg2.extras import execute_values
from psycopg2 import sql
from psycopg2.errors import DatetimeFieldOverflow
import cx_Oracle
import pymssql

# Selenium
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options

# Obtém o nome do script que foi executado
executing_script = os.path.basename(sys.argv[0])

# Só importa se o script rodando não for "LocalRun.py"
if executing_script != "LocalRun.py":
    
    # Airflow
    from airflow.utils.state import State
    from airflow.utils.timezone import make_aware, is_naive
    from airflow.operators.empty import EmptyOperator
    from airflow.operators.python import PythonOperator, get_current_context
    from airflow.exceptions import AirflowSkipException
    from airflow.operators.trigger_dagrun import TriggerDagRunOperator
    from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook

    # Apache Spark
    from pyspark.sql import SparkSession
    
    # Bibliotecas para manipulação de arquivos e protocolos
    import smbclient
    from smbclient import register_session, scandir, open_file
    from smbprotocol.exceptions import SMBException

    # Google BigQuery
    from google.cloud import bigquery


# Serviços na nuvem
from azure.storage.blob import BlobServiceClient

# SharePoint
from office365.runtime.auth.authentication_context import AuthenticationContext
from office365.sharepoint.client_context import ClientContext
from office365.sharepoint.files.file import File

# HTTP requests
import requests

# Variáveis e consultas personalizadas
from Auxiliares_ETL_v2.variables import *
from Global_Vars.Queries import Query

# Configura o logger para rodar os logs
handler = RotatingFileHandler(
    'etl_log.log', maxBytes=10**7, backupCount=5, encoding='utf-8'
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[handler, logging.StreamHandler()]
)

# Silencie logs detalhados da biblioteca SMB
logging.getLogger("smbprotocol").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

def get_jdbc_url(dbname):
    logger.info(f"Getting JDBC URL for database {dbname}")

    if dbname == "dw_corporativo":
        jdbc_url = f"jdbc:postgresql://{DW_CORPORATIVO_HOST}:{DW_CORPORATIVO_PORT}/{DW_CORPORATIVO_DBNAME}"
        driver = "org.postgresql.Driver"
        user = DW_CORPORATIVO_USERNAME
        password = DW_CORPORATIVO_PASSWORD
    elif dbname == "koneq":
        jdbc_url = f"jdbc:postgresql://{KONEQ_HOST}:{KONEQ_PORT}/{KONEQ_DBNAME}"
        driver = "org.postgresql.Driver"
        user = KONEQ_USERNAME
        password = KONEQ_PASSWORD
    elif dbname == "nbs":
        jdbc_url = f"jdbc:oracle:thin:@{NBS_HOST}:{NBS_PORT}/{NBS_SERVICE_NAME}"
        driver = "oracle.jdbc.OracleDriver"
        user = NBS_USERNAME
        password = NBS_PASSWORD
    elif dbname == "syonet":
        jdbc_url = f"jdbc:sqlserver://{SYONET_HOST}:{SYONET_PORT};databaseName={SYONET_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = SYONET_USERNAME
        password = SYONET_PASSWORD
    elif dbname == "newcon":
        primary_url = f"jdbc:sqlserver://{NEWCON_HOST}:{NEWCON_PORT};databaseName={NEWCON_DBNAME}"
        secondary_url = f"jdbc:sqlserver://{NEWCON_HOST_2}:{NEWCON_PORT};databaseName={NEWCON_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = NEWCON_USERNAME
        password = NEWCON_PASSWORD

        try:
            # Tentativa de conexão no servidor primário
            logger.info(f"Trying to connect to primary server: {NEWCON_HOST}")
            conn = pymssql.connect(server=NEWCON_HOST, user=user, password=password, database=NEWCON_DBNAME, port=NEWCON_PORT)
            conn.close()
            jdbc_url = primary_url
        except pymssql.OperationalError:
            logger.info(f"Primary server unavailable. Switching to secondary server: {NEWCON_HOST_2}")
            jdbc_url = secondary_url
    elif dbname == "dealer":
        jdbc_url = f"jdbc:sqlserver://{DEALER_HOST}:{DEALER_PORT};databaseName={DEALER_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = DEALER_USERNAME
        password = DEALER_PASSWORD
    elif dbname == "space":
        jdbc_url = f"jdbc:sqlserver://{SPACE_HOST}:{SPACE_PORT};databaseName={SPACE_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = SPACE_USERNAME
        password = SPACE_PASSWORD    
    elif dbname == "quiver":
        jdbc_url = f"jdbc:sqlserver://{QUIVER_HOST}:{QUIVER_PORT};databaseName={QUIVER_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = QUIVER_USERNAME
        password = QUIVER_PASSWORD
    elif dbname == "dw_bamaq":
        primary_url = f"jdbc:sqlserver://{DW_BAMAQ_HOST}:{DW_BAMAQ_PORT};databaseName={DW_BAMAQ_DBNAME}"
        secondary_url = f"jdbc:sqlserver://{DW_BAMAQ_HOST_2}:{DW_BAMAQ_PORT};databaseName={DW_BAMAQ_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = DW_BAMAQ_USERNAME
        password = DW_BAMAQ_PASSWORD

        try:
            # Tentativa de conexão no servidor primário
            logger.info(f"Trying to connect to primary server: {DW_BAMAQ_HOST}")
            conn = pymssql.connect(server=DW_BAMAQ_HOST, user=user, password=password, database=DW_BAMAQ_DBNAME, port=DW_BAMAQ_PORT)
            conn.close()
            jdbc_url = primary_url
        except pymssql.OperationalError:
            logger.info(f"Primary server unavailable. Switching to secondary server: {DW_BAMAQ_HOST_2}")
            jdbc_url = secondary_url
    elif dbname == "corpore":
        jdbc_url = f"jdbc:sqlserver://{CORPORE_HOST}:{CORPORE_PORT};databaseName={CORPORE_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = CORPORE_USERNAME
        password = CORPORE_PASSWORD
    elif dbname == "sync":
        jdbc_url = f"jdbc:sqlserver://{SYNC_HOST}:{SYNC_PORT};databaseName={SYNC_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = SYNC_USERNAME
        password = SYNC_PASSWORD
    elif dbname == "sge":
        jdbc_url = f"jdbc:sqlserver://{SGE_HOST}:{SGE_PORT};databaseName={SGE_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = SGE_USERNAME
        password = SGE_PASSWORD
    elif dbname == "netadmin":
        jdbc_url = f"jdbc:sqlserver://{NETADMIN_HOST}:{NETADMIN_PORT};databaseName={NETADMIN_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = NETADMIN_USERNAME
        password = NETADMIN_PASSWORD
    elif dbname == "netadmin_aud":
        jdbc_url = f"jdbc:sqlserver://{NETADMIN_HOST}:{NETADMIN_PORT};databaseName={NETADMIN_AUD_DBNAME}"
        driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user = NETADMIN_USERNAME
        password = NETADMIN_PASSWORD
    else:
        raise ValueError(f"Unsupported database type: {dbname}")

    logger.info(f"JDBC URL for {dbname} obtained successfully")
    return jdbc_url, driver, user, password

def get_conn(self):
    """
    Establish connection based on the sys name.
    
    Args:
        dbname (str): The name of the sys to connect to.

    Returns:
        Connection object: A connection object for the specified sys.
    """
    if self._type == 'planilha' or self.config.get("conn_type") == "planilha":
        dbname = self.config.get("site_url", SITE_URL)
        
    else:
        dbname = self._sys

    logger.info(f"Establishing connection for sys: {dbname}")
    if self.config.get("conn_type") == 'api' and self.config.get("token"):
        self._token = self.config.get("token")
        return

    elif self.config.get("conn_type") == 'api':
        token_request = self.config.get("requests").get("token")
        response = requests.post(
            token_request.get("url"), 
            json=token_request.get("payload"), 
            headers=token_request.get("headers")
        )

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Obter o JSON retornado
            data = response.json()
            self._token = data.get(token_request.get("fieldname"))
            return
        else:
            logger.error(
                f"Erro na requisição do token: {response.status_code} - {response.text}",
                exc_info=True
            )
            response.raise_for_status()
                    
    elif self._type == 'planilha' or self.config.get("conn_type") == "planilha":
        ctx_auth = AuthenticationContext(dbname)
        if ctx_auth.acquire_token_for_user(
                    self.config.get("username", SHAREPOINT_USERNAME), 
                    self.config.get("password", SHAREPOINT_PASSWORD)
                ):
            conn = ClientContext(dbname, ctx_auth)
    elif dbname == "syonet":
        conn = pymssql.connect(server=SYONET_HOST, user=SYONET_USERNAME, password=SYONET_PASSWORD, database=SYONET_DBNAME, port=SYONET_PORT)
    elif dbname == "newcon":
        #Trecho para conexão com os bancos Primarios e Secundarios identificando qual servidor está ativo no momento            
        try:
            logger.info(f"Trying to connect to primary server: {NEWCON_HOST_NAME}") 
            #Tentativa de conexão com o Batman
            conn = pymssql.connect(server=NEWCON_HOST, user=NEWCON_USERNAME, password=NEWCON_PASSWORD, database=NEWCON_DBNAME,port=NEWCON_PORT)
            logger.info(f"Connected to primary server: {NEWCON_HOST_NAME}")
        except pymssql.OperationalError as e:
            logger.error(f"Error establishing connection for database: {NEWCON_HOST_NAME}", exc_info=True)
            logger.info(f"Trying to connect to secondary server: {NEWCON_HOST_NAME_2}")
            #Tentativa de conexão com o Jocker
            conn = pymssql.connect(server=NEWCON_HOST_2, user=NEWCON_USERNAME, password=NEWCON_PASSWORD, database=NEWCON_DBNAME,port=NEWCON_PORT)
            logger.info(f"Connected to secondary server: {NEWCON_HOST_NAME_2}") 
    elif dbname == "quiver":
        conn = pymssql.connect(server=QUIVER_HOST, user=QUIVER_USERNAME, password=QUIVER_PASSWORD, database=QUIVER_DBNAME, port=QUIVER_PORT)
    elif dbname == "sge":
        conn = pymssql.connect(server=SGE_HOST, user=SGE_USERNAME, password=SGE_PASSWORD, database=SGE_DBNAME, port=SGE_PORT)
    elif dbname == "sync":
        conn = pymssql.connect(server=SYNC_HOST, user=SYNC_USERNAME, password=SYNC_PASSWORD, database=SYNC_DBNAME, port=SYNC_PORT)
    elif dbname == "dealer":
        conn = pymssql.connect(server=DEALER_HOST, user=DEALER_USERNAME, password=DEALER_PASSWORD, database=DEALER_DBNAME, port=DEALER_PORT)
    elif dbname == "space":
        conn = pymssql.connect(server=SPACE_HOST, user=SPACE_USERNAME, password=SPACE_PASSWORD, database=SPACE_DBNAME, port=SPACE_PORT)
    elif dbname == "dw_bamaq":
        #Trecho para conexão com os bancos Primarios e Secundarios identificando qual servidor está ativo no momento
        try:
            logger.info(f"Trying to connect to primary server: {DW_BAMAQ_HOST_NAME}")
            #Tentativa de conexão com o Batman
            conn = pymssql.connect(server=DW_BAMAQ_HOST, user=DW_BAMAQ_USERNAME, password=DW_BAMAQ_PASSWORD, database=DW_BAMAQ_DBNAME,port=DW_BAMAQ_PORT)
            logger.info(f"Connected to primary server: {DW_BAMAQ_HOST_NAME}")
        except pymssql.OperationalError as e:
            logger.error(f"Error establishing connection for database: {DW_BAMAQ_HOST}", exc_info=True)
            #Tentativa de conexão com o Jocker
            logger.info(f"Trying to connect to secondary server: {DW_BAMAQ_HOST_NAME_2}")
            conn = pymssql.connect(server=DW_BAMAQ_HOST_2, user=DW_BAMAQ_USERNAME, password=DW_BAMAQ_PASSWORD, database=DW_BAMAQ_DBNAME,port=DW_BAMAQ_PORT)
            logger.info(f"Connected to secondary server: {DW_BAMAQ_HOST_NAME_2}") 
    elif dbname == "dw_corporativo":
        conn = psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST)
    elif dbname == "koneq":
        conn = psycopg2.connect(dbname=KONEQ_DBNAME, user=KONEQ_USERNAME, password=KONEQ_PASSWORD, host=KONEQ_HOST,port=KONEQ_PORT)
    elif dbname == "corpore":
        conn = pymssql.connect(server=CORPORE_HOST, user=CORPORE_USERNAME, password=CORPORE_PASSWORD, database=CORPORE_DBNAME, port=CORPORE_PORT)
    elif dbname == "nbs":
        dsn_tns = cx_Oracle.makedsn(NBS_HOST, NBS_PORT, service_name=NBS_SERVICE_NAME)
        conn = cx_Oracle.connect(user=NBS_USERNAME, password=NBS_PASSWORD, dsn=dsn_tns)
    elif dbname == "netadmin":
        conn = pymssql.connect(server=NETADMIN_HOST, user=NETADMIN_USERNAME, password=NETADMIN_PASSWORD, database=NETADMIN_DBNAME, port=NETADMIN_PORT)
    elif dbname == "netadmin_aud":
        conn = pymssql.connect(server=NETADMIN_HOST, user=NETADMIN_USERNAME, password=NETADMIN_PASSWORD, database=NETADMIN_AUD_DBNAME, port=NETADMIN_PORT)
    logger.info(f"Connection established for database: {dbname}")
    return conn

def get_max_value(self):
    """
    Get the maximum value of the specified column from the table.
    
    Args:
        id_columns (list): List of ID column names.
        date_columns (list): List of date column names.
        schema (str): The database schema.
        table_name (str): The target table name.
        cur (Cursor object): The database cursor.

    Returns:
        str: The maximum value of the specified column.
    """
    logger.info(f"Fetching max value for table: {self.schema}.{self.obj_name}")
    column = self.date_columns[0] if self.date_columns else self.id_columns[0]
    query = f"SELECT MAX({column}) FROM {self.schema}.{self.obj_name}"
    logger.info(f"Executing query: {query}")
    self.cur.execute(query)
    data = self.cur.fetchone()
    max_value = data[0] if data else None
    logger.info(f"Max value for column {column} in table {self.schema}.{self.obj_name}: {max_value}")
    return max_value

def set_incremental_query(self):
    self.origin_table_name = self.config.get('_origin_table_name')
    self.date_columns = self.config.get("date_columns")
    # Coletando maior valor na tabela de destino, para usar de referência ao construir a query para a tabela de origem
    last_max_value = get_max_value(self)
    
    where_clause = []
    if self.date_columns:
        formatted_value = (f"TO_DATE('{last_max_value}', 'YYYY-MM-DD HH24:MI:SS')" if self._sys == "nbs" else
                        f"'{int(last_max_value)}'" if self._sys == "syonet" and self.origin_table_name != "syo_empresa" else
                        f"CAST('{last_max_value}'AS DATE)" if self._sys == "newcon" else
                        f"'{last_max_value}'")
        where_clause = [f"{col} >= {formatted_value}" for col in self.date_columns]
    else:
        where_clause = [f"{self.id_columns[0]} > {last_max_value}"]
    self._query += f" WHERE {' OR '.join(where_clause)}"

def fetch_data_in_chunks(self, type=None):
    conn_type = self.config.get("conn_type") if self.config.get("conn_type") else self._type
    nrows = 10 if type == 'sample' else None
    if conn_type == "api":
        request = self.request
        vars = {
            'token': self._token
        }
        if hasattr(self, 'vars'):
            vars = {
                **vars,
                **self.vars
            }

        request = substituir_placeholders_em_dicionarios(request, vars)

        response = requests.request(
            request.get("type"),
            request.get("url"), 
            json=request.get("payload"), 
            headers=request.get("headers"), 
            params=request.get("params"),
            **request.get("kwargs", {})
            )
        if response.status_code == 200:
            yield response.json()
        else:
            logger.error(
                f"Erro na requisição: {response.status_code} - {response.text}",
                exc_info=True
            )
            response.raise_for_status()
        
    elif conn_type == "file_system":
        entry = self.entry
        file_path = self.file_path
    
        print(f"Entry: {entry.name}")
        # se for .txt mas não for o fluxo -25.txt, já sai sem definir df nem dar yield:
        if entry.name.lower().endswith('.txt') and not entry.name.lower().endswith('-25.txt'):
           logger.info(f"Pulando (não -25.txt): {entry.name}")
           return
        fp = f"{file_path}\{entry.name}"
        if entry.name.endswith(('.csv', '.dat')):
            with open_file(fp, mode='r', encoding='utf-8') as file_obj:
                df = pd.read_csv(file_obj, 
                                    delimiter=';', 
                                    dtype=self.dtype,
                                    nrows=nrows
                                    )

        elif entry.name.endswith('xlsx'):
            with open_file(fp, mode='rb') as file_obj:
                df = pd.read_excel(file_obj, 
                                    sheet_name=self.tab_name,
                                    dtype=self.dtype,
                                    nrows=nrows
                                    )
                
        elif entry.name.endswith('-25.txt'): ##Fluxo exclusivo conta contabil
            with open_file(fp, mode='r', encoding='utf-8') as file_obj:
                df = pd.read_csv(file_obj, 
                                    delimiter='|', 
                                    dtype=self.dtype,
                                    nrows=nrows,
                                    names=[
                                    'Tipo', 'Categoria', 'Ano', 'Mes', 'Empresa', 'CNPJ',
                                    'Centro_Custo', 'Produto_Canal', 'Local', 'Lancamento',
                                    'Conta', 'Valor', 'Data', 'Referencias', 'Descricao',
                                    'Beneficiario', 'Detalhe'
                                    ],
                                    parse_dates=['Data'],
                                    engine='python' ,     
                                    on_bad_lines='skip'   
                                    )               
        yield df

    elif conn_type == "planilha":
        if self.config.get("file_id"):
            file = self.origin_conn.web.get_file_by_id(self.config.get("file_id")) 
        elif self.config.get("folder_id"):
            folder = self.origin_conn.web.get_folder_by_id(self.config.get("folder_id"))
            file = folder.files.get_by_url(self.config.get("file_name"))
        self.origin_conn.load(file)
        self.origin_conn.execute_query()
        file_stream = BytesIO(file.read())
        df = pd.read_excel(
                file_stream, 
                sheet_name=self.config.get("tab_name", 0),
                skiprows=self.config.get("skiprows", 0),
                dtype=self.config.get("dtype"),
                nrows=nrows
            )
        if self.config.get("column_names"):
            df.columns = self.config.get("column_names")
            
        logger.info(f"Fetched dataframe with {len(df)} records")
        yield df
    else:
        logger.info(f"Fetching data in chunks with query: {self._query}")
        for chunk in pd.read_sql_query(self._query, 
                                        self.origin_conn, 
                                        chunksize=10 if type == 'sample' else self.chunksize
                                        ):
            logger.info(f"Fetched chunk with {len(chunk)} records")
            yield chunk

def transform_data(self):
    if self._type == "freeze":
        data_posicao = (datetime.now(pytz.timezone("America/Sao_Paulo")) - timedelta(days=1)).date()
        self.data.columns = [col.lower() for col in self.data.columns]

        # Adicionar a coluna data_posicao ao dataframe
        self.data['data_posicao'] = data_posicao

def remove_prefix(prefix, col_name):
    if prefix and col_name.startswith(prefix):
        return col_name[len(prefix)+1:]
    return col_name

def normalize_column_name(col_name):
    """
    Normaliza o nome das colunas:
    - Remove acentos
    - Substitui espaços e caracteres especiais por '_'
    - Converte para minúsculas
    """
    col_name = unicodedata.normalize('NFKD', str(col_name)).encode('ascii', 'ignore').decode('utf-8')
    if col_name.isdigit():
        col_name = f"coluna{col_name}"
    col_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
    return col_name.lower()

def debug_lambda(x):
    """
    Trata valores ao converter para string, com suporte a bytes e valores NaN.
    """
    if isinstance(x, bytes):
        return x.decode('utf-8', errors='ignore')
    elif isinstance(x, str):
        return x
    elif pd.isnull(x):
        return ''
    return str(x)

def get_pg_type(col_data, default_type='text'):
    """
    Determina o tipo PostgreSQL com base nos dados da coluna.
    """
    if col_data.empty:
        return default_type
    if col_data.apply(lambda x: isinstance(x, str)).all():
        max_length = col_data.str.len().max()
        if max_length <= 100:
            return 'varchar(100)'
        elif max_length <= 150:
            return 'varchar(150)'
        elif max_length <= 200:
            return 'varchar(200)'
        return 'text'
    return default_type

def map_data_type(col_name, col_type, df, column_types):
    """
    Determina o tipo PostgreSQL de uma coluna com base no tipo Pandas e opções customizadas.
    """
    
    # Dicionário de tipos de dados do PostgreSQL
    pg_data_types = {
        'int64': 'double precision',
        'float64': 'double precision',
        'object': 'text',
        'datetime64[ns]': 'timestamp',
        'bool': 'varchar(15)'
    }

    if column_types and col_name.lower() in column_types:
        return column_types[col_name.lower()]
    elif col_name.lower().startswith("data_"):
        return "timestamp"
    elif str(col_type) == 'object':
        try:
            col_data = df[col_name].fillna('').apply(debug_lambda)
            return get_pg_type(col_data)
        except Exception as e:
            print(f"Erro ao mapear tipo para coluna {col_name}: {e}")
            return 'text'
    return pg_data_types.get(str(col_type), 'text')

def log_column_types(df):
    """
    Loga os tipos de dados encontrados no DataFrame.
    """
    print("Tipos de dados encontrados no DataFrame:")
    print(df.dtypes.value_counts())
    print("\nExemplo de dados:")
    print(df.head())

def dataframe_to_create_table_query(self):
    """
    Gera uma query SQL para recriar uma tabela PostgreSQL com base em um DataFrame.
    """
    df = self.data
    table_name = f"{self.schema}.{self.obj_name}"
    column_types = self._column_types
    # Normaliza os nomes das colunas
    df.columns = [normalize_column_name(col) for col in df.columns]
    
    # # Loga os tipos de dados (opcional)
    # log_column_types(df)
    
    # Inicializa a lista de colunas
    columns = []
    
    for col_name, col_type in df.dtypes.items():
        pg_type = map_data_type(col_name, col_type, df, column_types)
        columns.append(f'"{col_name.replace(" ", "").lower()}" {pg_type}')
    
    columns_str = ",\n    ".join(columns)
    
    # Monta a query de criação da tabela
    create_table_query = f"DROP TABLE IF EXISTS {table_name};\nCREATE TABLE {table_name} (\n    {columns_str}\n);\n"

    # Get permissions from aux_permissions_tables
    self.cur.execute(f"""
        SELECT username, table_name, grant_type, active 
        FROM dbdwcorporativo.aux_permissions_tables 
        WHERE (table_name = '*' OR lower('{table_name}') LIKE '%' || lower(table_name) || '%') 
        AND active = true
    """)
    permissions = self.cur.fetchall()
    
    # Add permissions to grant_query
    for user, table, permission_type, active in permissions:
        create_table_query += f"{permission_type} ON TABLE {table_name} TO {user};\n"
        
    return create_table_query

def compare_data_structure(self):
     # Consulta única para verificar existência e obter as colunas
    query_existing_columns = f"""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = '{self.obj_name}' AND table_schema = '{self.schema}'
        ORDER BY ordinal_position;
    """
    existing_columns = pd.read_sql_query(query_existing_columns, self.target_conn)
    self.existing_columns_list = [col.lower() for col in existing_columns['column_name'].tolist()]

    self.data = next(fetch_data_in_chunks(self, 'sample'))
    transform_data(self)
    self.new_columns_list = [col.lower() for col in self.data.columns.tolist()]   

    if existing_columns.empty:
        self._structure_check = "empty"

    elif self.existing_columns_list != self.new_columns_list:
        self._structure_check = "diff"

    else:
        self._structure_check = "equal"

def prepare_table(self):
    if self._structure_check == "empty":
        # Caso a tabela não exista
        logger.info(f"Table {self.schema}.{self.obj_name} does not exist. Creating it...")
        create_table_query = dataframe_to_create_table_query(self)
        logger.info(f"Creating table with statement:\n{create_table_query}")
        self.cur.execute(create_table_query)
        logger.info(f"Table {self.schema}.{self.obj_name} successfully created.")

    elif self._structure_check == "diff":
        if self._type == 'freeze':
            error_message = (
                f"Structure mismatch detected for table {self.schema}.{self.obj_name}. Please drop/alter the table before proceeding... "
                f"Existing columns: {self.existing_columns_list}"
                f"New columns: {self.new_columns_list}"
            )
            logger.error(error_message)
            raise ValueError(error_message)
        else:
            if self.config.get("_index"):
                for idx_name, idx_columns in self.config.get("_index").items():
                    if idx_name and idx_columns:
                        check_query = f"""
                        SELECT EXISTS (
                            SELECT 1
                            FROM pg_indexes 
                            WHERE schemaname = '{self.schema}' 
                            AND tablename = '{self.obj_name}' 
                            AND indexname = '{idx_name}'
                        );
                        """
                        self.cur.execute(check_query)
                        index_exists = self.cur.fetchone()[0]
                        
                        if index_exists:
                            idx_query = f"DROP INDEX {self.schema}.{idx_name};"
                            try:
                                self.cur.execute(idx_query)
                                logger.info(f"Successfully dropped index with: {idx_query}")
                            except Exception as e:
                                logger.error(f"Failed to drop index with: {idx_query}. Error: {e}")

            logger.info(f"Structure mismatch detected for table: {self.obj_name}")
            create_table_query = dataframe_to_create_table_query(self)
            logger.info(f"Recreating table with statement:\n{create_table_query}")
            self.cur.execute(create_table_query)

    if self.config.get("_index"):
        for idx_name, idx_columns in self.config.get("_index").items():
            if idx_name and idx_columns:
                idx_query = f"CREATE INDEX {idx_name} ON {self.schema}.{self.obj_name} ({', '.join(idx_columns)});"
                try:
                    self.cur.execute(idx_query)
                    logger.info(f"Successfully created index with: {idx_query}")
                except Exception as e:
                    logger.error(f"Failed to create index with: {idx_query}. Error: {e}")
    
def delete_from_destin(self):
    logger.info(f"Deleting data from table: {self.schema}.{self.obj_name}")
    self.data.columns = self.data.columns.str.lower()

    where_clause_parts = []
    if self.id_columns:
        self.id_columns = [col.lower() for col in self.id_columns]
        missing_columns = [col for col in self.id_columns if col not in self.data.columns]
        if missing_columns:
            raise KeyError(f"Missing columns in DataFrame: {missing_columns}")
        elif len(self.id_columns) > 1:
            for _, row in self.data.iterrows():
                row_where_parts = []
                for col in self.id_columns:
                    if col in row:
                        row_where_parts.append(f"{col} = '{row[col]}'")
                    else:
                        raise KeyError(f"Column '{col}' not found in DataFrame row")
                where_clause_parts.append(f"({' AND '.join(row_where_parts)})")
            where_clause = " OR ".join(where_clause_parts)
        else:
            col = self.id_columns[0]
            if col in self.data:
                ids_to_delete = ', '.join(f"'{str(id)}'" for id in self.data[col].tolist())
                where_clause = f"{col} IN ({ids_to_delete})"
            else:
                raise KeyError(f"Column '{col}' not found in DataFrame")
        
        attempt_delete = True
        while attempt_delete:
            try:
                self.cur.execute(f"SAVEPOINT before_insert_{self.obj_name}")
                query = f"DELETE FROM {self.schema}.{self.obj_name} WHERE {where_clause}"
                logger.info(f"Executing delete query: {query}")
                self.cur.execute(query)
                attempt_delete = False
                logger.info(f"Deleted chunk with {len(self.data)} records from {self.schema}.{self.obj_name}")
            except psycopg2.errors.InvalidTextRepresentation as e:
                logger.warning(f"InvalidTextRepresentation encountered: {str(e)}. Attempting to clean data and retry.")
                self.cur.execute(f"ROLLBACK TO SAVEPOINT before_insert_{self.obj_name}")
                for column_name in self.id_columns:
                    alter_table_query = f'ALTER TABLE {self.schema}.{self.obj_name} ALTER COLUMN "{column_name}" TYPE double precision;'
                    self.cur.execute(alter_table_query)
                    logger.info(f"Altered column {column_name} to double precision in table {self.schema}.{self.obj_name}. Retrying delete.")

    elif self._type == 'freeze':
        if self._scale:
            logger.info(f"Table {self.schema}.{self.obj_name} structure matches. Will not delete any data.")
        else:
            # Caso a tabela exista e a estrutura seja idêntica
            logger.info(f"Table {self.schema}.{self.obj_name} structure matches. Proceeding with deletion...")
            delete_query = f"""
                DELETE FROM {self.schema}.{self.obj_name}
                WHERE 
                    EXTRACT(MONTH FROM data_posicao) = EXTRACT(MONTH FROM CURRENT_DATE - INTERVAL '1 day')
                    AND EXTRACT(YEAR FROM data_posicao) = EXTRACT(YEAR FROM CURRENT_DATE - INTERVAL '1 day');
            """
            self.cur.execute(delete_query)
        
    else:
        logger.info(f"Table {self.schema}.{self.obj_name} structure matches. Proceeding with truncation...")
        self.cur.execute(f'truncate table {self.schema}.{self.obj_name}')    

def insert_data_in_chunks(self, data, column_list = None):
    """
    Insert data into the database in chunks.

    Args:
        data (DataFrame): The data to insert.
        table (str): The target table for the data.
        cur (Cursor object): The database cursor.
        chunksize (int): The size of each data chunk to insert.
    """
    table = f'{self.schema}.{self.obj_name}'
    cur = self.cur
    chunksize = self.chunksize
    column_list = f" ({column_list})" if column_list else  ""
    def insert_chunk(chunk, table, cur, use_quotes=False):
        with StringIO() as sio:
            if use_quotes:
                for col in chunk.columns:
                    if chunk[col].dtype == 'object':
                        chunk.loc[:, col] = chunk[col].apply(lambda x: f'"{x}"' if pd.notnull(x) else x)
                chunk.to_csv(sio, index=None, header=None, quotechar='"', quoting=3, escapechar='\\')
            else:
                chunk.to_csv(sio, index=None, header=None)
            sio.seek(0)
            quote_option = " QUOTE '\"'" if use_quotes else ""
            cur.copy_expert(f"COPY {table}{column_list} FROM STDIN WITH CSV{quote_option}", sio)

    def insert_chunk_with_bytes_conversion(chunk, table, cur, use_quotes=False):
        for col in chunk.columns:
            if chunk[col].dtype == 'object':
                try:
                    chunk[col] = chunk[col].apply(lambda x: x.read() if isinstance(x, cx_Oracle.LOB) else x.decode('utf-8') if isinstance(x, bytes) else str(x) if x is not None else x)
                except Exception as e:
                    logger.warning(f"Failed to convert bytes in column {col}. Error: {e}")
        insert_chunk(chunk, table, cur, use_quotes)

    def clean_data(chunk, column_name = None, alter_column = None):
        if column_name:
            alter_table_query = f'ALTER TABLE {table} ALTER COLUMN "{column_name}" TYPE {alter_column};'
            cur.execute(alter_table_query)
            logger.info(f"Altered column {column_name} to {alter_column} in table {table}. Retrying insert.")
        else:
            for col in chunk.columns:
                if chunk[col].dtype == 'object':
                    chunk[col] = chunk[col].apply(lambda x: x.replace('\r', '').replace('\n', ' ') if isinstance(x, str) else x)
                    chunk[col] = chunk[col].apply(lambda x: x.replace('"', '""') if isinstance(x, str) else x)

    def infer_and_convert_dates(df, column_name):
        """
        Infere o formato da data e converte os valores da coluna para datetime.
        """
        # Amostra de valores não nulos da coluna
        sample_data = df[column_name].dropna().head(10)
        
        # Tentar identificar o formato de data com base em exemplos
        inferred_format = None
        for value in sample_data:
            try:
                # Tentar inferir o formato explicitamente
                parsed_date = parser.parse(value, dayfirst=True)
                inferred_format = "%d/%m/%Y" if parsed_date.strftime("%d/%m/%Y") == value else "%d-%m-%Y"
                break
            except Exception as e:
                continue

        try:
            df[column_name] = pd.to_datetime(df[column_name], errors='coerce', format=inferred_format)
            logger.info(f"Inferred and applied date format {inferred_format} to column {column_name}.")
        except Exception as e:
            logger.error(f"Failed to apply inferred date format {inferred_format} to column {column_name}. Error: {str(e)}")
            df[column_name] = pd.NaT  # Configurar como valores nulos caso falhe

        # Log de diagnóstico para valores não convertidos
        null_count = df[column_name].isnull().sum()
        if null_count > 0:
            logger.warning(f"Column {column_name} has {null_count} null values after conversion.")

    logger.info(f"Inserting data into table: {table}")
    for i in range(0, len(data), chunksize):
        chunk = data.iloc[i:i + chunksize].copy()  # Create a copy to avoid SettingWithCopyWarning
        chunk.columns = chunk.columns.str.lower()
        attempt_insert = True
        use_quotes = False
        attempt_count = 0
        bytes_conversion_done = False
        first_clean = False

        while attempt_insert and attempt_count < 8:
            try:
                cur.execute(f"SAVEPOINT before_insert_{self.obj_name}")
                if not first_clean:
                    clean_data(chunk)
                    first_clean = True
                if bytes_conversion_done:
                    bytes_conversion_done = False
                    insert_chunk_with_bytes_conversion(chunk, table, cur, use_quotes)
                    logger.info(f"Inserted chunk with {len(chunk)} records into {table} after bytes conversion.")
                else:
                    insert_chunk(chunk, table, cur, use_quotes)
                    logger.info(f"Inserted chunk with {len(chunk)} records into {table}")
                attempt_insert = False
            except psycopg2.errors.BadCopyFileFormat as e:
                logger.warning(f"Error with default insert: {str(e)}. Retrying with quoted CSV.")
                cur.execute(f"ROLLBACK TO SAVEPOINT before_insert_{self.obj_name}")
                use_quotes = True
                attempt_count += 1
            except (psycopg2.errors.StringDataRightTruncation, psycopg2.errors.InvalidDatetimeFormat) as e:
                logger.error(f"StringDataRightTruncation encountered: {str(e)}. Attempting to alter table and retry.")
                cur.execute(f"ROLLBACK TO SAVEPOINT before_insert_{self.obj_name}")
                error_message = str(e).split("\n")
                column_name = None
                for line in error_message:
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip()
                        break
                if column_name:
                    alter_table_query = f"ALTER TABLE {table} ALTER COLUMN {column_name} TYPE TEXT;"
                    cur.execute(alter_table_query)
                    logger.info(f"Altered column {column_name} to TEXT in table {table}. Retrying insert.")
                else:
                    logger.error("Could not identify the problematic column for StringDataRightTruncation error.")
                    raise e
            except psycopg2.errors.DatetimeFieldOverflow as e:
                logger.error(f"DatetimeFieldOverflow encountered: {str(e)}. Attempting to clean column and retry.")
                cur.execute(f"ROLLBACK TO SAVEPOINT before_insert_{self.obj_name}")
                error_message = str(e).split("\n")
                column_name = None
                for line in error_message:
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip()
                        break
                if column_name:
                    if chunk[column_name].dtype == 'object':
                        chunk[column_name] = chunk[column_name].str.strip()
                        
                    infer_and_convert_dates(chunk, column_name)

                else:
                    logger.error("Could not identify the problematic column for DatetimeFieldOverflow error.")
                    raise e
            except TypeError as e:
                attempt_count += 1
                if "__str__ returned non-string (type bytes)" in str(e):
                    logger.warning(f"Encountered TypeError due to bytes. Retrying with bytes conversion.")
                    cur.execute(f"ROLLBACK TO SAVEPOINT before_insert_{self.obj_name}")
                    bytes_conversion_done = True
                else:
                    logger.error(f"Failed to insert chunk with {len(chunk)} records into {table}", exc_info=True)
                    raise e
            except psycopg2.errors.InvalidTextRepresentation as e:
                logger.warning(f"InvalidTextRepresentation encountered: {str(e)}. Attempting to clean data and retry.")
                cur.execute(f"ROLLBACK TO SAVEPOINT before_insert_{self.obj_name}")
                error_message = str(e).split("\n")
                column_name = None
                alter_column = None
                for line in error_message:
                    if any(word in line for word in ("integer", "smallint", "bigint")):
                        alter_column = 'double precision'
                    elif any(word in line for word in ("bytea",)):
                        alter_column = 'text'
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip()
                        break
                
                if alter_column:
                    clean_data(chunk, column_name, alter_column)
                else:
                    clean_data(chunk)
                    insert_chunk(chunk, table, cur, use_quotes)
                    logger.info(f"Inserted chunk with {len(chunk)} records into {table} after cleaning data.")
            except Exception as e:
                logger.error(f"Failed to insert chunk with {len(chunk)} records into {table}", exc_info=True)
                raise e

            if attempt_count >= 8:
                logger.error(f"Failed to insert chunk with {len(chunk)} records into {table} after {attempt_count} attempts.")
                raise RuntimeError(f"Max attempts reached for inserting chunk with {len(chunk)} records into {table}")
            
def ensure_flat_list(items):
    result = []
    for item in items:
        if isinstance(item, list):  # Verifica se é uma lista
            result.extend(ensure_flat_list(item))     # Expande a lista na lista principal
        else:
            result.append(item)     # Adiciona diretamente
    return result

def check_failed_tasks(**kwargs):
    dag_run = kwargs['dag_run']
    dag_id = kwargs['dag'].dag_id
    task_instances = dag_run.get_task_instances()
    
    failed_tasks = [ti for ti in task_instances if ti.state == 'failed']
    
    if failed_tasks and SCHEMA_CRIACAO_INSERT.lower() != "staging":
        failed_task_names = [task.task_id for task in failed_tasks]

        description = (
            f"Failed Tasks: {', '.join(failed_task_names)}\n"
        )

        # Truncate the description to 4000 characters
        if len(description) > 4000:
            description = description[:4000]

        # Log para debugging
        logging.info(f"Falha nas seguintes tasks: {description}")

        conn = pymssql.connect(server=SYNC_HOST, user=SYNC_USERNAME, password=SYNC_PASSWORD, database=SYNC_DBNAME, port=SYNC_PORT)
        cur = conn.cursor()

        # Escapando caracteres especiais para a string da descrição
        escaped_description = description.replace("'", "''")

        cur.execute(f"EXEC bqpr_SyncAPI_AberturaChamado_FalhaExecucaoDagAirflow '{dag_id}', '{escaped_description}'")
        conn.commit()
        cur.close()

def definir_periodo(hora):
    if hora is None:
        return "Inesperado"
    elif 3 <= hora <= 13:
        return "1ª do dia"
    elif 14 <= hora < 16:
        return "2ª do dia"
    elif 16 <= hora < 18:
        return "3ª do dia"
    elif 18 <= hora < 20:
        return "4ª do dia"
    elif 20 <= hora <= 24:
        return "5ª do dia"
    else:
        return "Inesperado"
    
def definir_periodo(hora):
    if hora is None:
        return "Inesperado"
    elif 3 <= hora <= 12:
        return "1ª do dia"
    elif 13 <= hora < 15:
        return "2ª do dia"
    elif 15 <= hora < 17:
        return "3ª do dia"
    elif 17 <= hora < 19:
        return "4ª do dia"
    elif 19 <= hora < 23:
        return "5ª do dia"
    else:
        return "Inesperado"
    
def log_dag_statistics(**context):

    if SCHEMA_CRIACAO_INSERT.lower() != "staging":
        dag_run = context['dag_run']
        dag_id = dag_run.dag_id
        execution_date = dag_run.execution_date
        # Verificar e ajustar `start_time` para timezone-aware
        start_time = dag_run.start_date
        if is_naive(start_time):  # Se for naive, torná-lo aware
            start_time = make_aware(start_time, timezone=pytz.timezone("America/Sao_Paulo"))

        hora = start_time.hour if start_time else None
        periodo = definir_periodo(hora)
        print(hora)
        print(periodo)
        # Garantir que `end_time` seja timezone-aware
        end_time = datetime.now(pytz.timezone("America/Sao_Paulo"))
        duration = (end_time - start_time).total_seconds() if start_time else None
        status = dag_run.state
        total_tasks = len(context['dag'].tasks)

        # Coletar estados das tasks
        task_instances = dag_run.get_task_instances()
        successful_tasks = len([t for t in task_instances if t.state == "success"])
        failed_tasks = len([t for t in task_instances if t.state == "failed"])
        skipped_tasks = len([t for t in task_instances if t.state == "skipped"])

        query = """
            INSERT INTO dbdwcorporativo.dag_statistics 
            (dag_id, execution_date, start_time, end_time, duration_seconds, status, total_tasks, successful_tasks, failed_tasks, skipped_tasks, periodo)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (dag_id, execution_date) DO NOTHING
        """
        data = (dag_id, execution_date, start_time, end_time, duration, status, total_tasks, successful_tasks, failed_tasks, skipped_tasks, periodo)
        with psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, data)

def substituir_placeholders_em_dicionarios(dicionario, variaveis):
    """
    Substitui todos os placeholders no formato 'var_*' nos valores de um dicionário.

    Args:
        dicionario (dict): O dicionário com placeholders a serem substituídos.
        variaveis (dict): Um dicionário contendo as substituições, onde a chave é o nome do placeholder.

    Returns:
        dict: O dicionário com os placeholders substituídos.
    """
    if isinstance(dicionario, dict):
        # Se for um dicionário, percorre suas chaves e valores recursivamente
        return {key: substituir_placeholders_em_dicionarios(value, variaveis) for key, value in dicionario.items()}
    
    elif isinstance(dicionario, list):
        # Se for uma lista, percorre os itens recursivamente
        return [substituir_placeholders_em_dicionarios(item, variaveis) for item in dicionario]
    
    elif isinstance(dicionario, str):
        # Se for uma string, substitui todos os placeholders encontrados
        for placeholder, valor in variaveis.items():
            if f'var_{placeholder}' in dicionario:
                # Se o valor substituto for uma lista, converte para string (ou formatação necessária)
                if isinstance(valor, list):
                    return valor  # Ou formate conforme necessário
                else:
                    dicionario = dicionario.replace(f'var_{placeholder}', str(valor))
        return dicionario
    
    else:
        # Retorna o valor diretamente se não for string, dicionário ou lista
        return dicionario

def get_aux_tables_func():
    conn = psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST)
    
    with conn:
        with conn.cursor() as cur:
            cur.execute("select schema, nome_tabela, tempo_execucao, tipo_execucao from dbdwcorporativo.aux_tipoExecucao")
            result = cur.fetchall()
            
    conn.close()
    return result