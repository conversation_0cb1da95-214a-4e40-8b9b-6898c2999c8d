"""
V3-SILVER-SYONET: Dados Transformados (Silver Layer)

🚀 DAG SILVER SYONET - DADOS TRANSFORMADOS:

Esta DAG processa dados transformados (silver) a partir dos dados bronze do Syonet:
- tb_oportunidades_base: View complexa de oportunidades com múltiplos JOINs
- tb_im_assinatura_original: View de assinaturas com transformações
- tb_maquinas_semana_passada: Processamento semanal (sextas-feiras)
- tb_maquinas_atual: Processamento de máquinas atual
- bi_maquinas: View BIMaquinas migrada do SQL Server com classificação de clientes
- bi_maquinas_pivotada: View BIMaquinasPivotada com dados pivotados (UNPIVOT)

DEPENDÊNCIAS:
- Depende da execução bem-sucedida de V3-BRONZE-SYONET
- Usa dados das tabelas bronze_syonet_syo_*
- bi_maquinas e bi_maquinas_pivotada dependem de tb_maquinas_atual

OTIMIZAÇÕES IMPLEMENTADAS:
✅ Filtros otimizados sem conversões custosas em WHERE
✅ CTEs simplificadas com DISTINCT ON (mais rápido que ROW_NUMBER)
✅ Eliminação de regex custosa
✅ JOINs otimizados com condições eficientes
✅ Controle de timeout (10 minutos por tabela)
✅ Logs detalhados de performance
✅ Migração de UNPIVOT SQL Server para UNNEST PostgreSQL
✅ Conversão de funções de data (GETDATE, DATEADD, MONTH, YEAR)
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.utils.trigger_rule import TriggerRule
from airflow.exceptions import AirflowSkipException
import psycopg2
import pandas as pd
import logging
import time
from contextlib import contextmanager

# Configuração de conexão PostgreSQL (destino)
POSTGRES_CONFIG = {
    'host': '***********',
    'user': 'bq_dwcorporativo_u',
    'password': 'N#OK+#{Yx*',
    'database': 'postgres',
    'port': 5432
}

# Configurações básicas
SEVEN_DAYS_TIMEOUT = 300  # 5 minutos timeout para operações 7 dias

@contextmanager
def get_postgres_cursor():
    """Context manager para conexão PostgreSQL"""
    conn = None
    cursor = None
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        cursor = conn.cursor()
        yield cursor, conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Argumentos padrão da DAG
BASE_DAG_ARGS = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'catchup': False
}

def process_tb_oportunidades_base(**context):
    """
    Gera tb_oportunidades_base OTIMIZADA para PostgreSQL
    🚀 OTIMIZAÇÕES IMPLEMENTADAS:
    - Filtros otimizados sem conversões custosas em WHERE
    - CTEs simplificadas com índices implícitos
    - Eliminação de regex custosa
    - JOINs otimizados com condições eficientes
    """
    print("🚀 Iniciando tb_oportunidades_base OTIMIZADA para PostgreSQL")
    
    # ESTRATÉGIA OTIMIZADA: Filtros eficientes + CTEs simplificadas
    extract_sql = f"""
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_oportunidades_base;
    
    CREATE TABLE dbdwcorporativo.silver_syonet_tb_oportunidades_base AS
    WITH evt_base AS (
        SELECT id_evento, id_cliente, id_tipoevento, dt_inc, dt_conclusao, dt_proximaacao,
               ds_formacontato, ds_midia, ds_assunto, ds_resultado, id_statusevento,
               ds_temperatura, id_componente
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('DVM LEAD','DVM OPORTUNIDADE','DVM LEAD RELACIONAMENTO','DVM RELACIONAMENTO','DVM LICITACAO')
          AND dt_inc >= 1672531200000  -- 2023-01-01 em milliseconds (otimizado)
    ),
    acao_stats AS (
        SELECT id_evento,
               MAX(dt_alt) as dt_alt_last,
               MIN(dt_alt) as dt_alt_first,
               MAX(CASE WHEN rn_desc = 1 THEN id_motivoresultado END) as id_motivoresultado
        FROM (
            SELECT id_evento, dt_alt, id_motivoresultado,
                   ROW_NUMBER() OVER (PARTITION BY id_evento ORDER BY id_acao DESC) as rn_desc
            FROM dbdwcorporativo.bronze_syonet_syo_acao
            WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ) t
        WHERE rn_desc <= 1
        GROUP BY id_evento
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               -- OTIMIZAÇÃO: Conversão numérica SEGURA (evita "Selecione..." e valores inválidos)
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='Previsão de Faturamento' THEN ds_valor END) AS PREV_FAT,
               MAX(CASE WHEN ds_etiqueta='Data do faturamento'     THEN ds_valor END) AS DT_FAT,
               MAX(CASE WHEN ds_etiqueta='FATURA ESSE MÊS?'        THEN ds_valor END) AS FATURA_MES
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    ),
    hef_agg AS (
        SELECT id_evento,
               'MAQUINA FATURADA' AS Faturada,
               -- OTIMIZAÇÃO: Conversão de timestamp otimizada
               MIN(TO_TIMESTAMP(dt_inc/1000) - INTERVAL '3 hours') AS dt_inc
        FROM dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento
        WHERE id_etapafunil IN (6,9,17,99,20,45,153,144,154)
        GROUP BY id_evento
    )
    SELECT
        EVT.id_cliente,
        CLI.nm_cliente         AS "Cliente",
        CLI.no_cpfcnpj         AS "CNPJ/CPF",
        COALESCE(CLI.nm_cidadecom, CLI.nm_cidaderes) AS "Cidade",
        COALESCE(CLI.sg_ufcom,    CLI.sg_ufres)      AS "UF",
        EVT.id_tipoevento,
        EMP.ap_empresa         AS "Filial",
        EVT.id_evento          AS "Nº Evento",
        EVT.ds_formacontato    AS "Origem",
        EVT.ds_midia           AS "Midia",
        EVT.ds_assunto         AS "Assunto",
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS "Data Fechamento",
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS "Data Inclusão",
        CASE WHEN acs.dt_alt_last IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_last/1000) - INTERVAL '3 hours' END AS "Data U.Alteração",
        CASE WHEN acs.dt_alt_first IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_first/1000) - INTERVAL '3 hours' END AS "Data P.Alteração",
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS "Data P.Acao",
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS "Status",
        rp._MODELO  AS "Modelo Interesse 1",
        rp._MODELO2 AS "Modelo Interesse 2",
        rp._MODELO3 AS "Modelo Interesse 3",
        rp._MODELO4 AS "Modelo Interesse 4",
        rp._MODELO5 AS "Modelo Interesse 5",
        mv1.id_versao AS "Máquina 1", mv2.id_versao AS "Máquina 2", mv3.id_versao AS "Máquina 3",
        mv4.id_versao AS "Máquina 4", mv5.id_versao AS "Máquina 5",
        rp.QTD1 AS "Quantidade 1", rp.QTD2 AS "Quantidade 2", rp.QTD3 AS "Quantidade 3",
        rp.QTD4 AS "Quantidade 4", rp.QTD5 AS "Quantidade 5",
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS "Etapa Funil",
        CASE WHEN EVT.id_statusevento = 'ANDAMENTO' THEN MR.ds_motivo END AS "Motivo de Andamento",
        CASE WHEN EVT.ds_resultado   = 'INSUCESSO'  THEN MR.ds_motivo END AS "Motivo da Perda",
        USU.nm_login AS "Usuário AJ",
        REPLACE(rp.VALOR1,'Não informado','0,00') AS "Valor 1",
        REPLACE(rp.VALOR2,'Não informado','0,00') AS "Valor 2",
        REPLACE(rp.VALOR3,'Não informado','0,00') AS "Valor 3",
        REPLACE(rp.VALOR4,'Não informado','0,00') AS "Valor 4",
        REPLACE(rp.VALOR5,'Não informado','0,00') AS "Valor 5",
        -- OTIMIZAÇÃO: Conversões de timestamp SEGURAS (evita "Selecione..." e valores inválidos)
        CASE WHEN rp.PREV_FAT IS NOT NULL
             AND TRIM(rp.PREV_FAT) != ''
             AND TRIM(rp.PREV_FAT) NOT ILIKE '%selecione%'
             AND TRIM(rp.PREV_FAT) ~ '^[0-9]+$'
             THEN TO_TIMESTAMP(TRIM(rp.PREV_FAT)::bigint/1000) - INTERVAL '3 hours' END AS "Previsão Faturamento",
        evt.ds_temperatura AS "Temperatura",
        EVT.id_componente  AS "Evento Anterior",
        hef_agg.Faturada,
        hef_agg.dt_inc        AS "Data Etapa",
        CASE WHEN rp.DT_FAT IS NOT NULL
             AND TRIM(rp.DT_FAT) != ''
             AND TRIM(rp.DT_FAT) NOT ILIKE '%selecione%'
             AND TRIM(rp.DT_FAT) ~ '^[0-9]+$'
             THEN TO_TIMESTAMP(TRIM(rp.DT_FAT)::bigint/1000) - INTERVAL '3 hours' END AS "Data do Faturamento",
        rp.FATURA_MES     AS "Fatura esse mês ?"
    FROM        evt_base EVT
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
               ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = USU.id_empresa
    LEFT JOIN   acao_stats acs ON acs.id_evento = EVT.id_evento
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON acs.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN   ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN   ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN   hef_agg ON hef_agg.id_evento = EVT.id_evento
    """
    
    print("🚀 Executando query otimizada para tb_oportunidades_base...")
    start_time = time.time()
    
    try:
        with get_postgres_cursor() as (cursor, conn):
            # Configurar timeout para a sessão (10 minutos)
            cursor.execute(extract_sql)
            conn.commit()
        
        elapsed_time = time.time() - start_time
        print(f"✅ TB_OPORTUNIDADES_BASE concluído em {elapsed_time:.1f}s")
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em tb_oportunidades_base após {elapsed_time:.1f}s: {str(e)}")
        raise
    
    # Validação - tabela de negócio, sem comparação direta com origem
    print(f"🔍 Iniciando validação imediata de tb_oportunidades_base")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base")
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            print(f"📊 tb_oportunidades_base criada com {count} registros")
            
            if count == 0:
                print(f"⚠️ AVISO: tb_oportunidades_base está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_oportunidades_base validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_oportunidades_base: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_im_assinatura_original(**context):
    """
    Gera tb_IM_Assinatura_original OTIMIZADA para PostgreSQL
    🚀 OTIMIZAÇÕES IMPLEMENTADAS:
    - Filtros otimizados sem conversões custosas em WHERE
    - CTEs simplificadas com DISTINCT ON
    - Eliminação de regex custosa
    - JOINs otimizados com condições eficientes
    """
    print("🚀 Iniciando tb_IM_Assinatura_original OTIMIZADA para PostgreSQL")

    extract_sql = f"""
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original;

    CREATE TABLE dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original AS
    WITH evt_base AS (
        SELECT id_evento, id_cliente, id_tipoevento, dt_inc, dt_conclusao, dt_proximaacao,
               ds_formacontato, ds_assunto, ds_resultado, id_statusevento, ds_temperatura, id_componente
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('INDICACAO ASSINATURA','DVA ASSINATURA','PRECIFICACAO ASSINATURA',
                                'DVA LEAD RELACIONAMENTO','DVA RELACIONAMENTO',
                                'PRECIFICACAO AVANT','DVA ASSINATURA AVANT')
          AND id_statusevento <> 'CANCELADO'
          AND dt_inc > 1704067200000  -- 2023-12-31 em milliseconds (otimizado)
    ),
    acao_stats AS (
        SELECT id_evento,
               MAX(dt_alt) as dt_alt_last,
               MIN(dt_alt) as dt_alt_first,
               MAX(CASE WHEN rn_desc = 1 THEN id_motivoresultado END) as id_motivoresultado
        FROM (
            SELECT id_evento, dt_alt, id_motivoresultado,
                   ROW_NUMBER() OVER (PARTITION BY id_evento ORDER BY id_acao DESC) as rn_desc
            FROM dbdwcorporativo.bronze_syonet_syo_acao
            WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ) t
        WHERE rn_desc <= 1
        GROUP BY id_evento
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               -- OTIMIZAÇÃO: Conversão numérica SEGURA (evita "Selecione..." e valores inválidos)
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO5
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    )
    SELECT
        EVT.id_cliente                               AS "id_cliente",
        CLI.nm_cliente                               AS "Cliente",
        CLI.no_cpfcnpj                               AS "CNPJ/CPF",
        EVT.id_tipoevento                            AS "Tipo Evento",
        EMP.ap_empresa                               AS "Filial",
        EVT.id_evento                                AS "Nº Evento",
        EVT.ds_formacontato                          AS "Origem",
        rp._MODELO                                   AS "Modelo Interesse 1",
        mv1.id_versao                                AS "Máquina 1",
        rp.QTD1                                      AS "Quantidade 1",
        REPLACE(rp.VALOR1,'Não informado','0,00')    AS "Valor 1",
        rp._MODELO2                                  AS "Modelo Interesse 2",
        mv2.id_versao                                AS "Máquina 2",
        rp.QTD2                                      AS "Quantidade 2",
        REPLACE(rp.VALOR2,'Não informado','0,00')    AS "Valor 2",
        rp._MODELO3                                  AS "Modelo Interesse 3",
        mv3.id_versao                                AS "Máquina 3",
        rp.QTD3                                      AS "Quantidade 3",
        REPLACE(rp.VALOR3,'Não informado','0,00')    AS "Valor 3",
        rp._MODELO4                                  AS "Modelo Interesse 4",
        mv4.id_versao                                AS "Máquina 4",
        rp.QTD4                                      AS "Quantidade 4",
        REPLACE(rp.VALOR4,'Não informado','0,00')    AS "Valor 4",
        rp._MODELO5                                  AS "Modelo Interesse 5",
        mv5.id_versao                                AS "Máquina 5",
        rp.QTD5                                      AS "Quantidade 5",
        REPLACE(rp.VALOR5,'Não informado','0,00')    AS "Valor 5",
        EVT.ds_assunto                               AS "Assunto",
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS "Data Fechamento",
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS "Data Inclusão",
        CASE WHEN acs.dt_alt_last IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_last/1000) - INTERVAL '3 hours' END AS "Data U.Alteração",
        CASE WHEN acs.dt_alt_first IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_first/1000) - INTERVAL '3 hours' END AS "Data P.Alteração",
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS "Data P.Acao",
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS "Status",
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS "Etapa Funil",
        CASE WHEN EVT.id_statusevento='ANDAMENTO' THEN MR.ds_motivo END AS "Motivo de Andamento",
        CASE WHEN EVT.ds_resultado='INSUCESSO' THEN MR.ds_motivo END    AS "Motivo da Perda",
        USU.nm_login                                   AS "Vendedor",
        EVT.ds_temperatura                             AS "Temperatura",
        EVT1.id_tipoevento                             AS "Tipo Evento Anterior",
        EVT1.cd_usuarioinc                             AS "Indicante Evento Anterior"
    FROM evt_base EVT
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
            ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = ENC.id_empresa
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN acao_stats acs ON acs.id_evento = EVT.id_evento
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON acs.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    -- OTIMIZAÇÃO: JOINs com modeloversao otimizados
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_evento EVT1 ON EVT1.id_evento = EVT.id_componente;

    GRANT SELECT ON TABLE dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original TO bq_vitor_barros_u;
    """

    print("🚀 Executando query otimizada para tb_IM_Assinatura_original...")
    start_time = time.time()

    try:
        with get_postgres_cursor() as (cursor, conn):
            # Configurar timeout para a sessão (10 minutos)

            # DEBUG: Verificar valores problemáticos antes da execução
            debug_query = """
            SELECT ds_etiqueta, ds_valor, COUNT(*) as qtd
            FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
            WHERE ds_etiqueta LIKE '_VERSAO%'
              AND (ds_valor ILIKE '%selecione%' OR ds_valor !~ '^[0-9]*$')
            GROUP BY ds_etiqueta, ds_valor
            ORDER BY qtd DESC
            LIMIT 10
            """
            cursor.execute(debug_query)
            debug_results = cursor.fetchall()
            if debug_results:
                print(f"🔍 DEBUG: Valores problemáticos encontrados:")
                for row in debug_results:
                    print(f"   - {row[0]}: '{row[1]}' ({row[2]} ocorrências)")

            cursor.execute(extract_sql)
            conn.commit()

        elapsed_time = time.time() - start_time
        print(f"✅ TB_IM_ASSINATURA_ORIGINAL concluído em {elapsed_time:.1f}s")

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em tb_IM_Assinatura_original após {elapsed_time:.1f}s: {str(e)}")

        # DEBUG adicional em caso de erro
        try:
            with get_postgres_cursor() as (cursor, conn):
                cursor.execute("SELECT ds_valor FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface WHERE ds_etiqueta LIKE '_VERSAO%' AND ds_valor = 'Selecione...' LIMIT 5")
                problematic_values = cursor.fetchall()
                if problematic_values:
                    print(f"🔍 DEBUG: Encontrados valores 'Selecione...': {len(problematic_values)} registros")
        except:
            pass

        raise

    # Validação
    print(f"🔍 Iniciando validação imediata de tb_IM_Assinatura_original")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original")
            result = cursor.fetchone()
            count = result[0] if result else 0

            print(f"📊 tb_IM_Assinatura_original criada com {count} registros")

            if count == 0:
                print(f"⚠️ AVISO: tb_IM_Assinatura_original está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_IM_Assinatura_original validada com sucesso")

    except Exception as e:
        error_msg = f"Erro na validação de tb_IM_Assinatura_original: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_semana_passada(**context):
    """Processa tb_maquinas_semana_passada APENAS nas sextas-feiras"""
    if not datetime.now().weekday() == 4: # 4 = sexta-feira
        print("✓ Não é sexta-feira - pulando tb_maquinas_semana_passada")
        raise AirflowSkipException("Não é sexta-feira - tb_maquinas_semana_passada executada apenas às sextas")

    print("📊 Processando tb_maquinas_semana_passada (sexta-feira)")

    extract_sql = """
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada;

    CREATE TABLE dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada AS
    SELECT "Cliente", "CNPJ/CPF", "id_tipoevento", "Filial", "Nº Evento",
           "Data Fechamento", "Data Inclusão", "Data U.Alteração", "Data P.Alteração",
           "Status", "Modelo Interesse 1", "Modelo Interesse 2", "Modelo Interesse 3",
           "Modelo Interesse 4", "Modelo Interesse 5", "Etapa Funil",
           "Motivo de Andamento", "Motivo da Perda", "Usuário AJ",
           "Valor 1", "Valor 2", "Valor 3", "Valor 4", "Valor 5",
           "Temperatura", "Evento Anterior"
    FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base;

    GRANT SELECT ON TABLE dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada TO bq_vitor_barros_u;
    """

    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute(extract_sql)
            conn.commit()

        print("✅ TB_MAQUINAS_SEMANA_PASSADA concluído")

        # Validação baseada na tabela de origem (lógica original)
        print(f"🔍 Iniciando validação imediata de tb_maquinas_semana_passada")
        with get_postgres_cursor() as (cursor, conn):
            # Conta registros na semana passada
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada")
            result = cursor.fetchone()
            week_count = result[0] if result else 0

            # Conta registros na tabela base
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base")
            result = cursor.fetchone()
            base_count = result[0] if result else 0

            print(f"📊 tb_maquinas_semana_passada: {week_count} registros (base: {base_count})")

            if week_count != base_count:
                print(f"⚠️ AVISO: Diferença entre semana passada ({week_count}) e base ({base_count})")
            else:
                print(f"✅ tb_maquinas_semana_passada validada com sucesso")

    except Exception as e:
        error_msg = f"Erro em tb_maquinas_semana_passada: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_atual(**context):
    """
    Processa tb_maquinas_atual - executa sempre
    LÓGICA ORIGINAL: Cria uma cópia completa da tb_oportunidades_base
    """
    print("📊 Processando tb_maquinas_atual")

    # Lógica original: SELECT * INTO tb_maquinas_atual FROM tb_oportunidades_base
    extract_sql = """
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_maquinas_atual;

    CREATE TABLE dbdwcorporativo.silver_syonet_tb_maquinas_atual AS
    SELECT * FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base;

    GRANT SELECT ON TABLE dbdwcorporativo.silver_syonet_tb_maquinas_atual TO bq_vitor_barros_u;
    """

    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute(extract_sql)
            conn.commit()

        print("✅ TB_MAQUINAS_ATUAL concluído")

        # Validação baseada na tabela de origem (lógica original)
        print(f"🔍 Iniciando validação imediata de tb_maquinas_atual")
        with get_postgres_cursor() as (cursor, conn):
            # Conta registros na atual
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual")
            result = cursor.fetchone()
            atual_count = result[0] if result else 0

            # Conta registros na tabela base
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base")
            result = cursor.fetchone()
            base_count = result[0] if result else 0

            print(f"📊 tb_maquinas_atual: {atual_count} registros (base: {base_count})")

            if atual_count != base_count:
                error_msg = f"Divergência entre atual ({atual_count}) e base ({base_count})"
                print(f"❌ {error_msg}")
                raise ValueError(error_msg)
            else:
                print(f"✅ tb_maquinas_atual validada com sucesso")

    except Exception as e:
        error_msg = f"Erro em tb_maquinas_atual: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_bi_maquinas(**context):
    """
    Gera BIMaquinas OTIMIZADA para PostgreSQL
    🚀 MIGRAÇÃO DA VIEW SQL SERVER PARA POSTGRESQL:
    - Conversão de sintaxe SQL Server para PostgreSQL
    - Otimização de ROW_NUMBER() com DISTINCT ON
    - Conversão de funções específicas (GETDATE, DATEADD, etc.)
    - Tratamento de aspas simples em aliases
    """
    print("🚀 Iniciando BIMaquinas OTIMIZADA para PostgreSQL")

    extract_sql = """
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_bi_maquinas;

    CREATE TABLE dbdwcorporativo.silver_syonet_bi_maquinas AS
    WITH base_maquinas AS (
        SELECT DISTINCT ON (MQAT."Nº Evento")
            MQAT."Cliente",
            MQAT."CNPJ/CPF",
            MQAT."Cidade",
            MQAT."UF",
            MQAT."id_tipoevento",
            MQAT."Filial",
            MQAT."Nº Evento",
            MQAT."Data Fechamento",
            MQAT."Data Inclusão",
            MQAT."Data P.Acao",
            MQAT."Data U.Alteração",
            MQAT."Data P.Alteração",
            MQAT."Data Fechamento" as "Hora Fechamento",
            MQAT."Data Inclusão" as "Hora Inclusão",
            MQAT."Data U.Alteração" as "Hora U.Alteração",
            MQAT."Data P.Alteração" as "Hora P.Alteração",
            MQAT."Status",
            MQAT."Modelo Interesse 1",
            MQAT."Máquina 1",
            MQAT."Quantidade 1",
            MQAT."Modelo Interesse 2",
            MQAT."Máquina 2",
            MQAT."Quantidade 2",
            MQAT."Modelo Interesse 3",
            MQAT."Máquina 3",
            MQAT."Quantidade 3",
            MQAT."Modelo Interesse 4",
            MQAT."Máquina 4",
            MQAT."Quantidade 4",
            MQAT."Modelo Interesse 5",
            MQAT."Máquina 5",
            MQAT."Quantidade 5",
            CASE
                WHEN MQAT."Status" = 'AGUARDANDO' AND MQAT."id_tipoevento" = 'DVM OPORTUNIDADE' THEN '0- AGUARDANDO'
                ELSE
                    CASE
                        WHEN MQAT."Etapa Funil" IS NULL OR MQAT."Etapa Funil" = '' OR MQAT."Etapa Funil" = ' ' THEN
                            CASE WHEN MQAT."Status" = 'ANDAMENTO' THEN MQAT."Motivo de Andamento" ELSE NULL END
                        ELSE RTRIM(REPLACE(MQAT."Etapa Funil", '         ', ''))
                    END
            END AS "Etapa Funil",
            MQAT."Motivo de Andamento",
            MQAT."Motivo da Perda",
            MQAT."Usuário AJ",
            MQAT."Valor 1",
            MQAT."Valor 2",
            MQAT."Valor 3",
            MQAT."Valor 4",
            MQAT."Valor 5",
            MQAT."Temperatura",
            MQAT."Evento Anterior",
            MQAT."Data do Faturamento",
            FNC3.nm_faixanovaclassificacao AS "Faixa Maquinas",
            MQAT."Fatura esse mês ?"
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual MQAT
        LEFT JOIN (
            SELECT *
            FROM dbdwcorporativo.bronze_syonet_syo_clientenovaclassificacao
            WHERE id_novaclassificacao = 15
        ) CNC3 ON CNC3.id_cliente = MQAT.id_cliente
        LEFT JOIN dbdwcorporativo.bronze_syonet_syo_faixanovaclassificacao FNC3
            ON FNC3.id_novaclassificacao = CNC3.id_novaclassificacao
            AND CNC3.no_pontuacao BETWEEN FNC3.vl_inicial AND FNC3.vl_final
        ORDER BY MQAT."Nº Evento"
    )
    SELECT * FROM base_maquinas;

    GRANT SELECT ON TABLE dbdwcorporativo.silver_syonet_bi_maquinas TO bq_vitor_barros_u;
    """

    print("🚀 Executando query otimizada para BIMaquinas...")
    start_time = time.time()

    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute(extract_sql)
            conn.commit()

        elapsed_time = time.time() - start_time
        print(f"✅ BI_MAQUINAS concluído em {elapsed_time:.1f}s")

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em BIMaquinas após {elapsed_time:.1f}s: {str(e)}")
        raise

    # Validação
    print(f"🔍 Iniciando validação imediata de BIMaquinas")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_bi_maquinas")
            result = cursor.fetchone()
            count = result[0] if result else 0

            print(f"📊 BIMaquinas criada com {count} registros")

            if count == 0:
                print(f"⚠️ AVISO: BIMaquinas está vazia - verificar filtros e dependências")
            else:
                print(f"✅ BIMaquinas validada com sucesso")

    except Exception as e:
        error_msg = f"Erro na validação de BIMaquinas: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_bi_maquinas_pivotada(**context):
    """
    Gera BIMaquinasPivotada OTIMIZADA para PostgreSQL
    🚀 MIGRAÇÃO DA VIEW SQL SERVER PARA POSTGRESQL:
    - Conversão de UNPIVOT para UNNEST com arrays
    - Conversão de funções de data (GETDATE, DATEADD, MONTH, YEAR)
    - Otimização de JOINs complexos
    - Tratamento de aspas simples em aliases
    """
    print("🚀 Iniciando BIMaquinasPivotada OTIMIZADA para PostgreSQL")

    extract_sql = """
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_bi_maquinas_pivotada;

    CREATE TABLE dbdwcorporativo.silver_syonet_bi_maquinas_pivotada AS
    WITH unpivot_modelos AS (
        SELECT
            "Nº Evento",
            modelo_interesse AS "Modelo Interesse",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Modelo Interesse 1", "Modelo Interesse 2", "Modelo Interesse 3", "Modelo Interesse 4", "Modelo Interesse 5"]) as modelo_interesse,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS modelos
        WHERE modelo_interesse IS NOT NULL AND TRIM(modelo_interesse) != ''
    ),
    unpivot_valores AS (
        SELECT
            "Nº Evento",
            valor AS "Valor",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Valor 1", "Valor 2", "Valor 3", "Valor 4", "Valor 5"]) as valor,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS valores
        WHERE valor IS NOT NULL AND TRIM(valor) != ''
    ),
    unpivot_quantidades AS (
        SELECT
            "Nº Evento",
            quantidade AS "Quantidade",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Quantidade 1", "Quantidade 2", "Quantidade 3", "Quantidade 4", "Quantidade 5"]) as quantidade,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS quantidades
        WHERE quantidade IS NOT NULL AND TRIM(quantidade) != ''
    ),
    unpivot_maquinas AS (
        SELECT
            "Nº Evento",
            maquina AS "Máquina",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Máquina 1", "Máquina 2", "Máquina 3", "Máquina 4", "Máquina 5"]) as maquina,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS maquinas
        WHERE maquina IS NOT NULL AND TRIM(maquina) != ''
    )
    SELECT
        MQAT.id_cliente,
        MQAT."Cliente",
        MQAT."CNPJ/CPF",
        MQAT."id_tipoevento",
        MQAT."Filial",
        MQAT."Nº Evento",
        MQAT."Origem",
        MQAT."Midia",
        MQAT."Assunto",
        MQAT."Data Fechamento",
        MQAT."Data Inclusão",
        MQAT."Data U.Alteração",
        MQAT."Data P.Alteração",
        MQAT."Data P.Acao",
        MQAT."Data Fechamento" as "Hora Fechamento",
        MQAT."Data Inclusão" as "Hora Inclusão",
        MQAT."Data U.Alteração" as "Hora U.Alteração",
        MQAT."Data P.Alteração" as "Hora P.Alteração",
        MQAT."Status",
        p."Modelo Interesse",
        v."Valor",
        t."Quantidade",
        h."Máquina",
        CASE
            WHEN MQAT."Status" = 'AGUARDANDO' AND MQAT."id_tipoevento" = 'DVM OPORTUNIDADE' THEN '0- AGUARDANDO'
            ELSE
                CASE
                    WHEN MQAT."Etapa Funil" IS NULL OR MQAT."Etapa Funil" = '' OR MQAT."Etapa Funil" = ' ' THEN
                        CASE WHEN MQAT."Status" = 'ANDAMENTO' THEN MQAT."Motivo de Andamento" ELSE NULL END
                    ELSE RTRIM(REPLACE(MQAT."Etapa Funil", '         ', ''))
                END
        END AS "Etapa Funil",
        MQAT."Motivo de Andamento",
        MQAT."Motivo da Perda",
        MQAT."Usuário AJ",
        MQAT."Temperatura",
        MQAT."Evento Anterior",
        FNC3.nm_faixanovaclassificacao AS "Faixa Maquinas",
        MQAT."Previsão Faturamento" AS "Previsão de fechamento",
        CASE
            WHEN EXTRACT(MONTH FROM MQAT."Previsão Faturamento") = EXTRACT(MONTH FROM CURRENT_DATE) THEN 'Este mês'
            WHEN EXTRACT(MONTH FROM MQAT."Previsão Faturamento") <= EXTRACT(MONTH FROM (CURRENT_DATE + INTERVAL '3 months'))
                AND EXTRACT(MONTH FROM MQAT."Previsão Faturamento") >= EXTRACT(MONTH FROM (CURRENT_DATE + INTERVAL '1 month')) THEN 'Este trimestre'
            WHEN EXTRACT(YEAR FROM MQAT."Previsão Faturamento") = EXTRACT(YEAR FROM (CURRENT_DATE + INTERVAL '3 months'))
                AND EXTRACT(MONTH FROM MQAT."Previsão Faturamento") >= EXTRACT(MONTH FROM CURRENT_DATE) THEN 'Este ano'
            ELSE NULL
        END AS "Previsão",
        COALESCE(ETF.nm_etapafunil, ETF1.nm_etapafunil) as "Faturada",
        COALESCE(
            TO_TIMESTAMP(HEF.dt_inc/1000) - INTERVAL '3 hours',
            TO_TIMESTAMP(HEF1.dt_inc/1000) - INTERVAL '3 hours'
        ) AS "Data Etapa",
        MQAT."Data do Faturamento",
        MQAT."Fatura esse mês ?"
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual MQAT
    LEFT JOIN (
        SELECT *
        FROM dbdwcorporativo.bronze_syonet_syo_clientenovaclassificacao
        WHERE id_novaclassificacao = 15
    ) CNC3 ON CNC3.id_cliente = MQAT.id_cliente
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_faixanovaclassificacao FNC3
        ON FNC3.id_novaclassificacao = CNC3.id_novaclassificacao
        AND CNC3.no_pontuacao BETWEEN FNC3.vl_inicial AND FNC3.vl_final
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF
        ON MQAT."Nº Evento" = HEF.id_evento
        AND HEF.id_etapafunil IN (6,9,17,99)
        AND HEF.ic_etapaatual = '1'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF
        ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF1
        ON MQAT."Nº Evento" = HEF1.id_evento
        AND HEF1.id_etapafunil IN (20,45)
        AND HEF1.ic_etapaatual = '1'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF1
        ON HEF1.id_etapafunil = ETF1.id_etapafunil
    LEFT JOIN unpivot_modelos p ON p."Nº Evento" = MQAT."Nº Evento"
    LEFT JOIN unpivot_valores v ON v."Nº Evento" = MQAT."Nº Evento" AND v.ordem = p.ordem
    LEFT JOIN unpivot_quantidades t ON t."Nº Evento" = MQAT."Nº Evento" AND t.ordem = p.ordem
    LEFT JOIN unpivot_maquinas h ON h."Nº Evento" = MQAT."Nº Evento" AND h.ordem = p.ordem;

    GRANT SELECT ON TABLE dbdwcorporativo.silver_syonet_bi_maquinas_pivotada TO bq_vitor_barros_u;
    """

    print("🚀 Executando query otimizada para BIMaquinasPivotada...")
    start_time = time.time()

    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute(extract_sql)
            conn.commit()

        elapsed_time = time.time() - start_time
        print(f"✅ BI_MAQUINAS_PIVOTADA concluído em {elapsed_time:.1f}s")

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em BIMaquinasPivotada após {elapsed_time:.1f}s: {str(e)}")
        raise

    # Validação
    print(f"🔍 Iniciando validação imediata de BIMaquinasPivotada")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_bi_maquinas_pivotada")
            result = cursor.fetchone()
            count = result[0] if result else 0

            print(f"📊 BIMaquinasPivotada criada com {count} registros")

            if count == 0:
                print(f"⚠️ AVISO: BIMaquinasPivotada está vazia - verificar filtros e dependências")
            else:
                print(f"✅ BIMaquinasPivotada validada com sucesso")

    except Exception as e:
        error_msg = f"Erro na validação de BIMaquinasPivotada: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

# ===== DEFINIÇÃO DA DAG =====

dag = DAG(
    dag_id='V3-SILVER-SYONET',
    description='🥈 SILVER SYONET: Dados Transformados (tb_oportunidades_base, tb_im_assinatura_original, tb_maquinas_*, bi_maquinas, bi_maquinas_pivotada) - Depende de V3-BRONZE-SYONET',
    schedule_interval=None,  # Executada apenas via orquestração
    start_date=datetime(2024, 1, 1),
    catchup=False,
    max_active_runs=1,
    max_active_tasks=5,  # Aumentado para 5 devido às novas tasks
    default_args=BASE_DAG_ARGS
)

# ===== SENSOR DE DEPENDÊNCIA =====

# Aguarda conclusão específica da task de finalização bronze
start_task = DummyOperator(
    task_id='silver_processing_start',
    dag=dag
)

# ===== TASKS SILVER =====

task_oportunidades_base = PythonOperator(
    task_id='process_tb_oportunidades_base',
    python_callable=process_tb_oportunidades_base,
    dag=dag
)

task_im_assinatura = PythonOperator(
    task_id='process_tb_im_assinatura_original',
    python_callable=process_tb_im_assinatura_original,
    dag=dag
)

task_maquinas_semana_passada = PythonOperator(
    task_id='process_tb_maquinas_semana_passada',
    python_callable=process_tb_maquinas_semana_passada,
    dag=dag
)

task_maquinas_atual = PythonOperator(
    task_id='process_tb_maquinas_atual',
    python_callable=process_tb_maquinas_atual,
    dag=dag
)

task_bi_maquinas = PythonOperator(
    task_id='process_bi_maquinas',
    python_callable=process_bi_maquinas,
    dag=dag
)

task_bi_maquinas_pivotada = PythonOperator(
    task_id='process_bi_maquinas_pivotada',
    python_callable=process_bi_maquinas_pivotada,
    dag=dag
)

# Task de finalização
task_silver_complete = DummyOperator(
    task_id='silver_processing_complete',
    dag=dag,
    trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS
)

# ===== DEPENDÊNCIAS =====

# Aguarda bronze → Executa silver em paralelo → Finalização
start_task >> [task_oportunidades_base, task_im_assinatura]
task_oportunidades_base >> [task_maquinas_semana_passada, task_maquinas_atual]
task_maquinas_atual >> [task_bi_maquinas, task_bi_maquinas_pivotada]
[task_maquinas_semana_passada, task_bi_maquinas, task_bi_maquinas_pivotada, task_im_assinatura] >> task_silver_complete