import os
import sys

# Obtém o nome do script que foi executado
executing_script = os.path.basename(sys.argv[0])

# Só importa se o script rodando não for "LocalRun.py"
if executing_script == "LocalRun.py":
    import importlib.util

    # Obtém o caminho absoluto do arquivo secrets.py
    secrets_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../secrets/secrets.py"))

    # Carrega o módulo dinamicamente
    spec = importlib.util.spec_from_file_location("secrets", secrets_path)
    secrets = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(secrets)

    # Importa todas as variáveis do módulo para o escopo atual
    globals().update({name: getattr(secrets, name) for name in dir(secrets) if not name.startswith("__")})

else:
    from airflow.models import Variable

    # NBS
    NBS_USERNAME = Variable.get("NBS_USERNAME")
    NBS_PASSWORD = Variable.get("NBS_PASSWORD")
    NBS_SERVICE_NAME = Variable.get("NBS_SERVICE_NAME")
    NBS_HOST = Variable.get("NBS_HOST")
    NBS_PORT = Variable.get("NBS_PORT")

    # DealerNet
    DEALER_USERNAME = Variable.get("DEALER_USERNAME")
    DEALER_PASSWORD = Variable.get("DEALER_PASSWORD")
    DEALER_DBNAME = Variable.get("DEALER_DBNAME")
    DEALER_HOST = Variable.get("DEALER_HOST")
    DEALER_PORT = Variable.get("DEALER_PORT")

    # Quiver
    QUIVER_USERNAME = Variable.get("QUIVER_USERNAME")
    QUIVER_PASSWORD = Variable.get("QUIVER_PASSWORD")
    QUIVER_DBNAME = Variable.get("QUIVER_DBNAME")
    QUIVER_HOST = Variable.get("QUIVER_HOST")
    QUIVER_PORT = Variable.get("QUIVER_PORT")

    # Syonet
    SYONET_USERNAME = Variable.get("SYONET_USERNAME")
    SYONET_PASSWORD = Variable.get("SYONET_PASSWORD")
    SYONET_DBNAME = Variable.get("SYONET_DBNAME")
    SYONET_HOST = Variable.get("SYONET_HOST")
    SYONET_PORT = Variable.get("SYONET_PORT")

    # Dw_BAMAQ (Consórcio)
    DW_BAMAQ_USERNAME = Variable.get("DW_BAMAQ_USERNAME")
    DW_BAMAQ_PASSWORD = Variable.get("DW_BAMAQ_PASSWORD")
    DW_BAMAQ_DBNAME = Variable.get("DW_BAMAQ_DBNAME")
    DW_BAMAQ_HOST = Variable.get("DW_BAMAQ_HOST")
    DW_BAMAQ_HOST_NAME = Variable.get("DW_BAMAQ_HOST_NAME")
    DW_BAMAQ_HOST_2 = Variable.get("DW_BAMAQ_HOST_2")
    DW_BAMAQ_HOST_NAME_2 = Variable.get("DW_BAMAQ_HOST_NAME_2")
    DW_BAMAQ_PORT = Variable.get("DW_BAMAQ_PORT")

    # Corpore RM 
    CORPORE_USERNAME = Variable.get("CORPORE_USERNAME")
    CORPORE_PASSWORD = Variable.get("QUIVER_PASSWORD")
    CORPORE_DBNAME = Variable.get("CORPORE_DBNAME")
    CORPORE_HOST = Variable.get("CORPORE_HOST")
    CORPORE_PORT = Variable.get("CORPORE_PORT")

    # SGE
    SGE_USERNAME = Variable.get("SGE_USERNAME")
    SGE_PASSWORD = Variable.get("QUIVER_PASSWORD")
    SGE_DBNAME = Variable.get("SGE_DBNAME")
    SGE_HOST = Variable.get("SGE_HOST")
    SGE_PORT = Variable.get("SGE_PORT")

    # SYNC
    SYNC_USERNAME = Variable.get("SYNC_USERNAME")
    SYNC_PASSWORD = Variable.get("QUIVER_PASSWORD")
    SYNC_DBNAME = Variable.get("SYNC_DBNAME")
    SYNC_HOST = Variable.get("SYNC_HOST")
    SYNC_PORT = Variable.get("SYNC_PORT")

    # DW Corporativo
    DW_CORPORATIVO_USERNAME = Variable.get("DW_CORPORATIVO_USERNAME")
    DW_CORPORATIVO_PASSWORD = Variable.get("DW_CORPORATIVO_PASSWORD")
    DW_CORPORATIVO_DBNAME = Variable.get("DW_CORPORATIVO_DBNAME")
    DW_CORPORATIVO_HOST = Variable.get("DW_CORPORATIVO_HOST")
    DW_CORPORATIVO_PORT = Variable.get("DW_CORPORATIVO_PORT")

    # Newcon
    NEWCON_USERNAME = Variable.get("NEWCON_USERNAME")
    NEWCON_PASSWORD =Variable.get("NEWCON_PASSWORD")
    NEWCON_DBNAME = Variable.get("NEWCON_DBNAME")
    NEWCON_HOST = Variable.get("NEWCON_HOST")
    NEWCON_HOST_NAME = Variable.get("NEWCON_HOST_NAME")
    NEWCON_HOST_2 = Variable.get("NEWCON_HOST_2")
    NEWCON_HOST_NAME_2 = Variable.get("NEWCON_HOST_NAME_2")
    NEWCON_PORT = Variable.get("NEWCON_PORT")

    # Koneq
    KONEQ_USERNAME = Variable.get("KONEQ_USERNAME")
    KONEQ_PASSWORD = Variable.get("KONEQ_PASSWORD")
    KONEQ_DBNAME = Variable.get("KONEQ_DBNAME")
    KONEQ_HOST = Variable.get("KONEQ_HOST")
    KONEQ_PORT = Variable.get("KONEQ_PORT")

    # Groot/Space
    SPACE_USERNAME = Variable.get("SPACE_USERNAME")
    SPACE_PASSWORD = Variable.get("SPACE_PASSWORD")
    SPACE_DBNAME = Variable.get("SPACE_DBNAME")
    SPACE_HOST = Variable.get("SPACE_HOST")
    SPACE_PORT = Variable.get("SPACE_PORT")

    # NetAdmin
    NETADMIN_USERNAME = Variable.get("NETADMIN_USERNAME")
    NETADMIN_PASSWORD = Variable.get("NETADMIN_PASSWORD")
    NETADMIN_DBNAME = Variable.get("NETADMIN_DBNAME")
    NETADMIN_AUD_DBNAME = Variable.get("NETADMIN_AUD_DBNAME")
    NETADMIN_HOST = Variable.get("NETADMIN_HOST")
    NETADMIN_PORT = Variable.get("NETADMIN_PORT")

    # Sharepoint
    SHAREPOINT_USERNAME = Variable.get("SHAREPOINT_USERNAME")
    SHAREPOINT_PASSWORD = Variable.get("SHAREPOINT_PASSWORD")
    SITE_URL = Variable.get("SITE_URL")

    WINDOWS_USERNAME = Variable.get("WINDOWS_USERNAME")
    WINDOWS_PASSWORD = Variable.get("WINDOWS_PASSWORD")
    LINUX_SERVER = Variable.get("LINUX_SERVER")

    # Blob
    BLOB_URL = Variable.get("BLOB_URL")
    BLOB_CREDENTIAL = Variable.get("BLOB_TOKEN")

    # BotMaker
    DW_BOTMAKER_SCHEMA = Variable.get("DW_BOTMAKER_SCHEMA")
    BOTMAKER_URL_ACCESSTOKEN = Variable.get("BOTMAKER_URL_ACCESSTOKEN")
    BOTMAKER_URL_BASE = Variable.get("BOTMAKER_URL_BASE")
    BOTMAKER_CLIENT_ID = Variable.get("BOTMAKER_CLIENT_ID")
    BOTMAKER_SECRET_ID = Variable.get("BOTMAKER_SECRET_ID")
    BOTMAKER_REFRESH_TOKEN = Variable.get("BOTMAKER_REFRESH_TOKEN")

    # Api Licensas Office365
    OFFICE365_API_LICENSAS_CLIENT_ID = Variable.get("OFFICE365_LICENSAS_CLIENT_ID_TOKEN")
    OFFICE365_API_LICENSAS_SECRET_ID = Variable.get("OFFICE365_LICENSAS_SECRET_ID")
    OFFICE365_API_LICENSAS_TENANT_ID = Variable.get("OFFICE365_LICENSAS_TENANT_ID_TOKEN")

    #Airflow
    AIRFLOW_USERNAME = Variable.get("AIRFLOW_USERNAME")
    AIRFLOW_PASSWORD = Variable.get("AIRFLOW_PASSWORD")
    
    # Dados para API do Vianuvem
    VIANUVEM_USER = Variable.get("VIANUVEM_USER")
    VIANUVEM_PASSWORD = Variable.get("VIANUVEM_PASSWORD")
    VIANUVEM_URL_GERAR_TOKEN_JWT = Variable.get("VIANUVEM_URL_GERAR_TOKEN_JWT")
    VIANUVEM_URL_CONSULTA_DOCUMENTOS = Variable.get("VIANUVEM_URL_CONSULTA_DOCUMENTOS")
    # Usúarios para consulta de documentos no Vianuvem
    SEARCH_USERS = ['Victoria Leite Soares Santos', 'Geovanna Quezia O. de Cassia', 'Dayane Fernanda Souza Trindade', 'Geisiane Georgia de Oliveira', 'geisiane.oliveira']