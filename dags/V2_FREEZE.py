from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

instances = [
    # Nbs
    Freeze("estoque").sys('nbs')
    .query(Query.Freeze.nbs_estoque),
    
    # Dealer
    Freeze("estoque").sys('dealer')
    .query(Query.Freeze.dealer_estoque),
    <PERSON>ze("estoque_filiais").sys('dealer')
    .query(Query.Freeze.dealer_estoque_filiais),
    <PERSON>ze("estoque_maquinas").sys('dealer')
    .query(Query.Freeze.dealer_estoque_maquinas),
    Freeze("estoque_pecas").sys('dealer')
    .query(Query.Freeze.dealer_estoque_pecas),
    <PERSON>ze("estoque_pecas_analitico").sys('dealer')
    .query(Query.Freeze.dealer_estoque_pecas_analitico),
    <PERSON>ze("os_pendentes").sys('dealer')
    .query(Query.Freeze.dealer_os_pendentes),

    # Dw Corporativo
    Freeze("posicao_estoque")
    .query(Query.Freeze.dw_posicao_estoque),
    Freeze("office365_license").scale()
    .query(Query.Freeze.dw_office365_license),
    Freeze("posicao_pecas_os")
    .query(Query.Freeze.dw_posicao_pecas_os)   
   ]

with DAG(
    'V2-FREEZE',
    default_args=default_args,
    description='DAG para ETL freeze de dados da BAMAQ',
    schedule_interval=None,
    max_active_runs=1,
    max_active_tasks=12, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, instances, triggers=['V2-SILVER'])

    start_etl_task >> end_etl_task >> dag_stats