from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

first_instances = [
    # Bronze('netadmin_aud').add_tables([
    #     {
    #     "Office365License": {
    #         "times_per_day": 1
    #     },
    #     "Office365User": {
    #         "times_per_day": 1
    #     }
    #     }
    # ]),
    Custom( func = insert_licensas_office365,
        times_per_day = 2
    ),
    Custom( func = freeze_dados_consolidados_contas_a_receber,
        times_per_day = 5
    ),
]

second_instances = [
    Custom( func = run_etl,
        times_per_day = 3
    ),
    Custom( func = etl_dados_inter,
        index = {
            "financeiro_receber": "bq_pr_INTER_ParcelasReceber",
            "financeiro_recebido": "bq_pr_INTER_ParcelasRecebidas",
            "financeiro_atrasado": "bq_pr_INTER_ParcelasAtraso",
            "remessa_mensal": "bq_pr_INTER_RemessaMensal"
        },
        times_per_day = 1
    ),
    Custom( func = merge_office365_licenses,
        times_per_day = 2
    ),
    Custom( func = merge_sessions_botmaker,
        times_per_day = 5
    ),
    Custom(func= process_mefepm,
           times_per_day = 2
    ),
    Custom( func = ETL_marketshare,
        times_per_day = 5
    ),
    Custom( func = ETL_metas_consorcio,
        times_per_day = 1
    )
]

    # Custom( func = etl_vianuvem_insert,
    #     conn_type = 'api',
    #     requests = {
    #         'token': {
    #             'url': VIANUVEM_URL_GERAR_TOKEN_JWT,
    #             'payload': {
    #                 'login': VIANUVEM_USER,
    #                 'pass': VIANUVEM_PASSWORD,
    #                 'encryptedPass': 'false'
    #             },
    #             'headers': {
    #                 'Content-Type': 'application/json'
    #             },
    #             'fieldname': 'token'
    #         },
    #         'consulta_documentos': {
    #             'type': 'post',
    #             'url': VIANUVEM_URL_CONSULTA_DOCUMENTOS,
    #             'payload': {
    #                 'initialDate': 'var_initialDate 00:00:00',
    #                 'finalDate': 'var_finalDate 23:59:59',
    #                 'hits': 1000,
    #                 'searchFor': 'var_searchFor'
    #             },
    #             'headers': {
    #                 'Authorization': f'Bearer var_token',
    #                 'Content-Type': 'application/json'
    #             }
    #         }
    #     },
    #     times_per_day = 5
    # ),
    # Custom( func = etl_vianuvem_update,
    #     conn_type = 'api',
    #     requests = {
    #         'token': {
    #             'url': VIANUVEM_URL_GERAR_TOKEN_JWT,
    #             'payload': {
    #                 'login': VIANUVEM_USER,
    #                 'pass': VIANUVEM_PASSWORD,
    #                 'encryptedPass': 'false'
    #             },
    #             'headers': {
    #                 'Content-Type': 'application/json'
    #             },
    #             'fieldname': 'token'
    #         },
    #         'consulta_documentos_atualizar': {
    #             'type': 'post',
    #             'url': VIANUVEM_URL_CONSULTA_DOCUMENTOS,
    #             'payload': {
    #                 'documentId': 'var_documentId',
    #                 'hits': 1000
    #             },
    #             'headers': {
    #                 'Authorization': f'Bearer var_token',
    #                 'Content-Type': 'application/json'
    #             }
    #         }
    #     },
    #     times_per_day = 4
    # ),
    # Custom( func = etl_meetime_insert,
    #     conn_type = 'api',
    #     token = MEETIME_AUTHORIZATION,
    #     requests = {
    #         'coleta_prospections': {
    #             'type': 'get',
    #             'url': "https://api.meetime.com.br/v2/prospections?start=var_start&created_after=var_data",
    #             'headers': {
    #                 "accept": "application/json",
    #                 "Authorization": "var_token"
    #             }
    #         },
    #         'coleta_leads': {
    #             'type': 'get',
    #             'url': "https://api.meetime.com.br/v2/leads?start=var_start&lead_created_after=var_data",
    #             'headers': {
    #                 "accept": "application/json",
    #                 "Authorization": "var_token"
    #             }
    #         },
    #         'coleta_activities': {
    #             'type': 'get',
    #             'url': "https://api.meetime.com.br/v2/prospections/activities?start=var_start&executed_after=var_data",
    #             'headers': {
    #                 "accept": "application/json",
    #                 "Authorization": "var_token"
    #             }
    #         }
    #     },
    #     times_per_day = 5
    # ),
    # Custom( func = etl_meetime_update,
    #     conn_type = 'api',
    #     token = MEETIME_AUTHORIZATION,
    #     requests = {
    #         'coleta_prospections_updated': {
    #             'type': 'get',
    #             'url': "https://api.meetime.com.br/v2/prospections?id=var_id",
    #             'headers': {
    #                 "accept": "application/json",
    #                 "Authorization": "var_token"
    #             }
    #         },
    #         'coleta_leads_updated': {
    #             'type': 'get',
    #             'url': "https://api.meetime.com.br/v2/leads?start=var_start&lead_updated_after=var_data",
    #             'headers': {
    #                 "accept": "application/json",
    #                 "Authorization": "var_token"
    #             }
    #         },
    #         'coleta_activities_updated': {
    #             'type': 'get',
    #             'url': "https://api.meetime.com.br/v2/prospections/activities?start=var_start&updated_after=var_data",
    #             'headers': {
    #                 "accept": "application/json",
    #                 "Authorization": "var_token"
    #             }
    #         }
    #     },
    #     times_per_day = 5
    # ),
    # Custom( func = atualiza_parquet_sessoes,
    #     conn_type = 'api',
    #     requests = {
    #         'token': {
    #             'url': BOTMAKER_URL_ACCESSTOKEN,
    #             'headers': {
    #                 'Content-Type': 'application/json',
    #                 'clientId': BOTMAKER_CLIENT_ID,
    #                 'secretId': BOTMAKER_SECRET_ID,
    #                 'refreshToken': BOTMAKER_REFRESH_TOKEN
    #             },
    #             'fieldname': 'accessToken'
    #         }
    #     },
    #     times_per_day = 1
    # ),
    # ,
    # Custom(func = execute_update_erp,
    #        times_per_day = 5
    # )
 
with DAG(
    'V2-FLEX',
    default_args=default_args,
    description='DAG para ETL flexível da BAMAQ',
    schedule_interval=None,
    max_active_runs=1,
    max_active_tasks=3, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, [first_instances, second_instances] , triggers=[])

    start_etl_task >> end_etl_task >> dag_stats