from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

first_instances = [
    View("autom").times_per_day(5)
    .query(
        Query.View.autom
    ),
    View("faturamento_sistemas_silver").times_per_day(1)
    .query(
        Query.View.faturamento_sistemas_silver
    ).index({
        "idx_view_faturamento_silver_doc_cli": ["doccli"],
        "idx_view_faturamento_silver_codempresa": ["codempresa"],
        "idx_view_faturamento_silver_chave_sistemaorigem": ["chave_faturamento", "sistemaorigem"]
    }),
    View("cx_vendas_newcon").times_per_day(5)
    .query(
        Query.View.cx_vendas_newcon
    )
]

second_instances = [
    View("atualizacao")
    .query(
        Query.View.atualizacao
    ),
]

with DAG(
    'V2-VIEW',
    default_args=default_args,
    description='DAG para ETL view de dados da BAMAQ',
    schedule_interval=None,
    max_active_runs=1,
    max_active_tasks=12, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, [first_instances, second_instances], triggers=[])

    start_etl_task >> end_etl_task >> dag_stats