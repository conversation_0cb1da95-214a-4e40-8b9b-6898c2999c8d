"""
Configuração do Sistema Syonet - ETL Framework

Configurações específicas do Syonet baseadas nas DAGs V3.
Refatorado para nova arquitetura: origem única (SQL Server), destino único (DW).
"""

from ...config.system_config import SystemConfig
from ...config.table_config import TableConfig, TableType, IncrementalMode
from ...config.database_config import DatabaseConfig, DatabaseType
from ...config.dw_config import DWConfig
from ...config.performance_config import get_global_performance_config
from ...silver.transformation import create_syonet_transformations
from .tables_config_v3_migrated import get_syonet_tables_config  # ✅ Importa configuração migrada do V3


def create_syonet_system_config(dw_config: DWConfig) -> SystemConfig:
    """
    Cria configuração completa do sistema Syonet
    ✅ Migrado do V3 com 100% de compatibilidade
    🚀 OTIMIZADO: Usa configurações globais de performance
    """
    
    # 🚀 OTIMIZAÇÃO: Obtém configurações globais de performance
    perf_config = get_global_performance_config()
    optimized_settings = perf_config.get_optimized_settings()
    
    # 🚀 OTIMIZAÇÃO: Configuração da origem com settings de performance globais
    source_config = DatabaseConfig(
        name="syonet_source",
        db_type=DatabaseType.SQLSERVER,
        host="***********",
        port=50666,
        database="master",
        user="bq_dwcorporativo_u",
        password="N#OK+#{Yx*",
        timeout=perf_config.connection_timeout
    )
    
    # 🚀 OTIMIZAÇÃO: Configuração do sistema usando settings globais otimizados
    system_config = SystemConfig(
        name="syonet",
        description="Sistema CRM Syonet - Dados de vendas, clientes e eventos (OTIMIZADO)",
        source_db_config=source_config,
        default_chunk_size=optimized_settings['default_chunk_size'],
        default_timeout=900,  # 15 minutos para consultas otimizadas
        max_parallel_tables=2,  # Reduzido para evitar contenção
        enable_smart_incremental=True,
        enable_validation=optimized_settings['enable_validation'],
        # 🚀 Configurações dinâmicas de performance
        enable_production_mode=perf_config.is_production_mode(),
        validation_cache_ttl=optimized_settings['validation_cache_ttl'],
        enable_connection_sharing=False,
        enable_lazy_loading=perf_config.enable_lazy_loading,
        # 🚨 OTIMIZAÇÃO CRÍTICA: Reduzir retries para evitar execuções múltiplas
        max_retries=0,  # Desabilitar retries automáticos
        retry_delay_minutes=5  # Aumentar delay se retry for necessário
    )
    
    # ✅ Adiciona configurações de tabelas bronze migradas do V3
    _add_syonet_bronze_tables_v3_migrated(system_config)

    # Adiciona transformações silver
    silver_transformations = create_syonet_transformations()
    for transformation in silver_transformations:
        system_config.add_silver_transformation(transformation)

    return system_config


def _add_syonet_bronze_tables_v3_migrated(system_config: SystemConfig):
    """
    ✅ Adiciona configurações de tabelas do Syonet migradas do V3
    Usa a configuração completa migrada do TABLES_CONFIG das DAGs V3
    """
    # ✅ Obtém configuração migrada do V3 (100% compatível)
    tables_config = get_syonet_tables_config()
    
    # ✅ Adiciona todas as tabelas ao SystemConfig
    for table_config in tables_config:
        system_config.add_bronze_table(table_config)


def create_syonet_source_config() -> DatabaseConfig:
    """Cria configuração da origem Syonet (SQL Server)"""
    return DatabaseConfig(
        name="syonet_source",
        type=DatabaseType.SQLSERVER,
        host="***********",
        port=50666,
        database="master",
        user="bq_dwcorporativo_u",
        password="N#OK+#{Yx*",
        timeout=300,
        description="SQL Server Syonet - Sistema CRM"
    )
