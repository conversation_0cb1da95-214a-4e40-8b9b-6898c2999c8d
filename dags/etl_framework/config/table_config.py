"""
Configuração de Tabelas - ETL Framework

Sistema flexível de configuração de tabelas baseado na lógica das DAGs V3.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum


class TableType(Enum):
    """Tipos de tabela para estratégias automáticas"""
    SMALL = "small"          # Tabelas pequenas (sempre full load)
    LARGE = "large"          # Tabelas grandes (incremental com fallback)
    CUSTOM = "custom"        # Tabelas com lógica customizada


class IncrementalMode(Enum):
    """Modos de incremental disponíveis"""
    DAILY = "daily"          # Incremental diário
    SEVEN_DAYS = "seven_days"  # Incremental 7 dias
    SMART = "smart"          # Estratégia inteligente com fallback
    FULL_ONLY = "full_only"  # Apenas full load


@dataclass
class TableConfig:
    """
    Configuração completa de uma tabela para ETL
    
    Baseado na estrutura TABLES_CONFIG das DAGs V3 mas mais flexível
    """
    name: str
    table_type: TableType = TableType.LARGE
    incremental_mode: IncrementalMode = IncrementalMode.SMART
    
    # Configurações básicas
    source_table: Optional[str] = None
    id_field: Optional[str] = None
    select_fields: Optional[str] = None
    
    # Configurações de schema
    source_schema: str = "public"
    target_schema: str = "bronze"
    table_prefix: str = ""
    
    # Configurações incrementais
    incremental_fields: str = "dt_inc,dt_alt"
    daily_filter: Optional[str] = None
    seven_days_filter: Optional[str] = None
    
    # SQLs customizados
    custom_sql: Optional[Dict[str, str]] = None
    
    # Configurações de processamento
    chunk_size: int = 50000
    timeout_seconds: int = 300
    max_gap_percentage: float = 10.0
    
    # Configurações de validação
    enable_validation: bool = True
    max_abs_diff: int = 10
    
    # Permissões customizadas
    custom_grants: Optional[List[str]] = None
    
    # Metadados adicionais
    description: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Validações e configurações automáticas após inicialização"""
        # Se não tem id_field, é tabela pequena
        if not self.id_field:
            self.table_type = TableType.SMALL
            self.incremental_mode = IncrementalMode.FULL_ONLY
        
        # Se não tem source_table, usa o próprio nome
        if not self.source_table:
            self.source_table = self.name
        
        # Configura filtros padrão se não especificados
        if not self.daily_filter:
            self.daily_filter = self._build_default_daily_filter()
        
        if not self.seven_days_filter:
            self.seven_days_filter = self._build_default_seven_days_filter()
    
    def _build_default_daily_filter(self) -> str:
        """Constrói filtro diário padrão baseado nas DAGs V3"""
        return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                  OR
                  TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))"""

    def _build_default_seven_days_filter(self) -> str:
        """Constrói filtro 7 dias padrão baseado nas DAGs V3"""
        return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                  OR
                  TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')"""
    
    def get_full_table_name(self) -> str:
        """Retorna nome completo da tabela de destino"""
        return f"{self.target_schema}.{self.table_prefix}{self.name}"
    
    def get_source_full_name(self) -> str:
        """Retorna nome completo da tabela de origem"""
        return f"{self.source_schema}.{self.source_table}"
    
    def is_small_table(self) -> bool:
        """Verifica se é tabela pequena (sempre full load)"""
        return self.table_type == TableType.SMALL or not self.id_field
    
    def has_custom_sql(self, sql_type: str = None) -> bool:
        """Verifica se tem SQL customizado"""
        if not self.custom_sql:
            return False
        
        if sql_type:
            return sql_type in self.custom_sql
        
        return len(self.custom_sql) > 0
    
    def get_custom_sql(self, sql_type: str) -> Optional[str]:
        """Obtém SQL customizado por tipo"""
        if not self.custom_sql:
            return None
        
        return self.custom_sql.get(sql_type)
    
    def should_use_smart_incremental(self) -> bool:
        """Verifica se deve usar estratégia inteligente"""
        return (self.incremental_mode == IncrementalMode.SMART and 
                not self.is_small_table())
    
    def to_table_metadata(self):
        """Converte para TableMetadata (compatibilidade com strategies)"""
        from ..strategies.base import TableMetadata
        
        return TableMetadata(
            name=self.name,
            source_table=self.source_table,
            id_field=self.id_field,
            select_fields=self.select_fields,
            custom_sql=self.custom_sql,
            incremental_field=self.incremental_fields,
            schema=self.source_schema,
            target_schema=self.target_schema,
            table_prefix=self.table_prefix
        )
    
    @classmethod
    def from_dict(cls, name: str, config_dict: Dict[str, Any]) -> 'TableConfig':
        """Cria TableConfig a partir de dicionário (compatibilidade com DAGs V3)"""
        return cls(
            name=name,
            source_table=config_dict.get('source'),
            id_field=config_dict.get('id_field'),
            select_fields=config_dict.get('select'),
            custom_sql=config_dict.get('custom_sql'),
            table_type=TableType.SMALL if not config_dict.get('id_field') else TableType.LARGE,
            incremental_mode=IncrementalMode.FULL_ONLY if not config_dict.get('id_field') else IncrementalMode.SMART
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'name': self.name,
            'table_type': self.table_type.value,
            'incremental_mode': self.incremental_mode.value,
            'source_table': self.source_table,
            'id_field': self.id_field,
            'select_fields': self.select_fields,
            'source_schema': self.source_schema,
            'target_schema': self.target_schema,
            'table_prefix': self.table_prefix,
            'incremental_fields': self.incremental_fields,
            'custom_sql': self.custom_sql,
            'chunk_size': self.chunk_size,
            'timeout_seconds': self.timeout_seconds,
            'max_gap_percentage': self.max_gap_percentage,
            'enable_validation': self.enable_validation,
            'max_abs_diff': self.max_abs_diff,
            'custom_grants': self.custom_grants,
            'description': self.description,
            'tags': self.tags
        }
