"""
Configuração de Filtros - ETL Framework

Sistema flexível de filtros incrementais para diferentes cenários.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from enum import Enum


class FilterType(Enum):
    """Tipos de filtro disponíveis"""
    DAILY = "daily"
    SEVEN_DAYS = "seven_days"
    MONTHLY = "monthly"
    CUSTOM_DATE_RANGE = "custom_date_range"
    CUSTOM_CONDITION = "custom_condition"


@dataclass
class FilterConfig:
    """
    Configuração de filtros incrementais
    
    Permite definir filtros flexíveis para diferentes estratégias incrementais
    """
    name: str
    filter_type: FilterType
    condition: str
    description: Optional[str] = None
    
    # Configurações específicas para filtros de data
    date_fields: Optional[List[str]] = None
    date_format: str = "timestamp_ms"  # timestamp_ms, timestamp_s, date, datetime
    
    # Parâmetros para filtros customizados
    parameters: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Configurações automáticas após inicialização"""
        if not self.date_fields and self.filter_type in [FilterType.DAILY, FilterType.SEVEN_DAYS, FilterType.MONTHLY]:
            # Campos padrão baseados nas DAGs V3
            self.date_fields = ["dt_inc", "dt_alt"]
    
    def build_filter_condition(self, **kwargs) -> str:
        """
        Constrói condição de filtro baseada no tipo e parâmetros
        """
        if self.filter_type == FilterType.DAILY:
            return self._build_daily_filter()
        elif self.filter_type == FilterType.SEVEN_DAYS:
            return self._build_seven_days_filter()
        elif self.filter_type == FilterType.MONTHLY:
            return self._build_monthly_filter()
        elif self.filter_type == FilterType.CUSTOM_DATE_RANGE:
            return self._build_custom_date_range_filter(**kwargs)
        elif self.filter_type == FilterType.CUSTOM_CONDITION:
            return self._build_custom_condition_filter(**kwargs)
        else:
            return self.condition
    
    def _build_daily_filter(self) -> str:
        """Constrói filtro diário baseado nas DAGs V3"""
        if self.date_format == "timestamp_ms":
            conditions = []
            for field in self.date_fields:
                conditions.append(f"TIMESTAMP ''epoch'' + {field} * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)")
            return f"({' OR '.join(conditions)})"
        else:
            # Para outros formatos de data
            conditions = []
            for field in self.date_fields:
                conditions.append(f"{field} >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)")
            return f"({' OR '.join(conditions)})"
    
    def _build_seven_days_filter(self) -> str:
        """Constrói filtro 7 dias baseado nas DAGs V3"""
        if self.date_format == "timestamp_ms":
            conditions = []
            for field in self.date_fields:
                conditions.append(f"TIMESTAMP ''epoch'' + {field} * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''")
            return f"({' OR '.join(conditions)})"
        else:
            # Para outros formatos de data
            conditions = []
            for field in self.date_fields:
                conditions.append(f"{field} >= CURRENT_TIMESTAMP - INTERVAL ''7 days''")
            return f"({' OR '.join(conditions)})"
    
    def _build_monthly_filter(self) -> str:
        """Constrói filtro mensal"""
        if self.date_format == "timestamp_ms":
            conditions = []
            for field in self.date_fields:
                conditions.append(f"TIMESTAMP ''epoch'' + {field} * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''month'', CURRENT_TIMESTAMP)")
            return f"({' OR '.join(conditions)})"
        else:
            # Para outros formatos de data
            conditions = []
            for field in self.date_fields:
                conditions.append(f"{field} >= DATE_TRUNC(''month'', CURRENT_TIMESTAMP)")
            return f"({' OR '.join(conditions)})"
    
    def _build_custom_date_range_filter(self, start_date: str = None, end_date: str = None, **kwargs) -> str:
        """Constrói filtro de range de datas customizado"""
        if not start_date and not end_date:
            return self.condition
        
        conditions = []
        for field in self.date_fields:
            field_conditions = []
            
            if start_date:
                if self.date_format == "timestamp_ms":
                    field_conditions.append(f"TIMESTAMP ''epoch'' + {field} * INTERVAL ''1 millisecond'' >= ''{start_date}''")
                else:
                    field_conditions.append(f"{field} >= ''{start_date}''")
            
            if end_date:
                if self.date_format == "timestamp_ms":
                    field_conditions.append(f"TIMESTAMP ''epoch'' + {field} * INTERVAL ''1 millisecond'' <= ''{end_date}''")
                else:
                    field_conditions.append(f"{field} <= '{end_date}'")
            
            if field_conditions:
                conditions.append(f"({' AND '.join(field_conditions)})")
        
        return f"({' OR '.join(conditions)})" if conditions else self.condition
    
    def _build_custom_condition_filter(self, **kwargs) -> str:
        """Constrói filtro com condição customizada"""
        condition = self.condition
        
        # Substitui parâmetros se fornecidos
        if self.parameters:
            for param, default_value in self.parameters.items():
                value = kwargs.get(param, default_value)
                condition = condition.replace(f"{{{param}}}", str(value))
        
        # Substitui parâmetros diretos do kwargs
        for key, value in kwargs.items():
            condition = condition.replace(f"{{{key}}}", str(value))
        
        return condition
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'name': self.name,
            'filter_type': self.filter_type.value,
            'condition': self.condition,
            'description': self.description,
            'date_fields': self.date_fields,
            'date_format': self.date_format,
            'parameters': self.parameters
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'FilterConfig':
        """Cria FilterConfig a partir de dicionário"""
        return cls(
            name=config_dict['name'],
            filter_type=FilterType(config_dict['filter_type']),
            condition=config_dict['condition'],
            description=config_dict.get('description'),
            date_fields=config_dict.get('date_fields'),
            date_format=config_dict.get('date_format', 'timestamp_ms'),
            parameters=config_dict.get('parameters')
        )
    
    @classmethod
    def create_daily_filter(cls, name: str = "daily_default") -> 'FilterConfig':
        """Cria filtro diário padrão baseado nas DAGs V3"""
        return cls(
            name=name,
            filter_type=FilterType.DAILY,
            condition="",  # Será construído automaticamente
            description="Filtro incremental diário padrão",
            date_fields=["dt_inc", "dt_alt"],
            date_format="timestamp_ms"
        )
    
    @classmethod
    def create_seven_days_filter(cls, name: str = "seven_days_default") -> 'FilterConfig':
        """Cria filtro 7 dias padrão baseado nas DAGs V3"""
        return cls(
            name=name,
            filter_type=FilterType.SEVEN_DAYS,
            condition="",  # Será construído automaticamente
            description="Filtro incremental 7 dias padrão",
            date_fields=["dt_inc", "dt_alt"],
            date_format="timestamp_ms"
        )
