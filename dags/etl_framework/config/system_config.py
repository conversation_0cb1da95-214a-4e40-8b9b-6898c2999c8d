"""
Configuração de Sistema - ETL Framework

Configuração por sistema de origem (Syonet, Oracle ERP, etc.) com destino único no DW.
Reflete a arquitetura real: múltiplas origens, destino único.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from .table_config import TableConfig
from .database_config import DatabaseConfig


@dataclass
class SystemConfig:
    """
    Configuração de um sistema de origem para ETL

    Representa um sistema de origem (Syonet, Oracle ERP, etc.) que será
    extraído para o Data Warehouse Corporativo (destino único).

    Agrupa:
    - Configuração de conexão com sistema de origem
    - Configurações de tabelas bronze
    - Configurações de transformações silver
    - Metadados específicos do sistema
    """
    name: str
    description: str

    # Configuração de conexão com sistema de origem
    source_db_config: DatabaseConfig

    # Configurações de tabelas bronze
    bronze_tables: Dict[str, TableConfig] = field(default_factory=dict)

    # Configurações de transformações silver
    silver_transformations: List = field(default_factory=list)
    
    # Configurações globais do sistema
    default_chunk_size: int = 50000
    default_timeout: int = 300
    default_max_gap_percentage: float = 10.0

    # Configurações de processamento
    max_parallel_tables: int = 5
    enable_smart_incremental: bool = True
    enable_validation: bool = True

    # 🚀 OTIMIZAÇÃO: Configurações de performance
    enable_production_mode: bool = False
    validation_cache_ttl: int = 300  # Cache TTL em segundos
    enable_connection_sharing: bool = False
    enable_lazy_loading: bool = True

    # Configurações de retry
    max_retries: int = 2
    retry_delay_minutes: int = 1

    # Campos incrementais específicos do sistema
    incremental_fields: List[str] = field(default_factory=lambda: ["dt_inc", "dt_alt"])

    # Metadados
    version: str = "2.0.0"
    tags: List[str] = field(default_factory=list)
    
    def add_bronze_table(self, table_config: TableConfig):
        """Adiciona configuração de tabela bronze"""
        # Aplica configurações padrão do sistema se não especificadas
        if table_config.chunk_size == 50000:  # Valor padrão
            table_config.chunk_size = self.default_chunk_size

        if table_config.timeout_seconds == 300:  # Valor padrão
            table_config.timeout_seconds = self.default_timeout

        if table_config.max_gap_percentage == 10.0:  # Valor padrão
            table_config.max_gap_percentage = self.default_max_gap_percentage

        # Define campos incrementais do sistema se não especificados
        if not table_config.incremental_fields:
            table_config.incremental_fields = ",".join(self.incremental_fields)

        self.bronze_tables[table_config.name] = table_config

    def add_silver_transformation(self, transformation):
        """Adiciona transformação silver"""
        self.silver_transformations.append(transformation)
    
    def get_bronze_table(self, table_name: str) -> Optional[TableConfig]:
        """Obtém configuração de tabela bronze"""
        return self.bronze_tables.get(table_name)

    def get_bronze_tables(self) -> List[TableConfig]:
        """Obtém todas as tabelas bronze"""
        return list(self.bronze_tables.values())

    def get_bronze_tables_by_type(self, table_type) -> List[TableConfig]:
        """Obtém tabelas bronze por tipo"""
        return [table for table in self.bronze_tables.values() if table.table_type == table_type]

    def get_small_bronze_tables(self) -> List[TableConfig]:
        """Obtém tabelas bronze pequenas (full load)"""
        from .table_config import TableType
        return self.get_bronze_tables_by_type(TableType.SMALL)

    def get_large_bronze_tables(self) -> List[TableConfig]:
        """Obtém tabelas bronze grandes (incremental)"""
        from .table_config import TableType
        return self.get_bronze_tables_by_type(TableType.LARGE)

    def get_silver_transformations(self) -> List:
        """Obtém todas as transformações silver"""
        return self.silver_transformations
    
    def get_tables_with_custom_sql(self) -> List[TableConfig]:
        """Obtém tabelas com SQL customizado"""
        return [table for table in self.bronze_tables.values() if table.has_custom_sql()]
    
    def validate_config(self) -> List[str]:
        """Valida configuração do sistema"""
        errors = []
        
        # Valida configurações de conexão
        if not self.source_db_config:
            errors.append("Configuração de banco origem não definida")
        
        # Valida tabelas
        if not self.bronze_tables:
            errors.append("Nenhuma tabela configurada")
        
        # Valida configurações de cada tabela
        for table_name, table_config in self.bronze_tables.items():
            if not table_config.name:
                errors.append(f"Tabela {table_name}: nome não definido")
            
            if not table_config.source_table:
                errors.append(f"Tabela {table_name}: tabela origem não definida")
        
        return errors
    
    def get_processing_order(self) -> List[str]:
        """
        Retorna ordem de processamento das tabelas
        
        Ordem baseada nas DAGs V3:
        1. Tabelas pequenas (full load rápido)
        2. Tabelas grandes sem dependências
        3. Tabelas com SQL customizado
        """
        small_tables = [t.name for t in self.get_small_bronze_tables()]
        large_tables = [t.name for t in self.get_large_bronze_tables()]
        custom_tables = [t.name for t in self.get_tables_with_custom_sql()]
        
        return small_tables + large_tables + custom_tables
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'name': self.name,
            'description': self.description,
            'source_db_config': self.source_db_config.to_dict() if self.source_db_config else None,
            'bronze_tables': {name: table.to_dict() for name, table in self.bronze_tables.items()},
            'silver_transformations': [t.to_dict() for t in self.silver_transformations],
            'default_chunk_size': self.default_chunk_size,
            'default_timeout': self.default_timeout,
            'default_max_gap_percentage': self.default_max_gap_percentage,
            'max_parallel_tables': self.max_parallel_tables,
            'enable_smart_incremental': self.enable_smart_incremental,
            'enable_validation': self.enable_validation,
            'max_retries': self.max_retries,
            'retry_delay_minutes': self.retry_delay_minutes,
            'version': self.version,
            'tags': self.tags
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'SystemConfig':
        """Cria SystemConfig a partir de dicionário"""
        system_config = cls(
            name=config_dict['name'],
            description=config_dict['description'],
            source_db_config=DatabaseConfig.from_dict(config_dict['source_db_config']) if config_dict.get('source_db_config') else None,
            default_chunk_size=config_dict.get('default_chunk_size', 50000),
            default_timeout=config_dict.get('default_timeout', 300),
            default_max_gap_percentage=config_dict.get('default_max_gap_percentage', 10.0),
            max_parallel_tables=config_dict.get('max_parallel_tables', 5),
            enable_smart_incremental=config_dict.get('enable_smart_incremental', True),
            enable_validation=config_dict.get('enable_validation', True),
            max_retries=config_dict.get('max_retries', 2),
            retry_delay_minutes=config_dict.get('retry_delay_minutes', 1),
            version=config_dict.get('version', '1.0.0'),
            tags=config_dict.get('tags', [])
        )
        
        # Adiciona tabelas
        tables_dict = config_dict.get('tables', {})
        for table_name, table_dict in tables_dict.items():
            table_config = TableConfig.from_dict(table_name, table_dict)
            system_config.add_table(table_config)
        
        return system_config
