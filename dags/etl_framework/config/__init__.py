"""
Módulo de Configuração - ETL Framework

Sistema de configuração flexível para tabelas, campos, filtros e estratégias:

- TableConfig: Configuração de tabelas
- SystemConfig: Configuração por sistema
- FilterConfig: Configuração de filtros incrementais
- DatabaseConfig: Configuração de bancos de dados
"""

from .table_config import TableConfig
from .system_config import SystemConfig
from .filter_config import FilterConfig
from .database_config import DatabaseConfig

__all__ = [
    'TableConfig',
    'SystemConfig',
    'FilterConfig',
    'DatabaseConfig'
]
