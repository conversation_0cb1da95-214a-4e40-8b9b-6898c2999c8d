"""
Strategy Factory - ETL Framework

Factory pattern para criação eficiente de estratégias ETL com reutilização de instâncias.
🚀 OTIMIZAÇÃO: Reduz overhead de instanciação através de pooling e reutilização.
"""

from typing import Dict, Any, Type, Optional
from .base import ETLStrategy, ETLMode
from .smart_incremental import SmartIncrementalStrategy
from .full_load import FullLoadStrategy
from .incremental_daily import IncrementalDailyStrategy
from .incremental_seven_days import IncrementalSevenDaysStrategy


class StrategyFactory:
    """
    Factory para criação eficiente de estratégias ETL
    
    🚀 OTIMIZAÇÃO: Implementa pooling de estratégias para reutilização
    - Reduz overhead de instanciação
    - Compartilha recursos entre execuções
    - Cache de configurações comuns
    """
    
    def __init__(self):
        # Pool de estratégias reutilizáveis
        self._strategy_pool: Dict[str, ETLStrategy] = {}
        self._strategy_registry: Dict[ETLMode, Type[ETLStrategy]] = {
            ETLMode.SMART_INCREMENTAL: SmartIncrementalStrategy,
            ETLMode.FULL_LOAD: FullLoadStrategy,
            ETLMode.INCREMENTAL_DAILY: IncrementalDailyStrategy,
            ETLMode.INCREMENTAL_SEVEN_DAYS: IncrementalSevenDaysStrategy
        }
        
        # Cache de argumentos para detecção de mudanças
        self._args_cache: Dict[str, tuple] = {}
    
    def get_strategy(self, 
                    mode: ETLMode,
                    source_connection,
                    target_connection,
                    chunk_size: int = 50000,
                    timeout_seconds: int = 300,
                    max_gap_percentage: float = 10.0,
                    **kwargs) -> ETLStrategy:
        """
        Obtém estratégia do pool ou cria nova se necessário
        
        🚀 OTIMIZAÇÃO: Reutiliza instâncias quando argumentos são iguais
        """
        # Cria chave única para o pool baseada em argumentos
        pool_key = self._create_pool_key(
            mode, source_connection, target_connection,
            chunk_size, timeout_seconds, max_gap_percentage
        )
        
        # Verifica se já existe no pool com mesmos argumentos
        if pool_key in self._strategy_pool:
            cached_args = self._args_cache.get(pool_key)
            current_args = (chunk_size, timeout_seconds, max_gap_percentage)
            
            # Se argumentos não mudaram, reutiliza instância
            if cached_args == current_args:
                return self._strategy_pool[pool_key]
        
        # Cria nova instância
        strategy_class = self._strategy_registry.get(mode)
        if not strategy_class:
            raise ValueError(f"Estratégia não encontrada para modo: {mode}")
        
        strategy = strategy_class(
            source_connection=source_connection,
            target_connection=target_connection,
            chunk_size=chunk_size,
            timeout_seconds=timeout_seconds,
            max_gap_percentage=max_gap_percentage,
            **kwargs
        )
        
        # Armazena no pool
        self._strategy_pool[pool_key] = strategy
        self._args_cache[pool_key] = (chunk_size, timeout_seconds, max_gap_percentage)
        
        return strategy
    
    def get_smart_incremental_strategy(self, 
                                     source_connection,
                                     target_connection,
                                     **kwargs) -> SmartIncrementalStrategy:
        """Wrapper específico para estratégia inteligente"""
        return self.get_strategy(
            ETLMode.SMART_INCREMENTAL,
            source_connection,
            target_connection,
            **kwargs
        )
    
    def get_full_load_strategy(self, 
                             source_connection,
                             target_connection,
                             **kwargs) -> FullLoadStrategy:
        """Wrapper específico para full load"""
        return self.get_strategy(
            ETLMode.FULL_LOAD,
            source_connection,
            target_connection,
            **kwargs
        )
    
    def _create_pool_key(self, mode: ETLMode, source_conn, target_conn, 
                        chunk_size: int, timeout: int, gap_pct: float) -> str:
        """Cria chave única para o pool"""
        source_id = id(source_conn)
        target_id = id(target_conn)
        return f"{mode.value}_{source_id}_{target_id}_{chunk_size}_{timeout}_{gap_pct}"
    
    def clear_pool(self):
        """Limpa pool de estratégias (útil para testes)"""
        self._strategy_pool.clear()
        self._args_cache.clear()
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do pool"""
        return {
            'pool_size': len(self._strategy_pool),
            'strategies_cached': list(self._strategy_pool.keys()),
            'memory_usage_estimate': len(self._strategy_pool) * 1000  # Estimativa em bytes
        }


# 🚀 OTIMIZAÇÃO: Instância global singleton para máxima reutilização
_global_strategy_factory: Optional[StrategyFactory] = None


def get_strategy_factory() -> StrategyFactory:
    """
    Obtém factory singleton global
    
    🚀 OTIMIZAÇÃO: Garante máxima reutilização entre diferentes processadores
    """
    global _global_strategy_factory
    if _global_strategy_factory is None:
        _global_strategy_factory = StrategyFactory()
    return _global_strategy_factory


def create_optimized_strategy(mode: ETLMode, 
                            source_connection,
                            target_connection,
                            **kwargs) -> ETLStrategy:
    """
    Função utilitária para criar estratégias otimizadas
    
    🚀 OTIMIZAÇÃO: Interface simples que usa factory global
    """
    factory = get_strategy_factory()
    return factory.get_strategy(
        mode, source_connection, target_connection, **kwargs
    )