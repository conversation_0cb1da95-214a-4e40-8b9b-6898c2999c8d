"""
Estratégia Full Load - ETL Framework

Implementa carga completa (drop/create) baseada na lógica das DAGs V3.
"""

from .base import ETLStrategy, ETLMode, TableMetadata, ETLResult
from typing import Dict, Any, Optional


class FullLoadStrategy(ETLStrategy):
    """
    Estratégia de Full Load
    
    Características:
    - Sempre dropa e recria a tabela
    - Carrega todos os dados da origem
    - Não usa filtros incrementais
    - Mais lenta mas mais confiável
    """
    
    def get_mode(self) -> ETLMode:
        return ETLMode.FULL_LOAD
    
    def should_use_strategy(self, table_metadata: TableMetadata, 
                          analysis_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        Full Load pode ser usado sempre como fallback
        
        Critérios para uso automático:
        - Tabela pequena (sem id_field configurado)
        - Falha em estratégias incrementais
        - Diferença muito grande entre origem e destino
        """
        # Se não tem campo ID, é tabela pequena - sempre full load
        if not table_metadata.id_field:
            return True
            
        # Se tem análise e a diferença é muito grande, usar full load
        if analysis_info:
            diff_percentage = analysis_info.get('diff_percentage', 0)
            if diff_percentage > self.max_gap_percentage:
                return True
                
        return False
    
    def build_extract_query(self, table_metadata: TableMetadata) -> str:
        """Constrói query de extração full load"""
        source_table = table_metadata.source_table or table_metadata.name
        select_fields = table_metadata.select_fields or "*"
        
        # Se tem SQL customizado para full load, usar
        if (table_metadata.custom_sql and 
            'full' in table_metadata.custom_sql):
            return table_metadata.custom_sql['full']
        
        # Query padrão baseada no tipo de conexão
        if hasattr(self.source_connection, 'execute_openquery'):
            # SQL Server com OPENQUERY
            return f"""
                SELECT {select_fields}
                FROM OPENQUERY(POSTGRES, 'SELECT {select_fields} FROM {table_metadata.schema}.{source_table}')
            """
        else:
            # Conexão direta
            return f"""
                SELECT {select_fields}
                FROM {table_metadata.schema}.{source_table}
            """
    
    def prepare_target_table(self, table_metadata: TableMetadata, 
                           sample_data=None) -> bool:
        """Prepara tabela para full load (drop/create)"""
        try:
            full_table_name = self.get_full_table_name(table_metadata)
            
            with self.target_connection.get_cursor() as (cursor, conn):
                # Drop table if exists
                drop_sql = f"DROP TABLE IF EXISTS {full_table_name}"
                cursor.execute(drop_sql)
                conn.commit()
                
                self.logger.info(f"Tabela {full_table_name} dropada para full load")
                
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao preparar tabela para full load: {str(e)}")
            return False
    
    def _execute_etl(self, table_metadata: TableMetadata, extract_query: str) -> int:
        """Executa ETL full load"""
        from ..processors.data_processor import DataProcessor
        
        processor = DataProcessor(
            source_connection=self.source_connection,
            target_connection=self.target_connection,
            chunk_size=self.chunk_size
        )
        
        return processor.transfer_table_data(
            extract_query=extract_query,
            table_metadata=table_metadata,
            is_full_load=True,
            delete_conditions=None
        )
