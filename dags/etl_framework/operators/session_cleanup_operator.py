"""
Operador Airflow para Limpeza Automática de Sessões - ETL Framework

Operador que identifica e mata automaticamente sessões problemáticas
que estejam impedindo a execução das rotinas ETL.
"""

import logging
from typing import Dict, Any, Optional
from airflow.models import BaseOperator
from airflow.utils.context import Context
from airflow.utils.decorators import apply_defaults

from ..utils.session_manager import create_session_manager
from ..config.dw_config import create_corporate_dw_config

logger = logging.getLogger(__name__)


class SessionCleanupOperator(BaseOperator):
    """
    Operador Airflow para limpeza automática de sessões problemáticas
    
    Este operador:
    1. Identifica sessões que estão rodando há muito tempo
    2. Detecta sessões bloqueadas por locks
    3. Encontra consultas duplicadas (múltiplas execuções)
    4. Mata automaticamente as sessões problemáticas
    """
    
    template_fields = ['max_duration_minutes', 'max_duplicate_queries']
    
    @apply_defaults
    def __init__(self,
                 max_duration_minutes: int = 10,
                 max_duplicate_queries: int = 2,
                 dry_run: bool = False,
                 fail_on_kill_errors: bool = False,
                 *args, **kwargs):
        """
        Inicializa o operador
        
        Args:
            max_duration_minutes: Duração máxima permitida para uma sessão (padrão: 10 min)
            max_duplicate_queries: Número máximo de consultas duplicadas (padrão: 2)
            dry_run: Se True, apenas identifica mas não mata sessões
            fail_on_kill_errors: Se True, falha a task se houver erro ao matar sessões
        """
        super().__init__(*args, **kwargs)
        self.max_duration_minutes = max_duration_minutes
        self.max_duplicate_queries = max_duplicate_queries
        self.dry_run = dry_run
        self.fail_on_kill_errors = fail_on_kill_errors
    
    def execute(self, context: Context) -> Dict[str, Any]:
        """
        Executa a limpeza de sessões problemáticas
        
        Args:
            context: Contexto do Airflow
            
        Returns:
            Dict: Relatório das ações executadas
        """
        logger.info("🧹 Iniciando limpeza automática de sessões...")
        
        try:
            # Criar configuração do DW e session manager
            dw_config = create_corporate_dw_config()
            session_manager = create_session_manager(dw_config)
            
            # Executar limpeza
            report = session_manager.kill_problematic_sessions(
                max_duration_minutes=self.max_duration_minutes,
                max_duplicate_queries=self.max_duplicate_queries,
                dry_run=self.dry_run
            )
            
            # Log do relatório
            self._log_report(report)
            
            # Verificar se deve falhar em caso de erros
            if self.fail_on_kill_errors and report['failed_kills']:
                raise Exception(f"Falha ao matar {len(report['failed_kills'])} sessões")
            
            # Retornar relatório para XCom
            return report
            
        except Exception as e:
            logger.error(f"❌ Erro na limpeza de sessões: {str(e)}")
            if self.fail_on_kill_errors:
                raise
            return {
                'error': str(e),
                'total_problematic': 0,
                'killed_sessions': [],
                'failed_kills': []
            }
    
    def _log_report(self, report: Dict[str, Any]):
        """
        Faz log detalhado do relatório
        
        Args:
            report: Relatório das ações executadas
        """
        total = report.get('total_problematic', 0)
        killed = len(report.get('killed_sessions', []))
        failed = len(report.get('failed_kills', []))
        
        if total == 0:
            logger.info("✅ Nenhuma sessão problemática encontrada")
            return
        
        if report.get('dry_run'):
            logger.info(f"🔍 [DRY RUN] Identificadas {total} sessões problemáticas")
        else:
            logger.warning(f"💀 Sessões processadas: {killed} mortas, {failed} falharam")
        
        # Log detalhado das sessões mortas
        for session in report.get('killed_sessions', []):
            action = session.get('action', 'UNKNOWN')
            pid = session.get('pid')
            reason = session.get('reason', 'Sem motivo')
            
            if action == 'DRY_RUN':
                logger.info(f"  🔍 [DRY RUN] PID {pid}: {reason}")
            else:
                logger.warning(f"  ✅ Morta PID {pid}: {reason}")
        
        # Log detalhado das falhas
        for session in report.get('failed_kills', []):
            pid = session.get('pid')
            reason = session.get('reason', 'Sem motivo')
            logger.error(f"  ❌ Falha PID {pid}: {reason}")


class PreTaskSessionCleanupOperator(SessionCleanupOperator):
    """
    Operador especializado para limpeza ANTES de executar transformações críticas
    
    Configurações mais agressivas para garantir que não há sessões impedindo
    a execução das transformações silver.
    """
    
    @apply_defaults
    def __init__(self, *args, **kwargs):
        # Configurações mais agressivas para pré-execução
        kwargs.setdefault('max_duration_minutes', 5)  # Mais restritivo
        kwargs.setdefault('max_duplicate_queries', 1)  # Não permite duplicatas
        kwargs.setdefault('dry_run', False)  # Mata sessões de verdade
        kwargs.setdefault('fail_on_kill_errors', False)  # Não falha se não conseguir matar
        
        super().__init__(*args, **kwargs)


class PostTaskSessionCleanupOperator(SessionCleanupOperator):
    """
    Operador especializado para limpeza APÓS transformações
    
    Configurações mais conservadoras para limpeza de manutenção.
    """
    
    @apply_defaults
    def __init__(self, *args, **kwargs):
        # Configurações mais conservadoras para pós-execução
        kwargs.setdefault('max_duration_minutes', 15)  # Mais permissivo
        kwargs.setdefault('max_duplicate_queries', 3)  # Permite mais duplicatas
        kwargs.setdefault('dry_run', False)
        kwargs.setdefault('fail_on_kill_errors', False)
        
        super().__init__(*args, **kwargs)


class SessionMonitorOperator(SessionCleanupOperator):
    """
    Operador especializado apenas para MONITORAMENTO (dry_run sempre True)
    
    Usado para identificar sessões problemáticas sem matá-las.
    """
    
    @apply_defaults
    def __init__(self, *args, **kwargs):
        # Sempre em modo dry_run para monitoramento
        kwargs['dry_run'] = True
        kwargs.setdefault('max_duration_minutes', 5)
        kwargs.setdefault('max_duplicate_queries', 1)
        kwargs.setdefault('fail_on_kill_errors', False)
        
        super().__init__(*args, **kwargs)


# Factory functions para facilitar o uso

def create_pre_task_cleanup(task_id: str = "cleanup_sessions_pre_task", **kwargs) -> PreTaskSessionCleanupOperator:
    """
    Cria operador de limpeza pré-execução
    
    Args:
        task_id: ID da task
        **kwargs: Argumentos adicionais
        
    Returns:
        PreTaskSessionCleanupOperator: Operador configurado
    """
    return PreTaskSessionCleanupOperator(
        task_id=task_id,
        **kwargs
    )


def create_post_task_cleanup(task_id: str = "cleanup_sessions_post_task", **kwargs) -> PostTaskSessionCleanupOperator:
    """
    Cria operador de limpeza pós-execução
    
    Args:
        task_id: ID da task
        **kwargs: Argumentos adicionais
        
    Returns:
        PostTaskSessionCleanupOperator: Operador configurado
    """
    return PostTaskSessionCleanupOperator(
        task_id=task_id,
        **kwargs
    )


def create_session_monitor(task_id: str = "monitor_sessions", **kwargs) -> SessionMonitorOperator:
    """
    Cria operador de monitoramento de sessões
    
    Args:
        task_id: ID da task
        **kwargs: Argumentos adicionais
        
    Returns:
        SessionMonitorOperator: Operador configurado
    """
    return SessionMonitorOperator(
        task_id=task_id,
        **kwargs
    )
