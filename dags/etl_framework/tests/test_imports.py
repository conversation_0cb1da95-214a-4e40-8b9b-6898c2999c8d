"""
Testes de Importação - ETL Framework

Testes para detectar problemas de importação antes do deploy.
"""

import unittest
import sys
import importlib
from typing import List, Tuple


class TestImports(unittest.TestCase):
    """
    Testa todas as importações críticas do framework
    """
    
    def test_main_framework_imports(self):
        """Testa importações principais do framework"""
        try:
            import etl_framework
            from etl_framework import (
                DatabaseConnection,
                BronzeProcessor,
                SilverProcessor,
                SystemConfig,
                DWConfig,
                ETLLogger,
                create_bronze_processor,
                create_silver_processor,
                create_corporate_dw_config,
                create_syonet_system_config
            )
            self.assertTrue(True, "Importações principais OK")
        except ImportError as e:
            self.fail(f"Falha na importação principal: {e}")
    
    def test_bronze_module_imports(self):
        """Testa importações do módulo bronze"""
        try:
            from etl_framework.bronze import (
                BronzeProcessor,
                create_bronze_processor,
                BronzeValidator
            )
            self.assertTrue(True, "Importações bronze OK")
        except ImportError as e:
            self.fail(f"Falha na importação bronze: {e}")
    
    def test_silver_module_imports(self):
        """Testa importações do módulo silver"""
        try:
            from etl_framework.silver import (
                SilverProcessor,
                create_silver_processor,
                SilverTransformation,
                SilverValidator
            )
            self.assertTrue(True, "Importações silver OK")
        except ImportError as e:
            self.fail(f"Falha na importação silver: {e}")
    
    def test_config_module_imports(self):
        """Testa importações do módulo config"""
        try:
            from etl_framework.config import (
                DWConfig,
                SystemConfig,
                TableConfig,
                DatabaseConfig
            )
            from etl_framework.config.dw_config import create_corporate_dw_config
            self.assertTrue(True, "Importações config OK")
        except ImportError as e:
            self.fail(f"Falha na importação config: {e}")
    
    def test_connections_imports(self):
        """Testa importações de conexões"""
        try:
            from etl_framework.connections import (
                DatabaseConnection,
                PostgreSQLConnection,
                SQLServerConnection
            )
            self.assertTrue(True, "Importações connections OK")
        except ImportError as e:
            self.fail(f"Falha na importação connections: {e}")
    
    def test_utils_imports(self):
        """Testa importações de utilitários"""
        try:
            from etl_framework.utils import (
                ETLLogger,
                TimeoutManager,
                DataTypeMapper,
                QueryBuilder
            )
            self.assertTrue(True, "Importações utils OK")
        except ImportError as e:
            self.fail(f"Falha na importação utils: {e}")
    
    def test_syonet_system_imports(self):
        """Testa importações específicas do Syonet"""
        try:
            from etl_framework.systems.syonet.config import create_syonet_system_config
            self.assertTrue(True, "Importações Syonet OK")
        except ImportError as e:
            self.fail(f"Falha na importação Syonet: {e}")
    
    def test_strategies_imports(self):
        """Testa importações de estratégias"""
        try:
            from etl_framework.strategies import (
                ETLStrategy,
                FullLoadStrategy,
                SmartIncrementalStrategy
            )
            self.assertTrue(True, "Importações strategies OK")
        except ImportError as e:
            self.fail(f"Falha na importação strategies: {e}")
    
    def test_dag_imports(self):
        """Testa se as DAGs conseguem importar o framework"""
        try:
            # Simula importação das DAGs
            from etl_framework.bronze.processor import create_bronze_processor
            from etl_framework.silver.processor import create_silver_processor
            from etl_framework.config.dw_config import create_corporate_dw_config
            
            # Testa criação de objetos
            dw_config = create_corporate_dw_config()
            self.assertIsNotNone(dw_config)
            
            self.assertTrue(True, "DAGs podem importar framework OK")
        except Exception as e:
            self.fail(f"DAGs não conseguem importar framework: {e}")


class TestFactoryFunctions(unittest.TestCase):
    """
    Testa factory functions e API padronizada
    """
    
    def test_bronze_processor_factory(self):
        """Testa factory do bronze processor"""
        try:
            from etl_framework.bronze.processor import create_bronze_processor
            from etl_framework.config.dw_config import create_corporate_dw_config
            
            dw_config = create_corporate_dw_config()
            
            # Testa criação para Syonet
            processor = create_bronze_processor("syonet", dw_config)
            self.assertIsNotNone(processor)
            self.assertEqual(processor.system_config.name, "syonet")
            
        except Exception as e:
            self.fail(f"Erro na factory bronze: {e}")
    
    def test_silver_processor_factory(self):
        """Testa factory do silver processor"""
        try:
            from etl_framework.silver.processor import create_silver_processor
            from etl_framework.config.dw_config import create_corporate_dw_config
            
            dw_config = create_corporate_dw_config()
            
            # Testa criação para Syonet
            processor = create_silver_processor("syonet", dw_config)
            self.assertIsNotNone(processor)
            self.assertEqual(processor.system_config.name, "syonet")
            
        except Exception as e:
            self.fail(f"Erro na factory silver: {e}")
    
    def test_dw_config_factory(self):
        """Testa factory do DW config"""
        try:
            from etl_framework.config.dw_config import create_corporate_dw_config
            
            dw_config = create_corporate_dw_config()
            self.assertIsNotNone(dw_config)
            self.assertEqual(dw_config.connection.db_type.value, "postgresql")
            
        except Exception as e:
            self.fail(f"Erro na factory DW config: {e}")


class TestCircularDependencies(unittest.TestCase):
    """
    Testa dependências circulares
    """
    
    def test_no_circular_imports(self):
        """Verifica se não há dependências circulares"""
        modules_to_test = [
            'etl_framework.bronze.processor',
            'etl_framework.silver.processor',
            'etl_framework.config.system_config',
            'etl_framework.config.dw_config',
            'etl_framework.systems.syonet.config'
        ]
        
        for module_name in modules_to_test:
            try:
                # Remove do cache se já foi importado
                if module_name in sys.modules:
                    del sys.modules[module_name]
                
                # Tenta importar
                importlib.import_module(module_name)
                
            except ImportError as e:
                if "circular import" in str(e).lower():
                    self.fail(f"Dependência circular detectada em {module_name}: {e}")
                else:
                    # Outros erros de import são tratados em outros testes
                    pass


def run_import_tests() -> Tuple[bool, List[str]]:
    """
    Executa todos os testes de importação
    
    Returns:
        Tuple[bool, List[str]]: (success, error_messages)
    """
    import io
    from contextlib import redirect_stderr
    
    # Captura stderr para pegar erros detalhados
    error_stream = io.StringIO()
    
    with redirect_stderr(error_stream):
        # Cria suite de testes
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # Adiciona classes de teste
        suite.addTests(loader.loadTestsFromTestCase(TestImports))
        suite.addTests(loader.loadTestsFromTestCase(TestFactoryFunctions))
        suite.addTests(loader.loadTestsFromTestCase(TestCircularDependencies))
        
        # Executa testes
        runner = unittest.TextTestRunner(stream=io.StringIO(), verbosity=2)
        result = runner.run(suite)
    
    # Coleta erros
    errors = []
    for failure in result.failures:
        errors.append(f"FAILURE: {failure[0]} - {failure[1]}")
    
    for error in result.errors:
        errors.append(f"ERROR: {error[0]} - {error[1]}")
    
    # Adiciona erros do stderr se houver
    stderr_content = error_stream.getvalue()
    if stderr_content:
        errors.append(f"STDERR: {stderr_content}")
    
    success = result.wasSuccessful()
    
    return success, errors


if __name__ == "__main__":
    print("🧪 Executando testes de importação do ETL Framework...")
    
    success, errors = run_import_tests()
    
    if success:
        print("✅ Todos os testes de importação passaram!")
    else:
        print("❌ Alguns testes falharam:")
        for error in errors:
            print(f"   {error}")
    
    # Executa testes normalmente também
    unittest.main()
