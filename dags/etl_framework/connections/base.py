"""
Classe Base para Conexões de Banco de Dados - ETL Framework (SIMPLIFICADA)

Classe abstrata que define a interface comum para todas as conexões de banco.
Implementa reutilização de conexão durante a task sem pooling complexo.
"""

from abc import ABC, abstractmethod
from contextlib import contextmanager
from typing import Dict, Any, Optional, Tuple
import logging
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """Configuração de conexão com banco de dados"""
    host: str
    port: int
    database: str
    user: str
    password: str
    timeout: int = 300
    extra_params: Optional[Dict[str, Any]] = None


class DatabaseConnection(ABC):
    """
    Classe abstrata para conexões de banco de dados (SIMPLIFICADA)

    Implementa padrões simples:
    - Context manager para gerenciamento automático de conexões
    - Reutilização de conexão durante a task
    - Configurações de timeout
    - Logging estruturado
    """

    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self._connection = None
        self._connection_active = False
        
    @abstractmethod
    def _create_connection_string(self) -> str:
        """Cria string de conexão específica do SGBD"""
        pass

    @abstractmethod
    def _create_connection(self):
        """Cria conexão direta específica do SGBD"""
        pass

    @abstractmethod
    def _get_cursor(self, connection):
        """Retorna cursor específico do SGBD"""
        pass

    @abstractmethod
    def _execute_query(self, cursor, query: str, params: Optional[Tuple] = None):
        """Executa query específica do SGBD"""
        pass

    @abstractmethod
    def _get_table_exists_query(self, schema: str, table: str) -> str:
        """Retorna query para verificar se tabela existe"""
        pass

    @abstractmethod
    def _get_count_query(self, schema: str, table: str) -> str:
        """Retorna query para contar registros"""
        pass

    def _ensure_connection(self):
        """Garante que existe uma conexão ativa"""
        if self._connection is None or not self._connection_active:
            try:
                if self._connection:
                    self._connection.close()
            except:
                pass

            self._connection = self._create_connection()
            self._connection_active = True
            self.logger.info(f"Conexão criada para {self.config.host}")

    @contextmanager
    def get_connection(self):
        """Context manager para obter conexão reutilizada"""
        self._ensure_connection()

        try:
            self.logger.debug(f"Reutilizando conexão ativa")
            yield self._connection
        except Exception as e:
            if self._connection:
                try:
                    self._connection.rollback()
                except:
                    pass
            self.logger.error(f"Erro na conexão: {str(e)}")
            raise
                
    @contextmanager
    def get_cursor(self):
        """Context manager para obter cursor"""
        with self.get_connection() as connection:
            cursor = None
            try:
                cursor = self._get_cursor(connection)
                yield cursor, connection
            except Exception as e:
                if connection:
                    try:
                        connection.rollback()
                    except:
                        pass
                raise
            finally:
                if cursor:
                    try:
                        cursor.close()
                    except:
                        pass
                    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> Any:
        """Executa query e retorna resultado"""
        with self.get_cursor() as (cursor, connection):
            result = self._execute_query(cursor, query, params)
            connection.commit()
            return result
            
    def table_exists(self, schema: str, table: str) -> bool:
        """Verifica se tabela existe"""
        query = self._get_table_exists_query(schema, table)
        with self.get_cursor() as (cursor, connection):
            cursor.execute(query)
            result = cursor.fetchone()
            return bool(result[0]) if result else False
            
    def get_table_count(self, schema: str, table: str) -> int:
        """Retorna contagem de registros da tabela"""
        query = self._get_count_query(schema, table)
        with self.get_cursor() as (cursor, connection):
            cursor.execute(query)
            result = cursor.fetchone()
            return int(result[0]) if result else 0
            
    def close(self):
        """Fecha conexão ativa"""
        if self._connection:
            try:
                self._connection.close()
                self.logger.info("Conexão fechada")
            except Exception as e:
                self.logger.warning(f"Erro ao fechar conexão: {str(e)}")
            finally:
                self._connection = None
                self._connection_active = False

    # Método para compatibilidade com código existente
    def close_pool(self):
        """Alias para close() - compatibilidade"""
        self.close()

    def test_connection(self) -> bool:
        """
        Testa a conexão com o banco de dados

        Returns:
            bool: True se conexão bem-sucedida, False caso contrário
        """
        try:
            with self.get_cursor() as (cursor, connection):
                # Query simples para testar conexão
                cursor.execute("SELECT 1")
                result = cursor.fetchone()

                if result:
                    self.logger.info(f"✅ Conexão testada com sucesso para {self.config.host}:{self.config.port}")
                    return True
                else:
                    self.logger.error(f"❌ Falha no teste de conexão para {self.config.host}:{self.config.port}")
                    return False

        except Exception as e:
            self.logger.error(f"❌ Erro ao testar conexão para {self.config.host}:{self.config.port}: {str(e)}")
            return False
