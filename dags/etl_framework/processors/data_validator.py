"""
Validador de Dados - ETL Framework

Implementa validação de dados baseada na lógica das DAGs V3.
"""

import logging
import time
from typing import Dict, Any, Tuple, Optional
from ..strategies.base import TableMetadata


class DataValidator:
    """
    Validador de dados ETL
    
    Implementa:
    - Validação de contagens entre origem e destino
    - Análise de divergências
    - Cálculo de percentuais de diferença
    - Logs estruturados de validação
    """
    
    def __init__(self, source_connection, target_connection, max_abs_diff: int = 10, 
                 enable_validation: bool = True, cache_ttl: int = 300):
        self.source_connection = source_connection
        self.target_connection = target_connection
        self.max_abs_diff = max_abs_diff
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 🚀 OTIMIZAÇÃO: Validação opcional e cache
        self.enable_validation = enable_validation
        self.cache_ttl = cache_ttl
        self._count_cache = {}  # Cache para contagens
        self._validation_cache = {}  # Cache para validações completas
    
    def validate_table(self, table_metadata: TableMetadata, task_name: str = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Valida uma tabela após processamento ETL
        Baseado na função validate_single_table das DAGs V3
        
        Returns:
            Tuple[bool, Dict]: (is_valid, analysis_info)
        """
        # 🚀 OTIMIZAÇÃO: Skip validação se desabilitada
        if not self.enable_validation:
            return True, {
                'should_use_seven_days': False,
                'source_count': 0,
                'dest_count': 0,
                'abs_diff': 0,
                'diff_percentage': 0,
                'validation_skipped': True
            }
        
        # 🚀 OTIMIZAÇÃO: Check cache primeiro
        cache_key = f"{table_metadata.name}_{task_name or 'default'}"
        cached_result = self._get_cached_validation(cache_key)
        if cached_result:
            return cached_result
        
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            task_label = task_name or table_metadata.name
            
            # 🚀 OTIMIZAÇÃO: Conta registros com cache
            dest_count = self._get_cached_count(
                "dest", table_metadata.target_schema, 
                f"{table_metadata.table_prefix}{table_metadata.name}",
                lambda: self.target_connection.get_table_count(
                    table_metadata.target_schema,
                    f"{table_metadata.table_prefix}{table_metadata.name}"
                )
            )
            
            source_count = self._get_cached_source_count(table_metadata)
            
            # Calcula diferenças
            abs_diff = abs(dest_count - source_count)
            diff_percentage = (abs_diff / source_count * 100) if source_count > 0 else 0
            
            # Análise para estratégias inteligentes
            should_use_seven_days = (
                source_count > 0 and  
                dest_count < source_count and  
                diff_percentage <= 10  # MAX_GAP_PERCENTAGE padrão
            )
            
            analysis_info = {
                'should_use_seven_days': should_use_seven_days,
                'source_count': source_count,
                'dest_count': dest_count,
                'abs_diff': abs_diff,
                'diff_percentage': diff_percentage
            }
            
            self.logger.info(f"🔍 Validação {task_label}: origem={source_count:,}, destino={dest_count:,}")
            
            # Verifica tolerâncias
            if abs_diff > self.max_abs_diff:
                self.logger.error(f"❌ Divergência em {table_metadata.name}: origem={source_count:,}, destino={dest_count:,}, diff={abs_diff:,} ({diff_percentage:.1f}%)")
                self.logger.info(f"📊 Recomendação para fallback: {'Incremental 7 Dias' if should_use_seven_days else 'Full Load'}")
                return False, analysis_info
            
            self.logger.info(f"✅ {task_label} validado com sucesso")
            
            # 🚀 OTIMIZAÇÃO: Cache resultado
            result = (True, analysis_info)
            self._cache_validation(cache_key, result)
            return result
                    
        except Exception as e:
            error_msg = f"Erro na validação de {table_metadata.name}: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            return False, {
                'should_use_seven_days': False,
                'source_count': 0,
                'dest_count': 0,
                'abs_diff': 0,
                'diff_percentage': 0,
                'error': error_msg
            }
    
    def _get_source_count(self, table_metadata: TableMetadata) -> int:
        """Conta registros na origem com tratamento especial para casos customizados"""
        try:
            source_table = table_metadata.source_table or table_metadata.name
            
            # Tratamento especial para syo_evento_obs (baseado nas DAGs V3)
            if table_metadata.name == 'syo_evento_obs':
                if hasattr(self.source_connection, 'execute_openquery'):
                    # SQL Server com OPENQUERY
                    with self.source_connection.get_cursor() as (cursor, conn):
                        cursor.execute(f"SELECT cnt FROM OPENQUERY(POSTGRES, 'SELECT COUNT(*) AS cnt FROM PUBLIC.syo_evento WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')")
                        result = cursor.fetchone()
                        return result[0] if result else 0
                else:
                    # Conexão direta
                    with self.source_connection.get_cursor() as (cursor, conn):
                        cursor.execute(f"SELECT COUNT(*) FROM {table_metadata.schema}.syo_evento WHERE ds_observacao LIKE '%Cadencia Meetime:%'")
                        result = cursor.fetchone()
                        return result[0] if result else 0
            else:
                # Contagem padrão
                return self.source_connection.get_table_count(
                    table_metadata.schema,
                    source_table
                )
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao contar registros na origem: {str(e)}")
            return 0
    
    def validate_data_quality(self, table_metadata: TableMetadata) -> Dict[str, Any]:
        """
        Valida qualidade dos dados (nulls, duplicatas, etc.)
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                # Contagem total
                cursor.execute(f"SELECT COUNT(*) FROM {full_table_name}")
                total_count = cursor.fetchone()[0]
                
                quality_info = {
                    'total_records': total_count,
                    'null_analysis': {},
                    'duplicate_analysis': {}
                }
                
                # Análise de NULLs por coluna
                cursor.execute(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_schema = '{table_metadata.target_schema}' 
                    AND table_name = '{table_metadata.table_prefix}{table_metadata.name}'
                """)
                columns = [row[0] for row in cursor.fetchall()]
                
                for column in columns[:10]:  # Limita a 10 colunas para performance
                    cursor.execute(f'SELECT COUNT(*) FROM {full_table_name} WHERE "{column}" IS NULL')
                    null_count = cursor.fetchone()[0]
                    null_percentage = (null_count / total_count * 100) if total_count > 0 else 0
                    
                    quality_info['null_analysis'][column] = {
                        'null_count': null_count,
                        'null_percentage': null_percentage
                    }
                
                # Análise de duplicatas (se tem campo ID)
                if table_metadata.id_field:
                    id_fields = table_metadata.id_field.split(',')
                    id_fields_clean = [f'"{field.strip()}"' for field in id_fields]
                    
                    cursor.execute(f"""
                        SELECT COUNT(*) - COUNT(DISTINCT {','.join(id_fields_clean)}) 
                        FROM {full_table_name}
                    """)
                    duplicate_count = cursor.fetchone()[0]
                    
                    quality_info['duplicate_analysis'] = {
                        'duplicate_count': duplicate_count,
                        'duplicate_percentage': (duplicate_count / total_count * 100) if total_count > 0 else 0
                    }
                
                self.logger.info(f"📊 Análise de qualidade para {table_metadata.name}: {total_count} registros")
                return quality_info
                
        except Exception as e:
            self.logger.error(f"❌ Erro na análise de qualidade: {str(e)}")
            return {
                'total_records': 0,
                'null_analysis': {},
                'duplicate_analysis': {},
                'error': str(e)
            }
    
    def compare_schemas(self, table_metadata: TableMetadata) -> Dict[str, Any]:
        """
        Compara schemas entre origem e destino
        """
        try:
            # Implementação básica - pode ser expandida
            dest_columns = self._get_table_columns(table_metadata, is_target=True)
            source_columns = self._get_table_columns(table_metadata, is_target=False)
            
            schema_comparison = {
                'dest_columns': len(dest_columns),
                'source_columns': len(source_columns),
                'columns_match': len(dest_columns) == len(source_columns),
                'missing_in_dest': [],
                'missing_in_source': []
            }
            
            dest_col_names = {col[0] for col in dest_columns}
            source_col_names = {col[0] for col in source_columns}
            
            schema_comparison['missing_in_dest'] = list(source_col_names - dest_col_names)
            schema_comparison['missing_in_source'] = list(dest_col_names - source_col_names)
            
            return schema_comparison
            
        except Exception as e:
            self.logger.error(f"❌ Erro na comparação de schemas: {str(e)}")
            return {'error': str(e)}
    
    def _get_table_columns(self, table_metadata: TableMetadata, is_target: bool = True) -> list:
        """Obtém colunas da tabela"""
        try:
            if is_target:
                connection = self.target_connection
                schema = table_metadata.target_schema
                table_name = f"{table_metadata.table_prefix}{table_metadata.name}"
            else:
                connection = self.source_connection
                schema = table_metadata.schema
                table_name = table_metadata.source_table or table_metadata.name
            
            with connection.get_cursor() as (cursor, conn):
                cursor.execute(f"""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_schema = '{schema}' 
                    AND table_name = '{table_name}'
                    ORDER BY ordinal_position
                """)
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao obter colunas: {str(e)}")
            return []
    
    # 🚀 OTIMIZAÇÃO: Métodos de cache
    def _get_cached_count(self, connection_type: str, schema: str, table: str, count_func) -> int:
        """Obtém contagem com cache"""
        cache_key = f"{connection_type}_{schema}_{table}"
        current_time = time.time()
        
        # Check cache
        if cache_key in self._count_cache:
            cached_data = self._count_cache[cache_key]
            if current_time - cached_data['timestamp'] < self.cache_ttl:
                return cached_data['count']
        
        # Cache miss ou expired - executa função
        count = count_func()
        self._count_cache[cache_key] = {
            'count': count,
            'timestamp': current_time
        }
        return count
    
    def _get_cached_source_count(self, table_metadata: TableMetadata) -> int:
        """Versão cached do _get_source_count"""
        cache_key = f"source_{table_metadata.schema}_{table_metadata.name}"
        current_time = time.time()
        
        # Check cache
        if cache_key in self._count_cache:
            cached_data = self._count_cache[cache_key]
            if current_time - cached_data['timestamp'] < self.cache_ttl:
                return cached_data['count']
        
        # Cache miss - executa contagem original
        count = self._get_source_count(table_metadata)
        self._count_cache[cache_key] = {
            'count': count,
            'timestamp': current_time
        }
        return count
    
    def _get_cached_validation(self, cache_key: str) -> Optional[Tuple[bool, Dict[str, Any]]]:
        """Obtém validação do cache"""
        current_time = time.time()
        
        if cache_key in self._validation_cache:
            cached_data = self._validation_cache[cache_key]
            if current_time - cached_data['timestamp'] < self.cache_ttl:
                return cached_data['result']
        
        return None
    
    def _cache_validation(self, cache_key: str, result: Tuple[bool, Dict[str, Any]]):
        """Armazena validação no cache"""
        self._validation_cache[cache_key] = {
            'result': result,
            'timestamp': time.time()
        }
    
    def clear_cache(self):
        """Limpa todos os caches"""
        self._count_cache.clear()
        self._validation_cache.clear()
