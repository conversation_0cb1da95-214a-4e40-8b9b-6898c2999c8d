"""
Gerenciador de Timeout - ETL Framework

Implementa controle de timeout baseado na lógica das DAGs V3.
"""

import threading
import time
import logging
from typing import Callable, Any, Optional


class TimeoutManager:
    """
    Gerenciador de timeout para operações ETL
    
    Implementa:
    - Timeout com threading
    - Execução assíncrona
    - Logs de timeout
    - Cleanup automático
    """
    
    def __init__(self, timeout_seconds: int = 300):
        self.timeout_seconds = timeout_seconds
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute_with_timeout(self, func: Callable, *args, **kwargs) -> Any:
        """
        Executa função com timeout usando threading
        Baseado na lógica execute_transfer_with_timeout das DAGs V3
        """
        result = {'success': False, 'error': None, 'return_value': None}
        start_time = time.time()
        
        self.logger.info(f"⏱️ Iniciando execução com timeout de {self.timeout_seconds}s")
        
        def execute_function():
            try:
                return_value = func(*args, **kwargs)
                result['success'] = True
                result['return_value'] = return_value
            except Exception as e:
                result['error'] = str(e)
                self.logger.error(f"❌ Erro na execução: {str(e)}")
        
        # Inicia thread da execução
        thread = threading.Thread(target=execute_function)
        thread.daemon = True
        thread.start()
        
        # Aguarda conclusão ou timeout
        thread.join(self.timeout_seconds)
        
        elapsed_time = time.time() - start_time
        
        if thread.is_alive():
            # Timeout ocorreu
            self.logger.warning(f"⏱️ TIMEOUT: Execução excedeu {self.timeout_seconds}s (executado por {elapsed_time:.1f}s)")
            raise TimeoutError(f"Execução excedeu timeout de {self.timeout_seconds}s")
        
        if not result['success']:
            error_msg = result['error'] or "Erro desconhecido na execução"
            self.logger.error(f"❌ Erro na execução após {elapsed_time:.1f}s: {error_msg}")
            raise Exception(error_msg)
        
        self.logger.info(f"✅ Execução concluída em {elapsed_time:.1f}s (limite: {self.timeout_seconds}s)")
        return result['return_value']
    
    def execute_with_retry_and_timeout(self, func: Callable, max_retries: int = 3, 
                                     retry_delay: int = 60, *args, **kwargs) -> Any:
        """
        Executa função com timeout e retry
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"🔄 Tentativa {attempt + 1}/{max_retries + 1}")
                    time.sleep(retry_delay)
                
                return self.execute_with_timeout(func, *args, **kwargs)
                
            except TimeoutError as e:
                last_exception = e
                self.logger.warning(f"⏱️ Timeout na tentativa {attempt + 1}: {str(e)}")
                
            except Exception as e:
                last_exception = e
                self.logger.error(f"❌ Erro na tentativa {attempt + 1}: {str(e)}")
        
        # Todas as tentativas falharam
        self.logger.error(f"❌ Todas as {max_retries + 1} tentativas falharam")
        raise last_exception


class OperationTimer:
    """
    Timer simples para operações
    """
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def start(self):
        """Inicia timer"""
        self.start_time = time.time()
        self.logger.info(f"⏱️ Iniciando timer para {self.operation_name}")
    
    def stop(self) -> float:
        """Para timer e retorna tempo decorrido"""
        if not self.start_time:
            self.logger.warning(f"Timer não foi iniciado para {self.operation_name}")
            return 0.0
        
        self.end_time = time.time()
        elapsed_time = self.end_time - self.start_time
        
        self.logger.info(f"⏱️ {self.operation_name} executado em {elapsed_time:.1f}s")
        return elapsed_time
    
    def get_elapsed_time(self) -> float:
        """Retorna tempo decorrido sem parar o timer"""
        if not self.start_time:
            return 0.0
        
        current_time = time.time()
        return current_time - self.start_time
    
    def __enter__(self):
        """Context manager entry"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        elapsed_time = self.stop()
        
        if exc_type:
            self.logger.error(f"❌ {self.operation_name} falhou após {elapsed_time:.1f}s: {exc_val}")
        else:
            self.logger.info(f"✅ {self.operation_name} concluído em {elapsed_time:.1f}s")


class BatchTimer:
    """
    Timer para operações em lote
    """
    
    def __init__(self, batch_name: str):
        self.batch_name = batch_name
        self.operation_times = {}
        self.batch_start_time = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def start_batch(self):
        """Inicia timer do lote"""
        self.batch_start_time = time.time()
        self.logger.info(f"🚀 Iniciando lote: {self.batch_name}")
    
    def start_operation(self, operation_name: str):
        """Inicia timer de operação específica"""
        self.operation_times[operation_name] = {
            'start_time': time.time(),
            'end_time': None,
            'elapsed_time': None
        }
        self.logger.debug(f"⏱️ Iniciando operação: {operation_name}")
    
    def end_operation(self, operation_name: str) -> float:
        """Finaliza timer de operação específica"""
        if operation_name not in self.operation_times:
            self.logger.warning(f"Operação {operation_name} não foi iniciada")
            return 0.0
        
        end_time = time.time()
        start_time = self.operation_times[operation_name]['start_time']
        elapsed_time = end_time - start_time
        
        self.operation_times[operation_name]['end_time'] = end_time
        self.operation_times[operation_name]['elapsed_time'] = elapsed_time
        
        self.logger.debug(f"✅ Operação {operation_name} concluída em {elapsed_time:.1f}s")
        return elapsed_time
    
    def end_batch(self) -> dict:
        """Finaliza lote e retorna estatísticas"""
        if not self.batch_start_time:
            self.logger.warning(f"Lote {self.batch_name} não foi iniciado")
            return {}
        
        batch_end_time = time.time()
        total_batch_time = batch_end_time - self.batch_start_time
        
        # Calcula estatísticas
        completed_operations = [op for op in self.operation_times.values() if op['elapsed_time'] is not None]
        
        stats = {
            'batch_name': self.batch_name,
            'total_batch_time': total_batch_time,
            'total_operations': len(self.operation_times),
            'completed_operations': len(completed_operations),
            'average_operation_time': sum(op['elapsed_time'] for op in completed_operations) / len(completed_operations) if completed_operations else 0,
            'fastest_operation': min(completed_operations, key=lambda x: x['elapsed_time'])['elapsed_time'] if completed_operations else 0,
            'slowest_operation': max(completed_operations, key=lambda x: x['elapsed_time'])['elapsed_time'] if completed_operations else 0,
            'operation_details': self.operation_times
        }
        
        self.logger.info(f"📊 Lote {self.batch_name} concluído em {total_batch_time:.1f}s")
        self.logger.info(f"📊 Operações: {stats['completed_operations']}/{stats['total_operations']} concluídas")
        self.logger.info(f"📊 Tempo médio por operação: {stats['average_operation_time']:.1f}s")
        
        return stats
