"""
Mapeador de Tipos de Dados - ETL Framework

Mapeia tipos de dados entre diferentes SGBDs baseado na lógica das DAGs V3.
"""

import pandas as pd
from typing import Dict, Any, Optional
from enum import Enum


class DatabaseType(Enum):
    """Tipos de banco de dados suportados"""
    POSTGRESQL = "postgresql"
    SQLSERVER = "sqlserver"
    ORACLE = "oracle"
    MYSQL = "mysql"


class DataTypeMapper:
    """
    Mapeador de tipos de dados entre SGBDs
    
    Implementa:
    - Mapeamento pandas -> PostgreSQL (baseado nas DAGs V3)
    - Mapeamento entre diferentes SGBDs
    - Conversões automáticas
    - Tipos customizados
    """
    
    def __init__(self):
        # Mapeamento pandas para PostgreSQL (baseado nas DAGs V3)
        self.pandas_to_postgresql = {
            'object': 'TEXT',
            'int64': 'BIGINT',
            'int32': 'INTEGER',
            'int16': 'SMALLINT',
            'int8': 'SMALLINT',
            'float64': 'DOUBLE PRECISION',
            'float32': 'REAL',
            'bool': 'BOOLEAN',
            'datetime64[ns]': 'TIMESTAMP',
            'datetime64[ns, UTC]': 'TIMESTAMP WITH TIME ZONE',
            'timedelta64[ns]': 'INTERVAL',
            'category': 'TEXT'
        }
        
        # Mapeamento pandas para SQL Server
        self.pandas_to_sqlserver = {
            'object': 'NVARCHAR(MAX)',
            'int64': 'BIGINT',
            'int32': 'INT',
            'int16': 'SMALLINT',
            'int8': 'TINYINT',
            'float64': 'FLOAT',
            'float32': 'REAL',
            'bool': 'BIT',
            'datetime64[ns]': 'DATETIME2',
            'datetime64[ns, UTC]': 'DATETIMEOFFSET',
            'timedelta64[ns]': 'TIME',
            'category': 'NVARCHAR(255)'
        }
        
        # Mapeamento pandas para Oracle
        self.pandas_to_oracle = {
            'object': 'CLOB',
            'int64': 'NUMBER(19)',
            'int32': 'NUMBER(10)',
            'int16': 'NUMBER(5)',
            'int8': 'NUMBER(3)',
            'float64': 'BINARY_DOUBLE',
            'float32': 'BINARY_FLOAT',
            'bool': 'NUMBER(1)',
            'datetime64[ns]': 'TIMESTAMP',
            'datetime64[ns, UTC]': 'TIMESTAMP WITH TIME ZONE',
            'timedelta64[ns]': 'INTERVAL DAY TO SECOND',
            'category': 'VARCHAR2(255)'
        }
        
        # Mapeamento pandas para MySQL
        self.pandas_to_mysql = {
            'object': 'LONGTEXT',
            'int64': 'BIGINT',
            'int32': 'INT',
            'int16': 'SMALLINT',
            'int8': 'TINYINT',
            'float64': 'DOUBLE',
            'float32': 'FLOAT',
            'bool': 'BOOLEAN',
            'datetime64[ns]': 'DATETIME',
            'datetime64[ns, UTC]': 'TIMESTAMP',
            'timedelta64[ns]': 'TIME',
            'category': 'VARCHAR(255)'
        }
    
    def get_mapping_for_database(self, db_type: DatabaseType) -> Dict[str, str]:
        """Retorna mapeamento para tipo de banco específico"""
        if db_type == DatabaseType.POSTGRESQL:
            return self.pandas_to_postgresql
        elif db_type == DatabaseType.SQLSERVER:
            return self.pandas_to_sqlserver
        elif db_type == DatabaseType.ORACLE:
            return self.pandas_to_oracle
        elif db_type == DatabaseType.MYSQL:
            return self.pandas_to_mysql
        else:
            raise ValueError(f"Tipo de banco não suportado: {db_type}")
    
    def map_pandas_type(self, pandas_type: str, target_db: DatabaseType) -> str:
        """Mapeia tipo pandas para tipo do banco de destino"""
        mapping = self.get_mapping_for_database(target_db)
        return mapping.get(str(pandas_type), 'TEXT')  # Default para TEXT se não encontrar
    
    def map_dataframe_types(self, df: pd.DataFrame, target_db: DatabaseType) -> Dict[str, str]:
        """Mapeia todos os tipos de um DataFrame"""
        column_types = {}
        mapping = self.get_mapping_for_database(target_db)
        
        for col_name, dtype in df.dtypes.items():
            # Sanitiza nome da coluna
            clean_col_name = col_name.replace(' ', '_').replace('/', '_').lower()
            
            # Mapeia tipo
            pg_type = mapping.get(str(dtype), 'TEXT')
            column_types[clean_col_name] = pg_type
        
        return column_types
    
    def generate_create_table_sql(self, table_name: str, df: pd.DataFrame, 
                                target_db: DatabaseType, schema: str = None) -> str:
        """
        Gera SQL CREATE TABLE baseado no DataFrame
        Baseado na função create_table_schema_postgres das DAGs V3
        """
        column_types = self.map_dataframe_types(df, target_db)
        
        # Constrói colunas
        columns = []
        for col_name, col_type in column_types.items():
            columns.append(f'"{col_name}" {col_type}')
        
        # Nome completo da tabela
        full_table_name = f"{schema}.{table_name}" if schema else table_name
        
        return f"CREATE TABLE {full_table_name} ({', '.join(columns)})"
    
    def get_optimal_type_for_column(self, series: pd.Series, target_db: DatabaseType) -> str:
        """
        Determina tipo ótimo para uma coluna baseado nos dados
        """
        # Análise básica dos dados
        if series.dtype == 'object':
            # Para strings, analisa tamanho máximo
            max_length = series.astype(str).str.len().max() if not series.empty else 0
            
            if target_db == DatabaseType.POSTGRESQL:
                if max_length <= 255:
                    return 'VARCHAR(255)'
                elif max_length <= 1000:
                    return 'VARCHAR(1000)'
                else:
                    return 'TEXT'
            elif target_db == DatabaseType.SQLSERVER:
                if max_length <= 255:
                    return 'NVARCHAR(255)'
                elif max_length <= 4000:
                    return 'NVARCHAR(4000)'
                else:
                    return 'NVARCHAR(MAX)'
            elif target_db == DatabaseType.ORACLE:
                if max_length <= 4000:
                    return f'VARCHAR2({min(max_length + 50, 4000)})'
                else:
                    return 'CLOB'
            elif target_db == DatabaseType.MYSQL:
                if max_length <= 255:
                    return 'VARCHAR(255)'
                elif max_length <= 65535:
                    return 'TEXT'
                else:
                    return 'LONGTEXT'
        
        # Para outros tipos, usa mapeamento padrão
        return self.map_pandas_type(str(series.dtype), target_db)
    
    def suggest_column_optimizations(self, df: pd.DataFrame, target_db: DatabaseType) -> Dict[str, Dict[str, Any]]:
        """
        Sugere otimizações para colunas baseado na análise dos dados
        """
        suggestions = {}
        
        for col_name in df.columns:
            series = df[col_name]
            clean_col_name = col_name.replace(' ', '_').replace('/', '_').lower()
            
            suggestion = {
                'original_type': str(series.dtype),
                'suggested_type': self.get_optimal_type_for_column(series, target_db),
                'null_count': series.isnull().sum(),
                'null_percentage': (series.isnull().sum() / len(series) * 100) if len(series) > 0 else 0,
                'unique_count': series.nunique(),
                'unique_percentage': (series.nunique() / len(series) * 100) if len(series) > 0 else 0
            }
            
            # Análises específicas por tipo
            if series.dtype == 'object':
                suggestion['max_length'] = series.astype(str).str.len().max() if not series.empty else 0
                suggestion['avg_length'] = series.astype(str).str.len().mean() if not series.empty else 0
            elif pd.api.types.is_numeric_dtype(series):
                suggestion['min_value'] = series.min() if not series.empty else None
                suggestion['max_value'] = series.max() if not series.empty else None
                suggestion['mean_value'] = series.mean() if not series.empty else None
            
            suggestions[clean_col_name] = suggestion
        
        return suggestions
    
    def convert_legacy_config_types(self, legacy_mapping: Dict[str, str], target_db: DatabaseType) -> Dict[str, str]:
        """
        Converte mapeamentos legados para novo formato
        """
        converted = {}
        
        for old_type, new_type in legacy_mapping.items():
            # Se já é um tipo válido do banco de destino, mantém
            if self._is_valid_db_type(new_type, target_db):
                converted[old_type] = new_type
            else:
                # Tenta mapear para tipo equivalente
                converted[old_type] = self._find_equivalent_type(new_type, target_db)
        
        return converted
    
    def _is_valid_db_type(self, type_name: str, target_db: DatabaseType) -> bool:
        """Verifica se tipo é válido para o banco de destino"""
        valid_types = {
            DatabaseType.POSTGRESQL: ['TEXT', 'VARCHAR', 'INTEGER', 'BIGINT', 'SMALLINT', 'BOOLEAN', 'TIMESTAMP', 'DOUBLE PRECISION', 'REAL'],
            DatabaseType.SQLSERVER: ['NVARCHAR', 'INT', 'BIGINT', 'SMALLINT', 'BIT', 'DATETIME2', 'FLOAT', 'REAL'],
            DatabaseType.ORACLE: ['VARCHAR2', 'CLOB', 'NUMBER', 'TIMESTAMP', 'BINARY_DOUBLE', 'BINARY_FLOAT'],
            DatabaseType.MYSQL: ['VARCHAR', 'TEXT', 'INT', 'BIGINT', 'SMALLINT', 'BOOLEAN', 'DATETIME', 'DOUBLE', 'FLOAT']
        }
        
        db_types = valid_types.get(target_db, [])
        return any(valid_type in type_name.upper() for valid_type in db_types)
    
    def _find_equivalent_type(self, type_name: str, target_db: DatabaseType) -> str:
        """Encontra tipo equivalente no banco de destino"""
        # Mapeamento básico de tipos comuns
        common_mappings = {
            'STRING': 'TEXT',
            'VARCHAR': 'TEXT',
            'INT': 'INTEGER',
            'FLOAT': 'DOUBLE PRECISION',
            'DATE': 'TIMESTAMP',
            'BOOL': 'BOOLEAN'
        }
        
        type_upper = type_name.upper()
        for common_type, mapped_type in common_mappings.items():
            if common_type in type_upper:
                return self.map_pandas_type('object' if mapped_type == 'TEXT' else 'int64', target_db)
        
        # Default para TEXT
        return self.map_pandas_type('object', target_db)
