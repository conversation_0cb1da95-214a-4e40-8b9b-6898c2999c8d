#!/usr/bin/env python3
"""
Teste Real do ETL Framework V4

Executa o framework exatamente como seria executado no Airflow,
incluindo conexões reais com bancos de dados.

Uso:
    python3 test_framework_real.py --system syonet --table syo_agenda
    python3 test_framework_real.py --system syonet --mode bronze --tables syo_agenda syo_usuario
    python3 test_framework_real.py --system syonet --mode silver
"""

import sys
import os
import logging
from datetime import datetime
from typing import List, Optional

# Configura path para imports (igual ao Airflow)
dag_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, dag_path)

def test_real_bronze_processing(system_name: str, table_names: List[str]):
    """Testa processamento bronze real (igual ao Airflow)"""
    
    print(f"\n🥉 === TESTE REAL BRONZE - {system_name.upper()} ===")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📋 Tabelas: {', '.join(table_names)}")
    print("=" * 60)
    
    try:
        # 1. Imports reais (igual ao v4_bronze.py)
        print("📦 1. Carregando módulos do framework...")

        from etl_framework.config.dw_config import create_corporate_dw_config
        from etl_framework.bronze.processor import create_bronze_processor

        print("  ✅ Imports concluídos")

        # 2. Configuração (igual ao v4_bronze.py)
        print("\n⚙️ 2. Carregando configurações...")

        dw_config = create_corporate_dw_config()

        print(f"  ✅ DW Config: {dw_config.connection.host}:{dw_config.connection.port}")

        # 3. Criação do processador (igual ao v4_bronze.py)
        print("\n🔧 3. Inicializando processador...")

        # ✅ Correção: Usar factory function que cria tudo corretamente
        processor = create_bronze_processor(system_name, dw_config)

        print(f"  ✅ BronzeProcessor criado: {processor.system_config.name}")
        print(f"  ✅ System Config: {processor.system_config.name} ({len(processor.system_config.get_bronze_tables())} tabelas)")
        
        # 4. Processamento real de cada tabela (igual às tasks do Airflow)
        print(f"\n🚀 4. Processando tabelas ({len(table_names)} tabelas)...")
        
        results = {}
        
        for table_name in table_names:
            print(f"\n  🔄 Processando {table_name}...")

            try:
                # Obtém configuração da tabela (igual ao v4_bronze.py)
                table_config = processor.system_config.get_bronze_table(table_name)
                if not table_config:
                    raise Exception(f"Tabela {table_name} não encontrada na configuração do {system_name}")

                # Executa exatamente como a função process_table() do v4_bronze.py
                result = processor.process_single_table(table_config)
                
                if result.get('success', False):
                    records = result.get('records_processed', 0)
                    time_taken = result.get('execution_time', 0)
                    strategy = result.get('strategy_used', 'unknown')

                    print(f"    ✅ {table_name}: {records} registros em {time_taken:.1f}s ({strategy})")
                    results[table_name] = {'success': True, 'records': records, 'time': time_taken}
                else:
                    error = result.get('error', 'Erro desconhecido')
                    print(f"    ❌ {table_name}: {error}")
                    results[table_name] = {'success': False, 'error': error}
                    
            except Exception as e:
                error_msg = str(e)
                print(f"    ❌ {table_name}: EXCEPTION - {error_msg}")
                results[table_name] = {'success': False, 'error': f"Exception: {error_msg}"}
        
        # 5. Cleanup (igual ao v4_bronze.py)
        print(f"\n🧹 5. Limpando recursos...")
        try:
            processor.cleanup()
            print("  ✅ Recursos limpos")
        except Exception as e:
            print(f"  ⚠️ Erro na limpeza: {str(e)}")
        
        # 6. Relatório final
        print(f"\n📊 6. Relatório Final:")
        
        successful = [k for k, v in results.items() if v.get('success', False)]
        failed = [k for k, v in results.items() if not v.get('success', False)]
        
        print(f"  ✅ Sucessos: {len(successful)}")
        print(f"  ❌ Falhas: {len(failed)}")
        
        if successful:
            total_records = sum(v.get('records', 0) for v in results.values() if v.get('success'))
            total_time = sum(v.get('time', 0) for v in results.values() if v.get('success'))
            print(f"  📊 Total processado: {total_records} registros em {total_time:.1f}s")
        
        if failed:
            print(f"\n⚠️ Tabelas com falha:")
            for table in failed:
                error = results[table].get('error', 'Erro desconhecido')
                print(f"    - {table}: {error}")
        
        return len(successful) > 0
        
    except Exception as e:
        print(f"\n❌ ERRO CRÍTICO no teste bronze: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_real_silver_processing(system_name: str):
    """Testa processamento silver real (igual ao Airflow)"""
    
    print(f"\n🥈 === TESTE REAL SILVER - {system_name.upper()} ===")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. Imports reais (igual ao v4_silver.py)
        print("📦 1. Carregando módulos do framework...")

        from etl_framework.config.dw_config import create_corporate_dw_config
        from etl_framework.silver.processor import create_silver_processor

        print("  ✅ Imports concluídos")

        # 2. Configuração (igual ao v4_silver.py)
        print("\n⚙️ 2. Carregando configurações...")

        dw_config = create_corporate_dw_config()

        print(f"  ✅ DW Config: {dw_config.connection.host}:{dw_config.connection.port}")

        # 3. Criação do processador (igual ao v4_silver.py)
        print("\n🔧 3. Inicializando processador...")

        # ✅ Correção: Usar factory function que cria tudo corretamente
        processor = create_silver_processor(system_name, dw_config)

        print(f"  ✅ SilverProcessor criado: {processor.system_config.name}")
        print(f"  ✅ System Config: {processor.system_config.name} ({len(processor.system_config.silver_transformations)} transformações)")

        # 4. Processamento real de transformações
        print(f"\n🚀 4. Processando transformações...")

        transformations = processor.system_config.silver_transformations
        results = {}
        
        for transformation in transformations:
            print(f"\n  🔄 Processando {transformation.name}...")
            
            try:
                # Executa exatamente como as tasks do v4_silver.py
                result = processor.process_transformation(transformation.name)
                
                if result.get('success', False):
                    time_taken = result.get('execution_time', 0)
                    records = result.get('records_affected', 0)
                    
                    print(f"    ✅ {transformation.name}: {records} registros em {time_taken:.1f}s")
                    results[transformation.name] = {'success': True, 'records': records, 'time': time_taken}
                else:
                    error = result.get('error', 'Erro desconhecido')
                    print(f"    ❌ {transformation.name}: {error}")
                    results[transformation.name] = {'success': False, 'error': error}
                    
            except Exception as e:
                error_msg = str(e)
                print(f"    ❌ {transformation.name}: EXCEPTION - {error_msg}")
                results[transformation.name] = {'success': False, 'error': f"Exception: {error_msg}"}
        
        # 5. Cleanup
        print(f"\n🧹 5. Limpando recursos...")
        try:
            processor.cleanup()
            print("  ✅ Recursos limpos")
        except Exception as e:
            print(f"  ⚠️ Erro na limpeza: {str(e)}")
        
        # 6. Relatório final
        print(f"\n📊 6. Relatório Final:")
        
        successful = [k for k, v in results.items() if v.get('success', False)]
        failed = [k for k, v in results.items() if not v.get('success', False)]
        
        print(f"  ✅ Sucessos: {len(successful)}")
        print(f"  ❌ Falhas: {len(failed)}")
        
        if failed:
            print(f"\n⚠️ Transformações com falha:")
            for transform in failed:
                error = results[transform].get('error', 'Erro desconhecido')  
                print(f"    - {transform}: {error}")
        
        return len(successful) > 0
        
    except Exception as e:
        print(f"\n❌ ERRO CRÍTICO no teste silver: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_connections_only(system_name: str):
    """Testa apenas as conexões (diagnóstico rápido)"""
    
    print(f"\n🔗 === TESTE DE CONEXÕES - {system_name.upper()} ===")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # Imports
        from etl_framework.config.dw_config import create_corporate_dw_config
        from etl_framework.bronze.processor import create_bronze_processor

        # Configuração
        dw_config = create_corporate_dw_config()
        processor = create_bronze_processor(system_name, dw_config)
        
        print("🔧 Testando conexões...")
        
        # Testa conexão de origem
        print(f"\n  🔄 Testando conexão de origem (SQL Server)...")
        try:
            source_conn = processor.source_connection
            if hasattr(source_conn, 'test_connection'):
                source_ok = source_conn.test_connection()
                print(f"    {'✅' if source_ok else '❌'} SQL Server: {'OK' if source_ok else 'FALHA'}")
            else:
                print(f"    ⚠️ SQL Server: Método test_connection não implementado")
        except Exception as e:
            print(f"    ❌ SQL Server: ERRO - {str(e)}")

        # Testa conexão de destino
        print(f"\n  🔄 Testando conexão de destino (PostgreSQL)...")
        try:
            target_conn = processor.dw_connection
            if hasattr(target_conn, 'test_connection'):
                target_ok = target_conn.test_connection()
                print(f"    {'✅' if target_ok else '❌'} PostgreSQL: {'OK' if target_ok else 'FALHA'}")
            else:
                print(f"    ⚠️ PostgreSQL: Método test_connection não implementado")
        except Exception as e:
            print(f"    ❌ PostgreSQL: ERRO - {str(e)}")
            print(f"    📋 Detalhes do erro: {str(e)}")
        
        processor.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO CRÍTICO no teste de conexões: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Teste real do ETL Framework V4')
    parser.add_argument('--system', default='syonet', help='Sistema a testar')
    parser.add_argument('--mode', choices=['bronze', 'silver', 'connections', 'all'], 
                       default='connections', help='Modo de teste')
    parser.add_argument('--tables', nargs='+', help='Tabelas específicas para teste bronze')
    parser.add_argument('--verbose', '-v', action='store_true', help='Logging detalhado')
    
    args = parser.parse_args()
    
    # Configurar logging
    level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=level, 
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 TESTE REAL ETL FRAMEWORK V4")
    print("Executa o framework exatamente como seria no Airflow")
    print("=" * 70)
    print(f"📅 Sistema: {args.system}")
    print(f"🎯 Modo: {args.mode}")
    if args.tables:
        print(f"📋 Tabelas: {', '.join(args.tables)}")
    
    success = True
    
    # Tabelas padrão para teste (pequenas e rápidas)
    default_test_tables = ['syo_agenda', 'syo_usuario']
    test_tables = args.tables or default_test_tables
    
    if args.mode in ['connections', 'all']:
        success &= test_connections_only(args.system)
    
    if args.mode in ['bronze', 'all']:
        success &= test_real_bronze_processing(args.system, test_tables)
    
    if args.mode in ['silver', 'all']:
        success &= test_real_silver_processing(args.system)
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 TESTE REAL CONCLUÍDO!")
        print("✅ Framework executou como seria no Airflow")
    else:
        print("❌ TESTE REAL FALHOU!")
        print("🔧 Corrija os erros antes de usar no Airflow")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)