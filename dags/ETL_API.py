from airflow import DAG
from airflow.utils.dates import days_ago
from datetime import timedelta
from Auxiliares_API.functions import *

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    # 'email': ['<EMAIL>'], 
    # 'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1)
}

with DAG(
    'ETL_BAMAQ_API',
    default_args=default_args,
    description='DAG para ETL de dados de API da BAMAQ',
    schedule_interval='10 3,13,14,15,17,19 * * *',
    # schedule_interval=None,
    max_active_tasks=1, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_ext, end_ext = create_ext_tasks(dag, SCHEMA_CRIACAO_INSERT)

    # Task que verifica falhas e cria o chamado
    check_falhas_task = PythonOperator(
        task_id='check_failed_tasks',
        python_callable=check_failed_tasks,
        provide_context=True,
        op_kwargs={'schema': SCHEMA_CRIACAO_INSERT},
        dag=dag,
    )

    start_ext >> end_ext >> check_falhas_task