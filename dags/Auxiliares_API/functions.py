import requests
import pandas as pd
import pymssql
import psycopg2
from psycopg2 import sql
import logging
from logging.handlers import RotatingF<PERSON><PERSON><PERSON><PERSON>
from io import StringIO, BytesIO
from datetime import datetime, timedelta
from airflow.exceptions import AirflowSkipException, AirflowFailException
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from Auxiliares_API.variables import *
from Auxiliares_API.api_dictionary import *
from io import String<PERSON>
from sqlalchemy import create_engine
from sqlalchemy import inspect
from datetime import datetime, timedelta, timezone
import json
import re
import smbclient


# Configura o logger para rodar os logs
handler = RotatingFileHandler(
    'etl_log.log', maxBytes=10**7, backupCount=5, encoding='utf-8'
)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[handler, logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


def get_hook(dbname):
    """
    Establish source connection based on the database name.
    
    Args:
        dbname (str): The name of the database to connect to.

    Returns:
        Connection object: A connection object for the specified database.
    """
    logger.info(f"Establishing connection for database: {dbname}")
    try:
        if dbname == "sync":
            hook = pymssql.connect(server=SYNC_HOST, user=SYNC_USERNAME, password=SYNC_PASSWORD, database=SYNC_DBNAME, port=SYNC_PORT)
        elif dbname == "dw_corporativo":
            hook = psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST)
        logger.info(f"Connection established for database: {dbname}")
        return hook
    except Exception as e:
        logger.error(f"Error establishing connection for database: {dbname}", exc_info=True)
        raise e
    
#region ViaNuvem
# Função para gerar Token de Autenticação Vianuvem
def gerar_token_jwt(url_api, login, password):
    # URL da API
    url = url_api

    # Dados para enviar na requisição POST
    payload = {
        'login': login,
        'pass': password,
        'encryptedPass': 'false'
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json'
    }

    # Fazer a requisição POST
    response = requests.post(url, json=payload, headers=headers)

    # Verificar se a requisição foi bem-sucedida
    if response.status_code == 200:
        # Obter o JSON retornado
        data = response.json()
        return data.get('token')

    else:
        logger.error(f"Erro na requisição do Token JWT: {response.status_code}", exc_info=True)
        raise KeyError
# Função para listar documentos lançados no período
def consulta_documentos_vianuvem(url_api, token_jwt, initial_date, final_date,  search_user):
    # URL da API
    url = url_api

    # Token de autenticação (substitua pelo seu token real)
    token = token_jwt

    # Dados para enviar na requisição POST
    payload = {
        'initialDate': initial_date + ' 00:00:00',
        'finalDate': final_date + ' 23:59:59',
        'hits': 1000,
        'searchFor': search_user
    }

    # Cabeçalhos da requisição, incluindo o token de autenticação
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    # Fazer a requisição POST
    response = requests.post(url, json=payload, headers=headers)

    # Verificar se a requisição foi bem-sucedida
    if response.status_code == 200:
        # Obter o JSON retornado
        data = response.json()

        # Verificar se o JSON retornado é um dicionário
        if isinstance(data, dict) and 'documents' in data:
            # Obter a lista dentro do dicionário
            items = data['documents']

            # Selecionar campos específicos do JSON
            selected_data = []
            for item in items:
                selected_data.append({
                    'idDocument': item['idDocument'],
                    'idUser': item['idUser'],
                    'createDate': item['createDate'],
                    'establishmentName': item['establishmentName'],
                    'userName': item['userName'],
                    'idSituationCheck': item['idSituationCheck'],
                    'idEstablishment': item['idEstablishment'],
                    'currentCheck': item['currentCheck'],
                    'establishmentCnpj': item['establishmentCnpj']
                })

            # Criar um DataFrame usando os dados selecionados
            df = pd.DataFrame(selected_data)
            
            return df
        else:
            logger.error("Erro na geração do DataFrame", exc_info=True)
            raise KeyError
    else:
        logger.error(f"Erro na requisição de documentos lançados: {response.status_code}", exc_info=True)
        raise KeyError
# Função para verificar status de aprovações de documentos no Vianuvem
def consulta_documentos_atualizar_vianuvem(url_api, token_jwt, documents_list):
    # URL da API
    url = url_api

    # Token de autenticação (substitua pelo seu token real)
    token = token_jwt

    # Dados para enviar na requisição POST
    payload = {
        'documentId': documents_list,
        'hits': 1000
    }

    # Cabeçalhos da requisição, incluindo o token de autenticação
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    # Fazer a requisição POST
    response = requests.post(url, json=payload, headers=headers)

    # Verificar se a requisição foi bem-sucedida
    if response.status_code == 200:
        # Obter o JSON retornado
        data = response.json()

        # Verificar se o JSON retornado é um dicionário
        if isinstance(data, dict) and 'documents' in data:
            # Obter a lista dentro do dicionário
            items = data['documents']

            # Selecionar campos específicos do JSON
            selected_data = []
            for item in items:
                selected_data.append({
                    'idDocument': item['idDocument'],
                    'establishmentName': item['establishmentName'],
                    'idSituationCheck': item['idSituationCheck'],
                    'idEstablishment': item['idEstablishment'],
                    'currentCheck': item['currentCheck'],
                    'establishmentCnpj': item['establishmentCnpj']
                })

            # Criar um DataFrame usando os dados selecionados
            df = pd.DataFrame(selected_data)
            
            return df
        else:
            logger.error("Erro na geração do DataFrame", exc_info=True)
            raise KeyError
    else:
        logger.error(f"Erro na requisição de documentos lançados: {response.status_code}", exc_info=True)
        raise KeyError
# Função para inserir um DataFrame em um banco de dados PostgreSQL
def insert_dataframe_to_postgresql(df, table_name, schema):
    hook = get_hook('dw_corporativo')
    conn = hook  # Use a conexão diretamente
    # Cria um cursor para interagir com o banco
    with conn.cursor() as cursor:
        for index, row in df.iterrows():
            # SQL para inserir ou atualizar (MERGE/UPSERT)
            merge_sql = sql.SQL("""
            INSERT INTO {schema}.{table} (iddocument, iduser, createdate, establishmentname, username, idsituationcheck, idestablishment, currentcheck, establishmentcnpj)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (iddocument) DO NOTHING
            """).format(schema=sql.Identifier(schema), table=sql.Identifier(table_name))
            
            # Executa o comando com os valores do dataframe
            cursor.execute(merge_sql, (row['iddocument'], row['iduser'], row['createdate'], row['establishmentname'], row['username'],
                                        row['idsituationcheck'],row['idestablishment'], row['currentcheck'], row['establishmentcnpj']))
        
        # Faz commit das mudanças no banco de dados
        conn.commit()
        print("DataFrame inserido com sucesso")
        cursor.close()
        conn.close()
        # print("Connection closed")
        logger.info(f"Connection closed for database: {schema}")
# Função para listar documentos pendentes de aprovação do Gestor TI
def select_data_to_update(table_name, user, password, host, port, dbname, schema):
    try:
        logger.info(f"Trying to connect to primary server: {host}")
        conn = psycopg2.connect(
                        host = host,
                        dbname = dbname,
                        user = user,
                        password = password,
                        port = port)
        cur = conn.cursor()
        logger.info(f"Connected to primary server: {host}")

        # Listando documentos para atualizar
        query = sql.SQL(
            "select iddocument from {schema}.{table} where idsituationcheck <> 'S' and currentcheck like '%TI%'"
        ).format(
            schema=sql.Identifier(schema),
            table=sql.Identifier(table_name)
        )

        # Executando a consulta SQL
        cur.execute(query)
        # Obtendo os resultados em uma lista
        resultados = [linha[0] for linha in cur.fetchall()]

        print("Fechando conexão com banco de dados")
        conn.commit()
        cur.close()
        conn.close()
        print("Conexão encerrada")
        print("Lista de documentos retornada com sucesso")

        # Retornando a lista
        return (resultados)

    except Exception as e:
        logger.error(f"Error establishing connection for database: {dbname}", exc_info=True)
        raise e
# Função para atualizar colunas no PostgreSQL a partir de um DataFrame
def update_table_from_dataframe(df, table_name, user, password, host, port, dbname, schema, update_column, update_column_2, filter_column):
    try:
        logger.info(f"Trying to connect to primary server: {host}")
        conn = psycopg2.connect(
                        host = host,
                        dbname = dbname,
                        user = user,
                        password = password,
                        port = port)
        cur = conn.cursor()
        logger.info(f"Connected to primary server: {host}")

        # Percorrendo cada linha do dataframe
        for index, row in df.iterrows():
            # Criando a consulta SQL de update
            query = sql.SQL(
                "UPDATE {schema}.{table} SET {update_col} = %s, {update_col_2} = %s WHERE {filter_col} = %s"
            ).format(
                schema=sql.Identifier(schema),
                table=sql.Identifier(table_name),
                update_col=sql.Identifier(update_column),
                update_col_2=sql.Identifier(update_column_2),
                filter_col=sql.Identifier(filter_column)
            )

            # Executando a consulta SQL
            cur.execute(query, (row[update_column], row[update_column_2], row[filter_column]))

        # Salvando as alterações no banco de dados
        conn.commit()

    except Exception:
        logger.error(f"Error updating table: {table_name}", exc_info=True)
        raise KeyError
    finally:
        if conn:
            cur.close()
            conn.close()

def etl_insert_vianuvem(schema_criacao, table_name):
    try:
        current_hour = (datetime.now().hour - 3) % 24
        if current_hour == 0:
            print("Consultando documentos dos últimos 20 a 10 dias atrás")
            # Obtendo a data atual
            data_atual = datetime.now()
            # Retornar lançamentos dos últimos 20 a 10 dias (Capturas Brobot)
            initial_date = data_atual - timedelta(days=21)
            final_date = data_atual - timedelta(days=11)
            # Formatando a data no formato "dd/mm/aaaa"
            initial_date = initial_date.strftime("%d/%m/%Y")
            final_date = final_date.strftime("%d/%m/%Y")

            # Geração do token de autenticação
            token_jwt = gerar_token_jwt(VIANUVEM_URL_GERAR_TOKEN_JWT, VIANUVEM_USER, VIANUVEM_PASSWORD)
            # Geração do DataFrame
            for item in search_users:
                df = consulta_documentos_vianuvem(VIANUVEM_URL_CONSULTA_DOCUMENTOS, token_jwt, initial_date, final_date, item)
                if df.empty:
                    print("Dataframe vazio, possívelmente erro na API ou usuário não realizou lançamentos no Vianuvem")
                else:
                    # Converter a coluna 'createDate' para datetime
                    df['createDate'] = pd.to_datetime(df['createDate'], format='%d/%m/%Y %H:%M:%S').dt.strftime('%Y-%m-%d')
                    # Converter os nomes das colunas para minúsculas
                    df.columns = df.columns.str.lower()
                    print(df)
                    insert_dataframe_to_postgresql(df, table_name, schema_criacao)
            
            print("Consultando documentos dos últimos 10 dias")
            # Retornar lançamentos dos últimos 10 (Capturas Brobot)
            initial_date = data_atual - timedelta(days=11)
            final_date = data_atual - timedelta(days=1)
            # Formatando a data no formato "dd/mm/aaaa"
            initial_date = initial_date.strftime("%d/%m/%Y")
            final_date = final_date.strftime("%d/%m/%Y")

            # Geração do token de autenticação
            token_jwt = gerar_token_jwt(VIANUVEM_URL_GERAR_TOKEN_JWT, VIANUVEM_USER, VIANUVEM_PASSWORD)
            # Geração do DataFrame
            for item in search_users:
                df = consulta_documentos_vianuvem(VIANUVEM_URL_CONSULTA_DOCUMENTOS, token_jwt, initial_date, final_date, item)
                if df.empty:
                    print("Dataframe vazio, possívelmente erro na API ou usuário não realizou lançamentos no Vianuvem")
                else:
                    # Converter a coluna 'createDate' para datetime
                    df['createDate'] = pd.to_datetime(df['createDate'], format='%d/%m/%Y %H:%M:%S').dt.strftime('%Y-%m-%d')
                    # Converter os nomes das colunas para minúsculas
                    df.columns = df.columns.str.lower()
                    print(df)
                    insert_dataframe_to_postgresql(df, table_name, schema_criacao)
        else:
            print("Consultando documentos do dia atual")
            # Obtendo a data atual
            data_atual = datetime.now()
            # Formatando a data no formato "dd/mm/aaaa"
            initial_date = data_atual.strftime("%d/%m/%Y")
            final_date = data_atual.strftime("%d/%m/%Y")

            # Geração do token de autenticação
            token_jwt = gerar_token_jwt(VIANUVEM_URL_GERAR_TOKEN_JWT, VIANUVEM_USER, VIANUVEM_PASSWORD)
            # Geração do DataFrame
            for item in search_users:
                df = consulta_documentos_vianuvem(VIANUVEM_URL_CONSULTA_DOCUMENTOS, token_jwt, initial_date, final_date, item)
                if df.empty:
                    print("Dataframe vazio, possívelmente erro na API ou usuário não realizou lançamentos no Vianuvem")
                else:
                    # Converter a coluna 'createDate' para datetime
                    df['createDate'] = pd.to_datetime(df['createDate'], format='%d/%m/%Y %H:%M:%S').dt.strftime('%Y-%m-%d')
                    # Formatando a data no formato "aaaa-mm-dd"
                    filtro_data = data_atual.strftime("%Y-%m-%d")
                    # Filtrar documentos lançados somente na data atual
                    df = df.query(f'createDate == "{filtro_data}"')
                    # Converter os nomes das colunas para minúsculas
                    df.columns = df.columns.str.lower()
                    print(df)
                    insert_dataframe_to_postgresql(df, table_name, schema_criacao)
    except Exception as e:
        raise KeyError

def etl_update_vianuvem(schema_criacao, table_name):
    try:
        # Geração do token de autenticação
        token_jwt = gerar_token_jwt(VIANUVEM_URL_GERAR_TOKEN_JWT, VIANUVEM_USER, VIANUVEM_PASSWORD)
        # Geração da lista de documentos para atualizar
        documents_list = select_data_to_update(table_name, DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_HOST, DW_CORPORATIVO_PORT, DW_CORPORATIVO_DBNAME, schema_criacao)
        if len(documents_list) == 0:
            print("Lista vazia, não há documentos para atualizar.")
        else:
            print("Documentos pendentes de aprovação da TI:\n", documents_list)
            # Geração do DataFrame
            print("Consultando situação de aprovação atual dos documentos")
            df = consulta_documentos_atualizar_vianuvem(VIANUVEM_URL_CONSULTA_DOCUMENTOS, token_jwt, documents_list)
            if df.empty:
                print("Dataframe vazio, possível erro na API")
            else:
                # Convertendo as colunas do dataframe para minúsculas e padronizar com nome das colunas na tabela
                df.columns = df.columns.str.lower()
                print(df)
                print("Iniciando processo de atualização dos documentos")
                update_table_from_dataframe(df, table_name, DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_HOST, DW_CORPORATIVO_PORT, DW_CORPORATIVO_DBNAME, schema_criacao, 'currentcheck', 'idsituationcheck', 'iddocument')
    except Exception as e:
        raise KeyError
    
#endregion

#region MeeTime
#region Conexão
def get_data_from_db(db_type, username, password, dbname, host, port):    
    if db_type == "Postgres":
        conn = psycopg2.connect(host=host, port=port, database=dbname, user=username, password=password)
        #data = pd.read_sql_query(query, conn)
    return conn
#endregion

#region Functions

def delete_dados(list,table):
    conn = get_data_from_db('Postgres',DW_CORPORATIVO_USERNAME,DW_CORPORATIVO_PASSWORD,DW_CORPORATIVO_DBNAME,DW_CORPORATIVO_HOST,DW_CORPORATIVO_PORT)
    cur = conn.cursor()
    if list:
    # Usar %s para segurança contra SQL injection
        format_strings = ','.join(['%s'] * len(list))
    if table == "dbdwcorporativo.api_meetime_leads":    
        query_u = f"DELETE FROM {table} WHERE current_prospection_id IN ({format_strings})"
    else :
        query_u = f"DELETE FROM {table} WHERE id IN ({format_strings})"
    query_to_print = cur.mogrify(query_u, tuple(list)).decode('utf-8')
    try:
        cur.execute(query_u,tuple(list))
        conn.commit()
        resp = f"Dados Excluidos! '{table}'"
    except Exception as e:
        resp = f"Tabela não criada!{e}"
        conn.rollback()
    cur.close()
    conn.close()
    result = resp + " " + query_to_print
    return result

def get_Data(data_inicial=None, data_final=None):
    """
    Retorna as datas para consulta da API.
    
    Args:
        data_inicial (str, optional): Data inicial no formato 'YYYY-MM-DD'. Se None, usa 60 dias atrás.
        data_final (str, optional): Data final no formato 'YYYY-MM-DD'. Se None, usa a data atual.
    
    Returns:
        tuple: (data_inicial, data_final) no formato 'YYYY-MM-DD'
    """
    if data_inicial is None:
        hoje = datetime.now()
        data_inicial = (hoje - timedelta(days=4)).strftime('%Y-%m-%d')
    
    if data_final is None:
        data_final = datetime.now().strftime('%Y-%m-%d')
        
    return data_inicial, data_final

def PostgreSQL_Insert(username, password, dbname, host, df, table, tablename):
    """
    Insere dados de um DataFrame no PostgreSQL usando COPY, reorganizando as colunas automaticamente com base na tabela.
    
    Args:
        username (str): Nome de usuário do banco de dados.
        password (str): Senha do banco de dados.
        dbname (str): Nome do banco de dados.
        host (str): Host do banco de dados.
        df (DataFrame): DataFrame contendo os dados a serem inseridos.
        table (str): Nome da tabela de destino.
        tablename (str): Nome da tabela sem o schema.
    """
    # Estabelecer a conexão com o PostgreSQL
    conn = psycopg2.connect(dbname=dbname, user=username, password=password, host=host)
    cur = conn.cursor()

    # Consultar a sequência das colunas na tabela do banco de dados
    cur.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = '{SCHEMA_CRIACAO_INSERT}' AND table_name = '{tablename}'
            ORDER BY ordinal_position;
    """)
    table_columns = [row[0] for row in cur.fetchall()]
    
    if not table_columns:
       print(f"DataFrame está vazio! Não há dados para inserir na tabela {table}.")
       
    # print(df.columns)
    df.columns = df.columns.str.lower()
    # Reorganizar o DataFrame para seguir a sequência das colunas da tabela
    df = df[table_columns]
    
    missing_columns = [col for col in table_columns if col not in df.columns]
    if missing_columns:
          raise ValueError(f"As seguintes colunas estão ausentes no DataFrame: {missing_columns}")

    
    if df.empty:
       print(f"DataFrame está vazio! Não há dados para inserir na tabela {table}.")

    # Criar o comando COPY com as colunas da tabela
    column_list = ', '.join(table_columns)
    copy_command = f"COPY {table} ({column_list}) FROM STDIN WITH CSV"

    # Criar um buffer de string para escrever os dados como CSV
    sio = StringIO()
    df.to_csv(sio, index=None, header=None)  # Gera CSV sem índices e cabeçalhos
    sio.seek(0)

    try:
        # Ler os dados do buffer de string e inserir em lote no PostgreSQL
        cur.copy_expert(copy_command, sio)
        # Commit das alterações
        conn.commit()
    except Exception as e:
        # Rollback em caso de erro durante o COPY
        conn.rollback()
        print(f"Erro ao executar COPY para a tabela {table}: {e}")
    finally:
        # Fechar o cursor e a conexão
        cur.close()
        conn.close()

    return df

dict_meetime = {
    'token_ser': {
        'leads': 'meetime_leads',
        'prospections': 'meetime_prospections',
        'activities': 'meetime_activities',
        'token': MEETIME_AUTHORIZATION
    },
    'token_cam': {
        'leads': 'meetime_leads_caminhoes',
        'prospections': 'meetime_prospections_caminhoes',
        'activities': 'meetime_activities_caminhoes',
        'token': MEETIME_AUTHORIZATION_CAMINHOES
    },
    'token_maq': {
        'leads': 'meetime_leads_maq_pecas',
        'prospections': 'meetime_prospections_maq_pecas',
        'activities': 'meetime_activities_maq_pecas',
        'token': MEETIME_AUTHORIZATION_MAQ_PECAS
    },
}

def process_meetime(etapa, token, tokenname, data_inicial=None, data_final=None):
    all_data = []
    total_pages = 0
    start = 0
    data_inicial, data_final = get_Data(data_inicial, data_final)
    print(f"Processando dados de {data_inicial} até {data_final}")    

    # Construir a URL base
    if etapa == "etl_insert_meetime_prospections":
        base_url = f"https://api.meetime.com.br/v2/prospections?created_after={data_inicial}&created_before={data_final}"
        table = table_name_prefix+dict_meetime.get(tokenname).get('prospections')
        nametable = SCHEMA_CRIACAO_INSERT + "." + table
    elif etapa == "etl_insert_meetime_leads":
        base_url = f"https://api.meetime.com.br/v2/leads?lead_created_after={data_inicial}&lead_created_before={data_final}"
        table = table_name_prefix+dict_meetime.get(tokenname).get('leads')
        nametable = SCHEMA_CRIACAO_INSERT + "." + table
    elif etapa == "etl_insert_meetime_activities":
        base_url = f"https://api.meetime.com.br/v2/prospections/activities?executed_after={data_inicial}&executed_before={data_final}"
        table = table_name_prefix+dict_meetime.get(tokenname).get('activities')
        nametable = SCHEMA_CRIACAO_INSERT + "." + table
    elif etapa == "etl_update_meetime":
        base_url = f"https://api.meetime.com.br/v2/leads?lead_updated_after={data_inicial}&lead_updated_before={data_final}"
        table_leads = table_name_prefix+dict_meetime.get(tokenname).get('leads')
        nametable_leads = SCHEMA_CRIACAO_INSERT + "." + table_leads
        table_prospection = table_name_prefix+dict_meetime.get(tokenname).get('prospections')
        nametable_prospection = SCHEMA_CRIACAO_INSERT + "." + table_prospection
    elif etapa == "etl_update_activities":
        base_url = f"https://api.meetime.com.br/v2/prospections/activities?updated_after={data_inicial}&updated_before={data_final}"
        table = table_name_prefix+dict_meetime.get(tokenname).get('activities')
        nametable = SCHEMA_CRIACAO_INSERT + "." + table

    headers = {
        "accept": "application/json",
        "Authorization": f"{token}"
    }

    while True:  
        try:
            current_url = f"{base_url}&start={start}"
            print(f"Processando URL: {current_url}")
            response = requests.get(current_url, headers=headers)
            data_dict = response.json()

            # Verificar se há dados na resposta
            if not data_dict.get('data'):
                print("Nenhum dado retornado pela API")
                break

            # Extrair dados da resposta
            data_list = data_dict.get('data', [])
            all_data.extend(data_list)

            # Atualizar o número total de páginas
            total_pages += 1
            print(f"Página {total_pages} salva. Total de registros: {len(all_data)}")

            # Verificar se há mais páginas
            if len(data_list) < 100:  # Se recebemos menos de 100 registros, é a última página
                print("Não há mais páginas para processar")
                break

            # Atualizar o start para a próxima página
            start += 100
            print(f"Próxima página: {start}")

        except Exception as e:
            print(f"Erro ao processar página {total_pages + 1}: {str(e)}")
            break

    # Criar DataFrame com todos os dados coletados
    df = pd.DataFrame(all_data)
    print(f"Total de páginas processadas: {total_pages}")
    print(f"Total de registros coletados: {len(df)}")

    # Verificação de segurança
    if df.empty:
        print("Nenhum dado foi retornado pela API. DataFrame final está vazio.")
        return "Nenhum dado para processar"

    if 'id' not in df.columns:
        raise KeyError(f"A coluna 'id' não foi encontrada no DataFrame. Colunas disponíveis: {df.columns.tolist()}")

    try:
        # Gerar lista de prospecções para deletar
        if etapa == "etl_insert_meetime_prospections":
            list_prospection = df['id'].tolist()
            print(f"Deletando {len(list_prospection)} registros antigos")
            print(delete_dados(list_prospection, nametable))
            print("Inserindo novos registros")
            print(PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, df, nametable, table))
        elif etapa == "etl_insert_meetime_leads":
            list_prospection = df['current_prospection_id'].tolist()
            if 'phones' in df.columns:
                df['phones'] = df['phones'].apply(lambda x: json.dumps(x) if isinstance(x, (list, dict)) else x)
            print(f"Deletando {len(list_prospection)} registros antigos")
            print(delete_dados(list_prospection, nametable))
            print("Inserindo novos registros")
            print(PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, df, nametable, table))
        elif etapa == "etl_insert_meetime_activities":
            list_prospection = df['prospection_id'].tolist()
            print(f"Deletando {len(list_prospection)} registros antigos")
            print(delete_dados(list_prospection, nametable))
            print("Inserindo novos registros")
            print(PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, df, nametable, table))
        elif etapa == "etl_update_activities":
            list_prospection = df['prospection_id'].tolist()
            print(f"Deletando {len(list_prospection)} registros antigos")
            print(delete_dados(list_prospection, nametable))
            print("Inserindo novos registros")
            print(PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, df, nametable, table))
        elif etapa == "etl_update_meetime":
            list_prospection = df['current_prospection_id'].tolist()
            print(f"Deletando {len(list_prospection)} registros antigos de leads")
            print(delete_dados(list_prospection, nametable_leads))
            print("Inserindo novos registros de leads")
            print(PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, df, nametable_leads, table_leads))
            print("Atualizando prospecções")
            df_prospection = coleta_prospections_updated(list_prospection, token)
            print(f"Deletando {len(list_prospection)} registros antigos de prospecções")
            print(delete_dados(list_prospection, nametable_prospection))
            print("Inserindo novos registros de prospecções")
            print(PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, df_prospection, nametable_prospection, table_prospection))

        result = "Etapa Concluida"
        return result
    except Exception as e:
        print(f"Erro ao processar dados: {str(e)}")
        return f"Erro: {str(e)}"

def insert_prospections():
    results = []
    for key, value in dict_meetime.items():
        try:
            print(f"\nProcessando endpoint: {key}")
            etapa = "etl_insert_meetime_prospections"
            token = value.get('token')
            tokenname = key
            result = process_meetime(etapa, token, tokenname)
            results.append(result)
            print(f"Endpoint {key} processado com sucesso")
        except Exception as e:
            print(f"Erro ao processar endpoint {key}: {str(e)}")
            results.append(f"Erro em {key}: {str(e)}")
    return results

def insert_leads():
    results = []
    for key, value in dict_meetime.items():
        try:
            print(f"\nProcessando endpoint: {key}")
            etapa = "etl_insert_meetime_leads"
            token = value.get('token')
            tokenname = key
            print(f"Token: {tokenname}")
            result = process_meetime(etapa, token, tokenname)
            results.append(result)
            print(f"Endpoint {key} processado com sucesso")
        except Exception as e:
            print(f"Erro ao processar endpoint {key}: {str(e)}")
            results.append(f"Erro em {key}: {str(e)}")
    return results

def insert_activities():
    results = []
    for key, value in dict_meetime.items():
        try:
            print(f"\nProcessando endpoint: {key}")
            etapa = "etl_insert_meetime_activities"
            tokenname = key
            token = value.get('token')
            result = process_meetime(etapa, token, tokenname)
            results.append(result)
            print(f"Endpoint {key} processado com sucesso")
        except Exception as e:
            print(f"Erro ao processar endpoint {key}: {str(e)}")
            results.append(f"Erro em {key}: {str(e)}")
    return results

def update_meetime():
    results = []
    for key, value in dict_meetime.items():
        try:
            print(f"\nProcessando endpoint: {key}")
            etapa = "etl_update_meetime"
            token = value.get('token')
            tokenname = key
            result = process_meetime(etapa, token, tokenname)
            results.append(result)
            print(f"Endpoint {key} processado com sucesso")
        except Exception as e:
            print(f"Erro ao processar endpoint {key}: {str(e)}")
            results.append(f"Erro em {key}: {str(e)}")
    return results

def update_meetime_activities():
    results = []
    for key, value in dict_meetime.items():
        try:
            print(f"\nProcessando endpoint: {key}")
            etapa = "etl_update_activities"
            tokenname = key
            token = value.get('token')
            result = process_meetime(etapa, token, tokenname)
            results.append(result)
            print(f"Endpoint {key} processado com sucesso")
        except Exception as e:
            print(f"Erro ao processar endpoint {key}: {str(e)}")
            results.append(f"Erro em {key}: {str(e)}")
    return results

#endregion

#endregion

#region BotMaker

# Função para remover emojis
def remove_emojis(text):
    if not isinstance(text, str):
        return text
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # Emojis de rosto
        "\U0001F300-\U0001F5FF"  # Símbolos e pictogramas diversos
        "\U0001F680-\U0001F6FF"  # Transporte e símbolos relacionados
        "\U0001F1E0-\U0001F1FF"  # Bandeiras (códigos de país)
        "\U00002700-\U000027BF"  # Símbolos diversos
        "\U000024C2-\U0001F251"  # Símbolos adicionais
        "]+", 
        flags=re.UNICODE
    )
    return emoji_pattern.sub(r'', text)

def gerar_accesstoken(client_id, secret_id, refresh_token):
    # URL da API
    url = BOTMAKER_URL_ACCESSTOKEN

    # Dados para enviar na requisição POST
    payload = {  
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json',
        'clientId': client_id,
        'secretId': secret_id,
        'refreshToken': refresh_token
    }

    # Fazer a requisição POST
    response = requests.post(url, json=payload, headers=headers)

    # Verificar se a requisição foi bem-sucedida
    if response.status_code == 200:
        # Obter o JSON retornado
        data = response.json()
        return data.get('accessToken')

    else:
        logger.error(f"Erro na requisição do accessToken: {response.status_code}", exc_info=True)
        raise KeyError

def consulta_sessoes(access_token, initial_date, final_date):
    # URL da API
    url = BOTMAKER_URL_BASE + '/sessions'

    # Parâmetros para a requisição GET
    params = {
        'from': initial_date,
        'to': final_date,
        # 'include-open-sessions': 'true',
        'include-variables': 'true'
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json',
        'access-token': access_token
    }
    
    all_data = []  # Lista para armazenar todos os dados
    current_url = url  # URL inicial da API
    first_request = True

    # Loop para lidar com paginação
    while current_url:
        if first_request:
            # Primeira requisição, inclui parâmetros
            response = requests.get(current_url, params=params, headers=headers)
            first_request = False  # Após a primeira requisição, desativa o uso de params
        else:
            # Requisições subsequentes, apenas URL e headers
            response = requests.get(current_url, headers=headers)

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Obter o JSON retornado
            data = response.json()

            # Verificar se o JSON retornado é um dicionário
            if isinstance(data, dict) and 'items' in data:
                # Obter a lista de itens
                items = data['items']

                # Selecionar campos específicos do JSON
                for item in items:
                    all_data.append({
                        'id': item.get('id', None),
                        'creationTime': item.get('creationTime', None),
                        'chatId': item.get('chat', {}).get('chat', {}).get('chatId', None),
                        'firstName': item.get('chat', {}).get('firstName', None),
                        'lastName': item.get('chat', {}).get('lastName', None),
                        'protocolo': item.get('chat', {}).get('variables', {}).get('protocolo', None),
                        'botParaAtribuicao': item.get('chat', {}).get('variables', {}).get('botParaAtribuicao', None),
                        'cpf_cnpj': item.get('chat', {}).get('variables', {}).get('cpf_cnpj', None),
                        'roteadorValorGerado': item.get('roteadorValorGerado', None),
                        'typification': item.get('chat', {}).get('variables', {}).get('typification', None),
                        'quemEncerrou': item.get('chat', {}).get('variables', {}).get('quemEncerrou', None),
                        'emailOperador': item.get('chat', {}).get('variables', {}).get('emailOperador', None),
                        'csatAgente': item.get('chat', {}).get('variables', {}).get('csatAgente', None),
                        'comentarioBot': item.get('chat', {}).get('variables', {}).get('comentarioBot', None),
                        'csatBot': item.get('chat', {}).get('variables', {}).get('csatBot', None),
                        'RegistroRateio': item.get('chat', {}).get('variables', {}).get('RegistroRateio', None),
                        'tags': item.get('chat', {}).get('tags', None)
                    })
            else:
                logger.error(f"Erro na geração da lista de dados", exc_info=True)
                raise KeyError

            # Atualizar a URL para a próxima página
            current_url = data.get('nextPage', None)
            if current_url == "null":  # Tratar a string "null" como None
               current_url = None

        else:
            logger.error(f"Erro na requisição: {response.status_code}", exc_info=True)
            raise KeyError

    # Criar um DataFrame com todos os dados coletados
    df = pd.DataFrame(all_data)

    # Remover emojis de todo o DataFrame
    df = df.applymap(remove_emojis)

    # Converter o formato para datetime com fuso horário UTC
    df['creationTime'] = pd.to_datetime(df['creationTime'], utc=True)
    # Ajustar o fuso horário para Horário Padrão de Brasília
    df['creationTime'] = df['creationTime'] - pd.Timedelta(hours=3)
    # Remover informações de fuso horário (opcional, para salvar no PostgreSQL como TIMESTAMP)
    df['creationTime'] = df['creationTime'].dt.tz_localize(None)

    # Converter a coluna 'tags' de lista para string (concatenando os elementos)
    df['tags'] = df['tags'].apply(lambda x: ', '.join(x) if isinstance(x, list) else x)

    # Adicionando coluna link do chat
    df['chatLink'] = 'https://go.botmaker.com/#/chats/' + df['chatId']

    return df

def consulta_mensagens(access_token, initial_date, final_date):
    # URL da API
    url = BOTMAKER_URL_BASE + '/messages'

    # Parâmetros para a requisição GET
    params = {
        'from': initial_date,
        'to': final_date,
        'long-term-search': 'true'
    }

    # Cabeçalhos da requisição
    headers = {
        'Content-Type': 'application/json',
        'access-token': access_token
    }
    
    all_data = []  # Lista para armazenar todos os dados
    current_url = url  # URL inicial da API
    first_request = True

    # Loop para lidar com paginação
    while current_url:
        if first_request:
            # Primeira requisição, inclui parâmetros
            response = requests.get(current_url, params=params, headers=headers)
            first_request = False  # Após a primeira requisição, desativa o uso de params
        else:
            # Requisições subsequentes, apenas URL e headers
            response = requests.get(current_url, headers=headers)

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Obter o JSON retornado
            data = response.json()

            # Verificar se o JSON retornado é um dicionário
            if isinstance(data, dict) and 'items' in data:
                # Obter a lista de itens
                items = data['items']

                # Selecionar campos específicos do JSON
                for item in items:
                    all_data.append({
                        'id': item.get('id', None),
                        'creationTime': item.get('creationTime', None),
                        'from': item.get('from', None),
                        'queueId': item.get('queueId', None),
                        'sessionCreationTime': item.get('sessionCreationTime', None),
                        'chatId': item.get('chat', {}).get('chatId', {}),
                        'channelId': item.get('chat', {}).get('channelId', {}),
                        'contactId': item.get('chat', {}).get('contactId', {}),
                        'sessionId': item.get('sessionId', None)
                    })
            else:
                logger.error(f"Erro na geração da lista de dados", exc_info=True)
                raise KeyError

            # Atualizar a URL para a próxima página
            current_url = data.get('nextPage', None)
            if current_url == "null":  # Tratar a string "null" como None
               current_url = None

        else:
            logger.error(f"Erro na requisição: {response.status_code}", exc_info=True)
            raise KeyError

    # Criar um DataFrame com todos os dados coletados
    df = pd.DataFrame(all_data)
    # Converter o formato para datetime com fuso horário UTC
    df['creationTime'] = pd.to_datetime(df['creationTime'], utc=True)
    df['sessionCreationTime'] = pd.to_datetime(df['sessionCreationTime'], utc=True)

    return df

def insert_botmaker_data(df, table_name, schema):
    hook = get_hook('dw_corporativo')
    conn = hook  # Use a conexão diretamente
    # Cria um cursor para interagir com o banco
    with conn.cursor() as cursor:
        for index, row in df.iterrows():
            
            if table_name == 'api_botmaker_listasessoes':
                # SQL para inserir ou atualizar (MERGE/UPSERT)
                merge_sql = sql.SQL("""
                INSERT INTO {schema}.{table} (id, creation_time, first_name, last_name, protocolo, bot_paraatribuicao, cpf_cnpj, roteador_valorgerado, typification, quem_encerrou, email_operador, csat_agente, comentario_bot, csat_bot, registro_rateio, tags)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
                """).format(schema=sql.Identifier(schema), table=sql.Identifier(table_name))
                
                # Executa o comando com os valores do dataframe
                cursor.execute(merge_sql, (row['id'], row['creationTime'], row['firstName'], row['lastName'], row['protocolo'], row['botParaAtribuicao'], row['cpf_cnpj'],
                                        row['roteadorValorGerado'], row['typification'], row['quemEncerrou'], row['emailOperador'], row['csatAgente'], row['comentarioBot'],
                                        row['csatBot'], row['RegistroRateio'], row['tags']))
                
            elif table_name == 'api_botmaker_listamensagens':
                #region insert sessoes
                # SQL para inserir ou atualizar (MERGE/UPSERT)
                merge_sql = sql.SQL("""
                INSERT INTO {schema}.{table} (id, creation_time, "from", queue_id, session_creation_time, chat_id, channel_id, contact_id, session_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
                """).format(schema=sql.Identifier(schema), table=sql.Identifier(table_name))
                
                # Executa o comando com os valores do dataframe
                cursor.execute(merge_sql, (row['id'], row['creationTime'], row['from'], row['queueId'], row['sessionCreationTime'], row['chatId'], row['channelId'], row['contactId'], row['sessionId']))
                #endregion
        
        # Faz commit das mudanças no banco de dados
        conn.commit()
        print("DataFrame inserido com sucesso")
        cursor.close()
        conn.close()
        # print("Connection closed")
        logger.info(f"Connection closed for database: {schema}")

def retorna_parquet_sessoes():
        
        print('Iniciando pesquisa pelo arquivo parquet no FileServer')
        
        # Configuração de credenciais
        server = "nova.mmbq.local"
        share = "\BI$\Botmaker\CX"

        smbclient.register_session(server, username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

        try:
            # Caminho completo do compartilhamento e pasta
            smb_path = f"\\\\{server}\\{share}".rstrip("\\")
            
            # Lista os arquivos na pasta
            print(f"Listando arquivos em: {smb_path}")
            entries = smbclient.listdir(smb_path)
            print("Arquivos disponíveis:", entries)
            
            # Filtra o arquivo parquet desejado (modifique conforme necessário)
            parquet_file = next((entry for entry in entries if entry.endswith('sessoes_cxia_cxbot.parquet')), None)
            if not parquet_file:
                print("Nenhum arquivo Parquet encontrado.")
            else:
                print(f"Lendo o arquivo Parquet: {parquet_file}")
                
                # Caminho completo do arquivo
                file_path = f"{smb_path}\\{parquet_file}"
                
                # Abre o arquivo usando o smbclient
                with smbclient.open_file(file_path, mode='rb') as f:
                    file_content = f.read()
                
                # Lê o conteúdo do arquivo Parquet em um DataFrame
                buffer = BytesIO(file_content)
                df = pd.read_parquet(buffer)

                # Converter a coluna para datetime (se necessário)
                df['creationTime'] = pd.to_datetime(df['creationTime'])
                
                print("DataFrame carregado com sucesso!")
                print(df.head())  # Exibe as primeiras linhas do DataFrame

                return df

            # Encerre a conexão após o uso
            smbclient.delete_session(server)
            print(f"Conexão com o servidor {server} encerrada.")

        except Exception as e:
            print(f"Erro: {e}")

def atualiza_parquet_sessoes():

    df_parquet_desatualizado = retorna_parquet_sessoes()
    
    max_date_parquet = df_parquet_desatualizado['creationTime'].max()
    max_date_parquet = max_date_parquet + timedelta(hours=3)
    # Converter para o formato ISO 8601 com o sufixo 'Z'
    max_date_parquet = max_date_parquet.strftime('%Y-%m-%dT%H:%M:%SZ')
    print(f'Maior data retornada: {max_date_parquet}')

    access_token = gerar_accesstoken(BOTMAKER_CLIENT_ID, BOTMAKER_SECRET_ID, BOTMAKER_REFRESH_TOKEN)
    print(f"Token gerado: {access_token}")

    current_hour = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    print(f'Consultando sessões entre: {max_date_parquet} e {current_hour}')
    df_sessoes = (consulta_sessoes(access_token, max_date_parquet, current_hour))
    # Filtrar registros onde a coluna 'tags' contenha 'BotdeCXIA' ou 'BotdeCXFixo'
    df_sessoes = df_sessoes[df_sessoes['tags'].str.contains('BotdeCXIA|BotdeCXFixo', case=False, na=False)]
    # Filtrar linhas sessões que já existam no Parquet Original
    df_sessoes_atualizado = df_sessoes[~df_sessoes['id'].isin(df_parquet_desatualizado['id'])]

    print('DataFrame de sessões atualizado gerado com sucesso.')
    print(df_sessoes_atualizado.head())

    # Combinar os dataframes
    df_final = pd.concat([df_parquet_desatualizado, df_sessoes_atualizado], ignore_index=True)


    # Configuração de credenciais
    server = "nova.mmbq.local"
    share = "\BI$\Botmaker\CX"

    smbclient.register_session(server, username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

    try:
        # Caminho completo do compartilhamento e arquivo
        smb_path = f"\\\\{server}\\{share}".rstrip("\\")
        parquet_file_name = "sessoes_cxia_cxbot.parquet"
        file_path = f"{smb_path}\\{parquet_file_name}"

        # Converte o DataFrame para o formato Parquet em memória
        buffer = BytesIO()
        df_final.to_parquet(buffer, index=False)
        buffer.seek(0)  # Retorna ao início do buffer para leitura

        # Salva o arquivo no servidor (substitui se já existir)
        with smbclient.open_file(file_path, mode='wb') as f:
            f.write(buffer.read())

        print(f"Arquivo {parquet_file_name} salvo com sucesso em {smb_path}!")

    except Exception as e:
        print(f"Erro ao salvar o arquivo Parquet: {e}")

    finally:
        # Encerra a conexão com o servidor
        smbclient.delete_session(server)
        print(f"Conexão com o servidor {server} encerrada.")

def atualiza_parquet_sessoes_manual():

    access_token = gerar_accesstoken(BOTMAKER_CLIENT_ID, BOTMAKER_SECRET_ID, BOTMAKER_REFRESH_TOKEN)
    print(f"Token gerado: {access_token}")

    current_hour = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    print(f'Consultando sessões entre: 2025-02-18T03:10:03Z e 2025-02-19T23:59:59Z')
    df_sessoes = (consulta_sessoes(access_token, '2025-02-18T03:10:03Z', '2025-02-19T23:59:59Z'))
    # Filtrar registros onde a coluna 'tags' contenha 'BotdeCXIA' ou 'BotdeCXFixo'
    df_sessoes = df_sessoes[df_sessoes['tags'].str.contains('BotdeCXIA|BotdeCXFixo', case=False, na=False)]

    print('DataFrame de sessões atualizado gerado com sucesso.')
    print(df_sessoes.head())

    # Combinar os dataframes
    df_final = df_sessoes


    # Configuração de credenciais
    server = "nova.mmbq.local"
    share = "\BI$\Botmaker"

    smbclient.register_session(server, username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

    try:
        # Caminho completo do compartilhamento e arquivo
        smb_path = f"\\\\{server}\\{share}".rstrip("\\")
        parquet_file_name = "sessoes_cxia_cxbot.parquet"
        file_path = f"{smb_path}\\{parquet_file_name}"

        # Converte o DataFrame para o formato Parquet em memória
        buffer = BytesIO()
        df_final.to_parquet(buffer, index=False)
        buffer.seek(0)  # Retorna ao início do buffer para leitura

        # Salva o arquivo no servidor (substitui se já existir)
        with smbclient.open_file(file_path, mode='wb') as f:
            f.write(buffer.read())

        print(f"Arquivo {parquet_file_name} salvo com sucesso em {smb_path}!")

    except Exception as e:
        print(f"Erro ao salvar o arquivo Parquet: {e}")

    finally:
        # Encerra a conexão com o servidor
        smbclient.delete_session(server)
        print(f"Conexão com o servidor {server} encerrada.")

#endregion

def etl_task(schema_criacao, function):
    if function == "etl_insert_vianuvem":
        table_name = table_name_prefix + "vianuvem_documentos_lancados"
        etl_insert_vianuvem(schema_criacao, table_name)
        
    elif function == "etl_update_vianuvem":
        table_name = table_name_prefix + "vianuvem_documentos_lancados"
        etl_update_vianuvem(schema_criacao, table_name)
        
    elif function == "etl_insert_meetime_prospections":
        insert_prospections()
        
    elif function == "etl_insert_meetime_leads":
        insert_leads()
    
    elif function == "etl_insert_meetime_activities":
        insert_activities()
        
    elif function == "etl_update_meetime":
        update_meetime()
    
    elif function == "etl_update_activities":
        update_meetime_activities()

    elif function == "etl_botmaker_parquet":
        atualiza_parquet_sessoes()
        # atualiza_parquet_sessoes_manual()

def create_ext_tasks(dag, schema_criacao):
    """
    Create Airflow tasks for the ETL process.
    
    Args:
        dag (DAG): The Airflow DAG object.
        schema_criacao (str): The database schema.
        task_function (str): The fuction to execute in task.

    Returns:
        Tuple: Start and end Airflow tasks for the ETL process.
    """
    start_ext_task = EmptyOperator(task_id='start_etl', dag=dag)
    end_ext_task = EmptyOperator(task_id='end_etl', dag=dag, trigger_rule='all_done')

    def skip_task_message():
        raise AirflowSkipException("Task skipped as it was not scheduled for this time.")
    
    for key in api_etl:
        current_hour = (datetime.now().hour - 3) % 24
        task_id = f'api_{schema_criacao}_{api_etl[key].get("kwargs", {}).get("function").lower()}'
        task_function = api_etl[key].get("kwargs", {}).get("function")

        if (api_etl[key].get("times_per_day") == 1 and current_hour in (0, 1, 2, 3)) or \
           (api_etl[key].get("times_per_day") == 2 and current_hour in (0, 1, 2, 3, 12, 13)) or \
           (api_etl[key].get("times_per_day") == 3 and current_hour in (0, 1, 2, 3, 10, 14, 15)) or \
           (api_etl[key].get("times_per_day") == 4 and current_hour in (0, 1, 2, 3, 10, 11, 12, 13, 17)) or \
           (api_etl[key].get("times_per_day") == 5 and current_hour in (0, 1, 2, 3, 10, 11, 12, 13, 14, 17)) or \
           api_etl[key].get("times_per_day") == 6:
        
            task = PythonOperator(
                task_id=task_id,
                python_callable=etl_task,
                op_args=[schema_criacao,
                         task_function],
                trigger_rule='all_done',
                dag=dag
            )
        
        else:
            task = PythonOperator(
                task_id=task_id,
                python_callable=skip_task_message,
                trigger_rule='all_done',
                dag=dag   
            )
                    
        start_ext_task >> task >> end_ext_task

    return start_ext_task, end_ext_task

def check_failed_tasks(**kwargs):
    dag_run = kwargs['dag_run']
    dag_id = kwargs['dag'].dag_id
    task_instances = dag_run.get_task_instances()
    
    failed_tasks = [ti for ti in task_instances if ti.state == 'failed']
    
    if failed_tasks and kwargs.get("schema").lower() == "dbdwcorporativo":
        failed_task_names = [task.task_id for task in failed_tasks]

        description = (
            f"Failed Tasks: {', '.join(failed_task_names)}\n"
        )

        # Truncate the description to 4000 characters
        if len(description) > 4000:
            description = description[:4000]

        # Log para debugging
        logging.info(f"Falha nas seguintes tasks: {description}")

        # Executa a procedure no banco de dados
        try:
            hook = get_hook('sync')
            conn = hook  # Use a conexão diretamente
            cur = conn.cursor()

            # Escapando caracteres especiais para a string da descrição
            escaped_description = description.replace("'", "''")

            cur.execute(f"EXEC bqpr_SyncAPI_AberturaChamado_FalhaExecucaoDagAirflow '{dag_id}', '{escaped_description}'")
            conn.commit()
            cur.close()
        except Exception as e:
            logging.error(f"Erro ao executar a procedure: {e}")

#updates_meetime

def coleta_prospections(start, data_inicial, data_final, token):
    # Função para buscar uma página específica da API
    url = f"https://api.meetime.com.br/v2/prospections?start={start}&created_after={data_inicial}&created_before={data_final}"
    print(url)
    headers = {
        "accept": "application/json",
        "Authorization": f"{token}"
    }
    response = requests.get(url, headers=headers)
    return response.json()

def coleta_leads(start, data_inicial, data_final, token):
    url = f"https://api.meetime.com.br/v2/leads?start={start}&lead_created_after={data_inicial}&lead_created_before={data_final}"
    headers = {
        "accept": "application/json",
        "Authorization": f"{token}"
    }
    response = requests.get(url, headers=headers)
    return response.json()

def coleta_activities(start, data_inicial, data_final, token):
    url = f"https://api.meetime.com.br/v2/prospections/activities?start={start}&executed_after={data_inicial}&executed_before={data_final}"
    headers = {
        "accept": "application/json",
        "Authorization": f"{token}"
    }
    response = requests.get(url, headers=headers)
    return response.json()

def coleta_activities_updated(start, data_inicial, data_final, token):
    url = f"https://api.meetime.com.br/v2/prospections/activities?start={start}&updated_after={data_inicial}&updated_before={data_final}"
    headers = {
        "accept": "application/json",
        "Authorization": f"{token}"
    }
    response = requests.get(url, headers=headers)
    return response.json()

def coleta_leads_updated(start, data_inicial, data_final, token):
    url = f"https://api.meetime.com.br/v2/leads?start={start}&lead_updated_after={data_inicial}&lead_updated_before={data_final}"
    headers = {
        "accept": "application/json",
        "Authorization": f"{token}"
    }
    response = requests.get(url, headers=headers)
    return response.json()