from Auxiliares_API.passwords import *

#region Database

# Schema para a criação e inserção de registros do ETL
SCHEMA_CRIACAO_INSERT = 'dbdwcorporativo'
# SCHEMA_CRIACAO_INSERT = 'staging'

# Prefixo para nome de tabelas do ETL
table_name_prefix = 'api_'

# DwCorporativo
DW_CORPORATIVO_HOST = '***********'
DW_CORPORATIVO_DBNAME = 'postgres'
DW_BOTMAKER_SCHEMA = 'dbdwbotmaker'
DW_CORPORATIVO_USERNAME = 'bq_dwcorporativo_u'
DW_CORPORATIVO_PASSWORD = DW_CORPORATIVO_PASSWORD
DW_CORPORATIVO_PORT = '5432'

# Sync
SYNC_USERNAME = 'bq_dwcorporativo_u'
SYNC_PASSWORD = SYNC_PASSWORD
SYNC_DBNAME = 'SYNC_BAMAQ'
SYNC_HOST =  '***********'
SYNC_PORT = '50666'

# Usuarios com permissoes gerais nas tabelas
all_grants = ["master", "iebt_daniel_laranjo", "ext_leticia_furletti", "bq_dwcorporativo_u", "ext_rafael_neto"]

#endregion

#region Vianuvem

# Dados para API do Vianuvem
VIANUVEM_USER = 'integracao.bi'
VIANUVEM_PASSWORD = VIANUVEM_PASSWORD
VIANUVEM_URL_GERAR_TOKEN_JWT = 'https://webservices.vianuvem.com.br/AdminVianuvem/public/token'
VIANUVEM_URL_CONSULTA_DOCUMENTOS = 'https://webservices.vianuvem.com.br/AdminVianuvem/api/document/search'
# Usúarios para consulta de documentos no Vianuvem
search_users = ['Victoria Leite Soares Santos', 'Geovanna Quezia O. de Cassia', 'Dayane Fernanda Souza Trindade', 'Geisiane Georgia de Oliveira', 'geisiane.oliveira']

#endregion

#region Tallos
TALLOS_USER = '<EMAIL>'

#endregion

#region BotMaker

BOTMAKER_URL_ACCESSTOKEN = 'https://go.botmaker.com/api/v1.0/auth/credentials'
BOTMAKER_URL_BASE = 'https://api.botmaker.com/v2.0'
BOTMAKER_CLIENT_ID = 'grupobamaq'
BOTMAKER_SECRET_ID = BOTMAKER_SECRET_ID
BOTMAKER_REFRESH_TOKEN = BOTMAKER_REFRESH_TOKEN

#endregion

WINDOWS_USERNAME = r'MMBQ\powerbi.consorcio'
WINDOWS_PASSWORD = WINDOWS_PASSWORD