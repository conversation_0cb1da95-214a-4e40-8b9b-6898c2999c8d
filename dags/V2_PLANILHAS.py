from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

instances = [
    Planilha('PWBI Painel de Vagas Grupo Bamaq R&S 2025.xlsm')
    .folder_id('7ad99f1a-2025-43e5-80b3-7d51bb8107de')
    .tab('PS').kwargs(
        table_name='Colaboradores'
    ),
    <PERSON><PERSON><PERSON>('Metas BI DealernetWF - DPR.xlsx')
    .folder_id('429ca0fc-c3cc-493c-b31d-58da9784c4bf')
    .tab('Meta').kwargs(
        table_name='MetaBIDealernetWF_DPR'
    )
    .tab('DeParaProdutos2').kwargs(
        table_name='BIDealernetWF_DeParaProdutos2'
    ),
    <PERSON><PERSON><PERSON>('Metas BI DealernetWF - DVS.xlsx')
    .folder_id('2f452902-e804-420e-9c59-335376ca50c6')
    .tab('Meta').kwargs(
        table_name='MetaBIDealernetWF_DVS'
    ),
    Planilha('Estoque_GWM_BH.xlsx')
    .folder_id('76208bf0-dd7a-41af-99d5-0bc548f0298b')
    .tab().kwargs(
        table_name='Estoque_GWM_BH'
    ),
    Planilha('Estoque_GWM_CG.xlsx')
    .folder_id('60cbebb6-dbd1-4847-ad51-354858d923d7')
    .tab().kwargs(
        table_name='Estoque_GWM_CG'
    ),
    Planilha('Estoque_GWM_Pamp.xlsx')
    .folder_id('a46ffcbd-a04f-431c-9c0b-69d0d6681100')
    .tab().kwargs(
        table_name='Estoque_GWM_pamp'
    ),
    Planilha('Estoque_GWM_Dourados.xlsx')
    .folder_id('f3579aa0-a300-4d21-927a-ce96e93d4225')
    .tab().kwargs(
        table_name='Estoque_GWM_dourados'
    ),
    Planilha('Estoque_GWM_CO.xlsx')
    .folder_id('2cedc218-0bf0-4825-ae1e-e533de5816fc')
    .tab().kwargs(
        table_name='Estoque_GWM_CO'
    ),
    Planilha('Dados_GWM_BH.xlsx')
    .folder_id('76208bf0-dd7a-41af-99d5-0bc548f0298b')
    .tab().kwargs(
        table_name='GWM_BH'
    ),
    Planilha('Dados_GWM_CG.xlsx')
    .folder_id('60cbebb6-dbd1-4847-ad51-354858d923d7')
    .tab().kwargs(
        table_name='GWM_CG'
    ),
    Planilha('Dados_GWM_Pamp.xlsx')
    .folder_id('a46ffcbd-a04f-431c-9c0b-69d0d6681100')
    .tab().kwargs(
        table_name='GWM_pamp'
    ),
    Planilha('Dados_GWM_CO.xlsx')
    .folder_id('2cedc218-0bf0-4825-ae1e-e533de5816fc')
    .tab().kwargs(
        table_name='GWM_CO'
    ),    
    Planilha('Dados_GWM_Dourados.xlsx')
    .folder_id('f3579aa0-a300-4d21-927a-ce96e93d4225')
    .tab().kwargs(
        table_name='GWM_dourados'
    ),
    Planilha('Entrevista de Desligamento(1-91).xlsx')
    .file_id('78768e21-fe1e-4d59-aaa3-bb0430e07aeb')
    .tab().kwargs(
        table_name='Desligamento'
    ),
    Planilha('Consorcio_Auxiliar.xlsx')
    .file_id('8273aef5-3e5f-4496-b33b-1e655eee176f')
    .tab('Consorcio').kwargs(
        table_name='consorcio_auxiliar'
    ),
    Planilha('Estoque_Cariacica_BH.xlsx')
    .folder_id('7a364d0d-a618-4726-be95-d23911b669fd')
    .tab().kwargs(
        table_name='estoque_nacionalizar_porschebh'
    ),
    Planilha('Estoque_Cariacica_SSA.xlsx')
    .folder_id('bb750619-f619-4663-a7c6-50eefade9d7a')
    .tab().kwargs(
        table_name='estoque_nacionalizar_porschessa'
    ),
    Planilha('Estoque_Cariacica_CG.xlsx')
    .folder_id('633003c1-f62e-42db-9ebe-c2965622d0af')
    .tab().kwargs(
        table_name='estoque_nacionalizar_porschescg'
    ),
    Planilha('Dados_BI_Automotivo_Porsche_vendas_BH_Novos.xlsx')
    .file_id('9237123e-2b2d-40ba-ad4a-726503cb883c')
    .tab('Fila de Espera').kwargs(
        table_name='fila_de_espera_porsche_bh',
        column_names = ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"]
    ),
    Planilha('Dados_BI_Automotivo_Porsche_vendas_Salvador_Novos.xlsx')
    .file_id('815bcd7e-55fc-4017-8ea2-343bf31d4ce6')
    .tab('Fila de Espera').kwargs(
        table_name='fila_de_espera_porsche_ssa',
        column_names = ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"]
    ),
    Planilha('Dados_BI_Automotivo_Bamaq_Premium.xlsx')
    .file_id('eee1467d-e0d4-4bac-b6cf-1154263bcbed')
    .tab('Vendedores').kwargs(
        table_name='vendedores_premium',
        dtype = {"cpf":str}
    ),
    Planilha('Dados_BI_Automotivo_Mercedes_vendas_BH_Raja.xlsx')
    .file_id('8f02d27d-55ec-4146-be59-df73d3fa925c')
    .tab('Fila de Espera').kwargs(
        table_name='fila_de_espera_mercedes_bh',
        column_names = ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"]
    ),
    Planilha('Dados_BI_Automotivo_Mercedes_vendas_JF.xlsx')
    .file_id('997c421f-4a19-495b-b7c8-d7798ea85d9b')
    .tab('Fila de Espera').kwargs(
        table_name='fila_de_espera_mercedes_jf',
        column_names = ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"]
    ),
    Planilha('Referencias_BI_Automotivo.xlsx')
    .file_id('A35A267F-27FB-4CB0-BCA6-87CBD0C1AF25')
    .tab().kwargs(
        table_name='referencias_bi_automotivo'
    ),
    Planilha('Controle de Venda Direta - Haval H6 e ORA.xlsx')
    .file_id('EE49C6EE-D78F-448A-A75B-9CCAAECE15DB')
    .tab('Faturamento Haval').kwargs(
        table_name='faturamento_haval',
        skiprows=1
    )
    .tab('Faturamento ORA').kwargs(
        table_name='faturamento_ora',
        skiprows=1
    )
    .tab('Comissionamento Fixo').kwargs(
        table_name='comissionamentofixo_gwm'
    )
    .tab('Comissionamento Variável').kwargs(
        table_name='comissionamentovariavel_gwm'
    ),
    Planilha('Tomorrow_guide_GWM.xlsx').times_per_day(5)
    .file_id('4B6CF4CF-5FA0-48F3-8CB1-23EECD3755EB')
    .tab('GWM').kwargs(
        table_name='tomorrow_guide_GWM'
    ),
    Planilha('CRM_Porsche_BH.xlsx')
    .file_id('65979B79-BA2E-4A02-8E3B-1C1BE1B7B5B2')
    .tab('Insucessos Test Drive').kwargs(
        table_name='crm_test_drive_bh',
        column_names = ["periodo", "modelo", "motivo_insucesso", "quantidade"]
    ),
    Planilha('CRM_Porsche_SSA.xlsx')
    .file_id('A2A8DE60-2FC4-40B3-81DE-FBC0185D1026')
    .tab('Insucessos Test Drive').kwargs(
        table_name='crm_test_drive_ssa',
        column_names = ["periodo", "modelo", "motivo_insucesso", "quantidade"]
    ),
    Planilha('Metas Test Drive.xlsx').times_per_day(5)
    .file_id('523DBC76-3DE8-4D40-951D-66995B26C645')
    .tab('Metas Test Drive').kwargs(
        table_name='Metas_Test_Drive'
    ),
    Planilha('Ata_Reuniao.xlsx').times_per_day(5)
    .file_id('fe3a0077-0f7d-4aac-a0c3-c12cbcef5ac0')
    .tab('Ata').kwargs(
        table_name='Atas_Comite'
    ),    
    Planilha('Referencia_Orcado.xlsx')
    .file_id('d6ed738b-361f-46b8-8dc3-cc2f4fddd017')
    .tab('Orcado').kwargs(
        table_name='referencia_orcado'
    ),
    Planilha('Dados_Controladoria_Consolidado.xlsx')
    .file_id('65a16263-505d-42d5-a5c2-ea5c4f914edb')
    .tab('Vendedores - Pesados').kwargs(
        table_name='custo_vendedores_pesados'
    )
    .tab('Vendedores - Automotivo').kwargs(
        table_name='custo_vendedores_automotivo'
    )
    .tab('DePara - Veiculos').kwargs(
        table_name='DePara_Contabilidade'
    )
    .tab('DePara - Oficina').kwargs(
        table_name='DePara_Contabilidade_Oficina'
    )
    .tab('DePara - Clientes').kwargs(
        table_name='DePara_Contabilidade_Clientes'
    )
    .tab('Venda direta - Nfs pendentes').kwargs(
        table_name='venda_direta_pendentes'
    ),
    Planilha('Documentacao.xlsx')
    .file_id('c85ba229-3967-4931-9033-81a4ddd08f81')
    .tab('doc').kwargs(
        table_name='documentacao_controladoria'
    ),
    Planilha('Documentacao.xlsx')
    .file_id('51843CF1-A682-458C-A246-BBE1CF95F4B1')
    .tab('doc').kwargs(
        table_name='documentacao_interno'
    ),
    Planilha('Documentacao.xlsx')
    .file_id('B1D5E437-CAC6-44B7-B874-0C35138CC74D')
    .tab('doc').kwargs(
        table_name='documentacao_contabilidade'
    ),
    Planilha('Documentacao_BI.xlsx')
    .file_id('1863F566-6C23-46DC-8DEF-C796062A9A28')
    .tab('doc').kwargs(
        table_name='documentacao_bi'
    ),
    Planilha('Starclass.xlsx').times_per_day(5)
    .file_id('41f732cb-6c0e-4def-826d-b08956bcb1eb')
    .tab('Qualificadores').kwargs(
        table_name='qualificadores'
    ),
    Planilha('Starclass.xlsx').times_per_day(5)
    .file_id('41f732cb-6c0e-4def-826d-b08956bcb1eb')
    .tab('Indicadores').kwargs(
        table_name='indicadores'
    ),
    Planilha('Cadastros - Consórcio 20.xlsx')
    .file_id('4AE7F82F-4662-4743-9E6F-F6B899343E4C')
    .tab('Administradoras').kwargs(
        table_name='bacen_administradoras'
    )
    .tab('Planos_de_Venda').kwargs(
        table_name='consorcio_planos_venda' 
    ),
    Planilha('Indicadores.xlsx')
    .file_id('500B94BA-DD38-4DEC-B87D-28208490069C')
    .tab('NPS CSI').kwargs(
        table_name='Indicadores_NPS'
    ),    
    Planilha('Importação Afix - Aquisição.xlsx')
    .file_id('3F398B25-78A2-4604-B76E-77B6C6AA9BD0')
    .tab().kwargs(
        table_name='dealer_capex'
    ), 
    Planilha('Usuarios_Syonet.xlsx').times_per_day(5)
    .file_id('5039DFAE-FB8D-4D97-8B4C-7C6C7145C93B')
    .tab().kwargs(
        table_name='usuarios_syonet_cgf'
    ), 
    Planilha('dealer_prefeitura_iss_retido.xlsx')
    .file_id('3324782a-825a-44ad-8864-6b3c5ac32e46')
    .tab().kwargs(
        table_name='dealer_prefeitura_iss_retido'
    ),   
    Custom( func = etl_planilhas_automotivo,
        conn_type = "planilha",
        times_per_day = 5,
        index = {
            "Consorcio.xlsx": {
                "file_id": "626D4ABD-748A-471B-9D5C-E5920861F23B",
                "pages": {
                    "Porsche BH": {
                        "metas_objetivos_id": 2,   
                        "cod_empresa": "401"
                    },
                    "Porsche SSA": {
                        "metas_objetivos_id": 2,   
                        "cod_empresa": "403"
                    },
                    "Mercedes Raja": {
                        "metas_objetivos_id": 2,   
                        "cod_empresa": "101"
                    },
                    "Mercedes Barão": {
                        "metas_objetivos_id": 2,   
                        "cod_empresa": "102"
                    },
                    "Mercedes JF": {
                        "metas_objetivos_id": 2,   
                        "cod_empresa": "103"
                    },
                    "Gwm BH": {
                        "metas_objetivos_id": 2,    
                        "cod_empresa": "601"
                    },
                    "Gwm CG": {
                        "metas_objetivos_id": 2,    
                        "cod_empresa": "602"
                    },
                    "Premium": {
                        "metas_objetivos_id": 2,    
                        "cod_empresa": "23"
                    },
                    "Gwm Pampulha": {
                        "metas_objetivos_id": 2,    
                        "cod_empresa": "605"
                    },
                    "Gwm Dourados": {
                        "metas_objetivos_id": 2,    
                        "cod_empresa": "608"
                    },
                    "Porsche CG": {
                        "metas_objetivos_id": 2,    
                        "cod_empresa": "404"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Porsche_vendas_BH_Novos.xlsx": {
                "file_id": "9237123e-2b2d-40ba-ad4a-726503cb883c",
                "pages": {
                    "PSQ": {
                        "metas_objetivos_id": 5,   
                        "cod_empresa": "401"
                    },
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "401"
                    },
                    "CES": {
                        "metas_objetivos_id": 8,   
                        "cod_empresa": "401"
                    },
                    "Vendas Veiculos (Volume)": {
                        "metas_objetivos_id": 1,   
                        "cod_empresa": "401"
                    },
                    "Vendas Veiculos (Valor R$)": {
                        "metas_objetivos_id": 20,   
                        "cod_empresa": "401"
                    },
                    "Margem Veiculos": {
                        "metas_objetivos_id": 16,    
                        "cod_empresa": "401"
                    },
                    "Reservas a faturar": {
                        "metas_objetivos_id": 42,   
                        "cod_empresa": "401"
                    },
                    "Cotas Alocadas": {
                        "sheet_name": "Cotas Alocadas",
                        "metas_objetivos_id": 43,   
                        "cod_empresa": "401"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Porsche_pos_vendas_BH.xlsx": {
                "file_id": "36da166b-3e49-4386-8d9b-e366b1189770",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "401"
                    },
                    "CSI": {
                        "sheet_name": "CSI",
                        "metas_objetivos_id": 18,   
                        "cod_empresa": "401"
                    },
                    "CES": {
                        "sheet_name": "CES",
                        "metas_objetivos_id": 22,   
                        "cod_empresa": "401"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "401"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "401"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "401"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "401"
                    },
                    "Vendas VN": {
                        "metas_objetivos_id": 32,   
                        "cod_empresa": "401"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Porsche_pos_vendas_Salvador.xlsx": {
                "file_id": "DDDD1769-E5E7-4D16-94EC-42FE383F645C",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "403"
                    },
                    "CSI": {
                        "metas_objetivos_id": 18,   
                        "cod_empresa": "403"
                    },
                    "CES": {
                        "metas_objetivos_id": 22,   
                        "cod_empresa": "403"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "403"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "403"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "403"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "403"
                    },
                    "Vendas VN": {
                        "metas_objetivos_id": 32,   
                        "cod_empresa": "403"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Porsche_pos_vendas_Campo_grande.xlsx": {
                "file_id": "B515B319-AC51-4758-A31F-B965E4C5A0CF",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "404"
                    },
                    "CSI": {
                        "metas_objetivos_id": 18,   
                        "cod_empresa": "404"
                    },
                    "CES": {
                        "metas_objetivos_id": 22,   
                        "cod_empresa": "404"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "404"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "404"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "404"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "404"
                    },
                    "Vendas VN": {
                        "metas_objetivos_id": 32,   
                        "cod_empresa": "404"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Porsche_vendas_Salvador_Novos.xlsx": {
                "file_id": "815bcd7e-55fc-4017-8ea2-343bf31d4ce6",
                "pages": {
                    "PSQ": {
                        "metas_objetivos_id": 5,   
                        "cod_empresa": "403"
                    },
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "403"
                    },
                    "CES": {
                        "metas_objetivos_id": 8,   
                        "cod_empresa": "403"
                    },
                    "Vendas Veiculos (Volume)": {
                        "metas_objetivos_id": 1,   
                        "cod_empresa": "403"
                    },
                    "Vendas Veiculos (Valor R$)": {
                        "metas_objetivos_id": 20,   
                        "cod_empresa": "403"
                    },
                    "Margem Veiculos": {
                        "metas_objetivos_id": 16,   
                        "cod_empresa": "403"
                    },
                    "Reservas a faturar": {
                        "metas_objetivos_id": 42,   
                        "cod_empresa": "403"
                    },
                    "Cotas Alocadas": {
                        "metas_objetivos_id": 43,   
                        "cod_empresa": "403"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Porsche_vendas_Campo_Grande_Novos": {
                "file_id": "1CF79B29-EEDC-4D09-A07A-A69BE0E82113",
                "pages": {
                    "PSQ": {
                        "metas_objetivos_id": 5,   
                        "cod_empresa": "404"
                    },
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "404"
                    },
                    "CES": {
                        "metas_objetivos_id": 8,   
                        "cod_empresa": "404"
                    },
                    "Vendas Veiculos (Volume)": {
                        "metas_objetivos_id": 1,   
                        "cod_empresa": "404"
                    },
                    "Vendas Veiculos (Valor R$)": {
                        "metas_objetivos_id": 20,   
                        "cod_empresa": "404"
                    },
                    "Margem Veiculos": {
                        "metas_objetivos_id": 16,   
                        "cod_empresa": "404"
                    },
                    "Reservas a faturar": {
                        "metas_objetivos_id": 42,   
                        "cod_empresa": "404"
                    },
                    "Cotas Alocadas": {
                        "metas_objetivos_id": 43,   
                        "cod_empresa": "404"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_Vendas_BH.xlsx": {
                "file_id": "db000dd2-1c72-4632-8edf-24704369d545",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "601"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "601"
                    },
                    "Reservas do Mês": {
                        "metas_objetivos_id": 38,   
                        "cod_empresa": "601"
                    },
                    "Pendências emplacamento": {
                        "metas_objetivos_id": 39,   
                        "cod_empresa": "601"
                    },
                    "Estoque": {
                        "metas_objetivos_id": 41,   
                        "cod_empresa": "601"
                    },
                    "Comissoes VD": {
                        "metas_objetivos_id": 55,   
                        "cod_empresa": "601"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_Vendas_Pamp.xlsx": {
                "file_id": "A2F6093D-B022-4EDF-8799-3FBA095246EC",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "605"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "605"
                    },
                    "Reservas do Mês": {
                        "metas_objetivos_id": 38,   
                        "cod_empresa": "605"
                    },
                    "Pendências emplacamento": {
                        "metas_objetivos_id": 39,   
                        "cod_empresa": "605"
                    },
                    "Estoque": {
                        "metas_objetivos_id": 41,   
                        "cod_empresa": "605"
                    },
                    "Comissoes VD": {
                        "metas_objetivos_id": 55,   
                        "cod_empresa": "605"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_Vendas_Dourados.xlsx": {
                "file_id": "94F3C00D-B8F1-4A5F-AA23-D66A216329D",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "608"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "608"
                    },
                    "Reservas do Mês": {
                        "metas_objetivos_id": 38,   
                        "cod_empresa": "608"
                    },
                    "Pendências emplacamento": {
                        "metas_objetivos_id": 39,   
                        "cod_empresa": "608"
                    },
                    "Estoque": {
                        "metas_objetivos_id": 41,   
                        "cod_empresa": "608"
                    },
                    "Comissoes VD": {
                        "metas_objetivos_id": 55,   
                        "cod_empresa": "608"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_Vendas_CO.xlsx": {
                "file_id": "94F3C00D-B8F1-4A5F-AA23-D66A216329DC",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "609"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "609"
                    },
                    "Reservas do Mês": {
                        "metas_objetivos_id": 38,   
                        "cod_empresa": "609"
                    },
                    "Pendências emplacamento": {
                        "metas_objetivos_id": 39,   
                        "cod_empresa": "609"
                    },
                    "Estoque": {
                        "metas_objetivos_id": 41,   
                        "cod_empresa": "609"
                    },
                    "Comissoes VD": {
                        "metas_objetivos_id": 55,   
                        "cod_empresa": "609"
                    }
                }
            },                        
            "1-Dados_BI_Automotivo_GWM_pos_vendas_BH.xlsx": {
                "file_id": "CF745A03-0ED5-408B-971B-ED8C4AD653F1",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "601"
                    },
                    "NPS": {
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "601"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "601"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "601"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "601"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "601"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_pos_vendas_Dourados.xlsx": {
                "file_id": "46DCB029-29B4-4B41-B703-44BAFB2ADEB5",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "608"
                    },
                    "NPS": {
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "608"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "608"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "608"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "608"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "608"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_pos_vendas_CO.xlsx": {
                "file_id": "03699DED-2D3F-4F7E-95E8-8BDAACCAE334",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "609"
                    },
                    "NPS": {
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "609"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "609"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "609"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "609"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "609"
                    }
                }
            },                        
            "1-Dados_BI_Automotivo_GWM_pos_vendas_Pamp.xlsx": {
                "file_id": "383F8BD0-53D3-4B5B-A6AA-9EB88724E41A",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "605"
                    },
                    "NPS": {
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "605"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "605"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "605"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "605"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "605"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_Vendas_MS.xlsx": {
                "file_id": "56b3ef86-fe38-4812-8559-5a222c2c7038",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "602"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "602"
                    },
                    "Reservas do Mês": {
                        "metas_objetivos_id": 38,   
                        "cod_empresa": "602"
                    },
                    "Pendências emplacamento": {
                        "metas_objetivos_id": 39,   
                        "cod_empresa": "602"
                    },
                    "Estoque": {
                        "sheet_name": "Estoque",
                        "metas_objetivos_id": 41,   
                        "cod_empresa": "602"
                    },
                    "Comissoes VD": {
                        "metas_objetivos_id": 55,   
                        "cod_empresa": "602"
                    }
                }
            },
            "1-Dados_BI_Automotivo_GWM_pos_vendas_MS.xlsx": {
                "file_id": "EB6775E6-5CD2-4F3C-BABE-72C63E94445E",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "602"
                    },
                    "NPS": {
                        "sheet_name": "NPS",
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "602"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "602"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "602"
                    },
                    "Passagens": {
                        "sheet_name": "Passagens",
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "602"
                    },
                    "Margem": {
                        "metas_objetivos_id": 30,   
                        "cod_empresa": "602"
                    }
                }
            }, 
            "1-Dados_BI_Automotivo_Bamaq_Premium.xlsx": {
                "file_id": "eee1467d-e0d4-4bac-b6cf-1154263bcbed",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "23"
                    },
                    "CSI": {
                        "metas_objetivos_id": 7,    
                        "cod_empresa": "23"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "23"
                    },
                    "CSAT": {
                        "metas_objetivos_id": 120,   
                        "cod_empresa": "23"
                    },                    
                    "Margem Veiculos": {
                        "metas_objetivos_id": 16,   
                        "cod_empresa": "23"
                    },
                    "Performance de Captação": {
                        "metas_objetivos_id": 14,   
                        "cod_empresa": "23"
                    },
                    "Estoque": {
                        "metas_objetivos_id": 15,   
                        "cod_empresa": "23"
                    },
                    "Intermediacao Venda - PC SSA": {
                        "metas_objetivos_id": 64,   
                        "cod_empresa": "403"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Mercedes_pos_vendas_BH.xlsx": {
                "file_id": "2e36e189-c520-45b7-af55-0a5c96f57d48",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,  
                        "cod_empresa": "102"
                    },
                    "CSI": {
                        "metas_objetivos_id": 18,   
                        "cod_empresa": "102"
                    },
                    "NPS": {
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "102"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "102"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "102"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "102"
                    },
                    "Vendas VN": {
                        "metas_objetivos_id": 32,   
                        "cod_empresa": "102"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Mercedes_vendas_BH_Raja.xlsx": {
                "file_id": "8f02d27d-55ec-4146-be59-df73d3fa925c",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "101"
                    },
                    "CSI": {
                        "metas_objetivos_id": 7,   
                        "cod_empresa": "101"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "101"
                    },
                    "Vendas Veiculos Novos (R$)": {
                        "metas_objetivos_id": 20,   
                        "cod_empresa": "101"
                    },
                    "Reservas a faturar": {
                        "metas_objetivos_id": 40,   
                        "cod_empresa": "101"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Mercedes_vendas_JF.xlsx": {
                "file_id": "997c421f-4a19-495b-b7c8-d7798ea85d9b",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 62,   
                        "cod_empresa": "103"
                    },
                    "CSI": {
                        "metas_objetivos_id": 7,   
                        "cod_empresa": "103"
                    },
                    "NPS": {
                        "metas_objetivos_id": 12,   
                        "cod_empresa": "103"
                    },
                    "Vendas Veiculos Novos (R$)": {
                        "metas_objetivos_id": 20,   
                        "cod_empresa": "103"
                    },
                    "Reservas a faturar": {
                        "metas_objetivos_id": 40,
                        "cod_empresa": "103"
                    }
                }
            },
            "1-Dados_BI_Automotivo_Mercedes_pos_vendas_JF.xlsx": {
                "file_id": "bdb78f9d-3664-47d2-a992-7ebd7475d7ad",
                "pages": {
                    "Percentual de conversão": {
                        "metas_objetivos_id": 63,   
                        "cod_empresa": "103"
                    },
                    "CSI": {
                        "metas_objetivos_id": 18,   
                        "cod_empresa": "103"
                    },
                    "NPS": {
                        "metas_objetivos_id": 17,   
                        "cod_empresa": "103"
                    },
                    "Performance de peças": {
                        "metas_objetivos_id": 3,   
                        "cod_empresa": "103"
                    },
                    "Performance de serviços": {
                        "metas_objetivos_id": 4,   
                        "cod_empresa": "103"
                    },
                    "Passagens": {
                        "metas_objetivos_id": 29,   
                        "cod_empresa": "103"
                    },
                    "Vendas VN": {
                        "metas_objetivos_id": 32,   
                        "cod_empresa": "103"
                    }
                }
            },
            "Market_Share.xlsx": {
                "file_id": "E60E90B2-ADE7-43A2-B64C-8B5AC0501081",
                "pages": {
                    "Porsche BH": {
                        "metas_objetivos_id": 11,   
                        "cod_empresa": "401"
                    },
                    "Porsche SSA": {
                        "metas_objetivos_id": 11,   
                        "cod_empresa": "403"
                    },
                    "Mercedes BH": {
                        "metas_objetivos_id": 11,   
                        "cod_empresa": "101"
                    },
                    "Mercedes JF": {
                        "metas_objetivos_id": 11,   
                        "cod_empresa": "103"
                    },
                    "GWM BH": {
                        "metas_objetivos_id": 11,    
                        "cod_empresa": "601"
                    },
                    "GWM CG": {
                        "metas_objetivos_id": 11,    
                        "cod_empresa": "602"
                    },
                    "GWM Dourados": {
                        "metas_objetivos_id": 11,    
                        "cod_empresa": "608"
                    }
                }
            },
            "Starclass.xlsx": {
                "file_id": "41f732cb-6c0e-4def-826d-b08956bcb1eb",
                "pages": {
                    "Mercedes BH - Vendas": {
                        "metas_objetivos_id": 33,   
                        "cod_empresa": "101"
                    },
                    "Mercedes BH - Pós Vendas": {
                        "metas_objetivos_id": 34,   
                        "cod_empresa": "101"
                    },
                    "Mercedes JF - Vendas": {
                        "metas_objetivos_id": 33,   
                        "cod_empresa": "103"
                    },
                    "Mercedes JF - Pós Vendas": {
                        "metas_objetivos_id": 34,    
                        "cod_empresa": "103"
                    }
                }
            },
            "Bonus_Performance.xlsx": {
                "file_id": "345311fb-9334-4f7a-a722-ae549832cc50",
                "pages": {
                    "Porsche BH - Vendas": {
                        "metas_objetivos_id": 36,   
                        "cod_empresa": "401"
                    },
                    "Porsche BH - Outros": {
                        "metas_objetivos_id": 37,   
                        "cod_empresa": "401"
                    },
                    "Porsche BH - Pós Vendas": {
                        "metas_objetivos_id": 35,   
                        "cod_empresa": "401"
                    },
                    "Porsche SSA - Vendas": {
                        "metas_objetivos_id": 36,   
                        "cod_empresa": "403"
                    },
                    "Porsche SSA - Outros": {
                        "metas_objetivos_id": 37,   
                        "cod_empresa": "403"
                    },
                    "Porsche SSA - Pós Vendas": {
                        "metas_objetivos_id": 35,    
                        "cod_empresa": "403"
                    }
                }
            },
            "CRM_Porsche_BH.xlsx": {
                "file_id": "65979B79-BA2E-4A02-8E3B-1C1BE1B7B5B2",
                "pages": {
                    "Funil de Vendas": {
                        "metas_objetivos_id": 56,   
                        "cod_empresa": "401"
                    },
                    "Vendas por Mídia": {
                        "metas_objetivos_id": 57,   
                        "cod_empresa": "401"
                    },
                    "Vendas por Canal": {
                        "metas_objetivos_id": 58,   
                        "cod_empresa": "401"
                    },
                    "Vendas por Semana": {
                        "metas_objetivos_id": 59,   
                        "cod_empresa": "401"
                    },
                    "Insucessos": {
                        "metas_objetivos_id": 60,   
                        "cod_empresa": "401"
                    },
                    "Test Drive": {
                        "metas_objetivos_id": 61,    
                        "cod_empresa": "401"
                    }
                }
            },
            "CRM_Porsche_SSA.xlsx": {
                "file_id": "A2A8DE60-2FC4-40B3-81DE-FBC0185D1026",
                "pages": {
                    "Funil de Vendas": {
                        "metas_objetivos_id": 56,   
                        "cod_empresa": "403"
                    },
                    "Vendas por Mídia": {
                        "metas_objetivos_id": 57,   
                        "cod_empresa": "403"
                    },
                    "Vendas por Canal": {
                        "metas_objetivos_id": 58,   
                        "cod_empresa": "403"
                    },
                    "Vendas por Semana": {
                        "metas_objetivos_id": 59,   
                        "cod_empresa": "403"
                    },
                    "Insucessos": {
                        "metas_objetivos_id": 60,   
                        "cod_empresa": "403"
                    },
                    "Test Drive": {
                        "metas_objetivos_id": 61,    
                        "cod_empresa": "403"
                    }
                }
            },
            "Metas_repasse_premium.xlsx": {
                "file_id": "F7953F4B-F3F3-4127-8545-CD5C327C6F25",
                "pages": {
                    "Volume": {
                        "metas_objetivos_id": 98,   
                        "cod_empresa": "23"
                    },
                    "Receita": {
                        "metas_objetivos_id": 99,   
                        "cod_empresa": "23"
                    },
                    "Autoavaliar Pc Belo Horizonte": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "401"
                    },
                    "Autoavaliar Pc Salvador": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "402"
                    },
                    "Autoavaliar Mb Matriz": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "101"
                    },
                    "Autoavaliar MB JF": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "103"
                    },
                    "Autoavaliar GWM Pampulha": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "605"
                    },
                    "Autoavaliar GWM Matriz": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "601"
                    },
                    "Autoavaliar GWM Campo Grande": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "602"
                    },
                    "Autoavaliar GWM Dourados": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "608"
                    },
                    "Autoavaliar Repasse": {
                        "metas_objetivos_id": 101,   
                        "cod_empresa": "000"
                    }                                              
                }
            }            
        }
        ),
    Custom( func = etl_dados_mef,
        conn_type = "file_system",
        treatment = True,
        index = {
            "\\172.16.100.201\9-Power-BI$\Projeto\MEF": {
                ("\\Base\\Realizado", "\\Base\\Orçado"): {  
                    "table_name": "dados_mef",
                    "files": (".csv", ".dat"),
                    "ignore": ("MEF_Orcado_CGF_25.csv",),
                    "dtype": {'Filial': str}
                },
                "\\Base\\Drill Orçado": {
                    "table_name": "dados_mef_drill",
                    "files": (".csv", ".dat", ".xlsx"),
                    "dtype": {'Unnamed: 3': str},
                    "columns": ["Years", "Period", "Scenario", "Entity", "Filial", "Centro_Custo", "Account", "Produto_Canal", "amount"]
                },
                "\\Parâmetros": {
                    "table_name": "mef_depara",
                    "tab_name": "dFormatoMEF",
                    "files": "DeParaMEF.xlsx"
                }
                ,
                "\\Base\\Carga Contábil": {
                    "table_name": "mef_carga_contabil",
                    "files": (".csv", ".dat", ".xlsx",".txt")
                }
            }
        }
    ),
    # Custom( func = etl_dados_mef,
    #     conn_type = "file_system",
    #     treatment = False,
    #     index = {
    #         "\\nova.mmbq.local\9-Power-BI$\Projeto\MEF": {
    #             "\\Parâmetros": {
    #                 "table_name": "mef_depara_raw",
    #                 "tab_name": "dFormatoMEF",
    #                 "files": "DeParaMEF.xlsx"
    #             }
    #         }
    #     }
    # )
   ]
 
with DAG(
    'V2-PLANILHAS',
    default_args=default_args,
    description='DAG para ETL de planilhas da BAMAQ',
    schedule_interval=schedule_interval,
    max_active_runs=1,
    max_active_tasks=3, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, instances, triggers=['V2-FLEX'])

    start_etl_task >> end_etl_task >> dag_stats