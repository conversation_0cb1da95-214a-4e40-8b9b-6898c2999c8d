from airflow import DAG
from airflow.utils.dates import days_ago
from azure.storage.blob import BlobServiceClient
import io
import pandas as pd
from __Antigos_v1.Auxiliares_ETL.functions import *  # Funções auxiliares do projeto

# Parâmetros de configuração do Azure Blob Storage
account_url = "https://strfininter.blob.core.windows.net"
credential = "sp=racwdl&st=2024-12-03T12:39:28Z&se=2054-12-03T20:39:28Z&spr=https&sv=2022-11-02&sr=c&sig=71Uu%2FQ6dVhppPYw5s8lNJbkXgi1WxLFvrwbvf%2BzPkX4%3D"
container_name = "files"
 
# Função para consulta e exportação de dados para CSV no Azure Blob Storage
def query_to_csv(query: str, output_file: str):
    """
    Executa uma consulta SQL e exporta os resultados para um arquivo CSV.
    O arquivo CSV é enviado para o Azure Blob Storage.
    """
    try:
        # Conectar ao banco de dados
        with get_hook('newcon') as conn:
            df = pd.read_sql_query(query, conn)

        # Salvar DataFrame como CSV em memória
        csv_data = df.to_csv(index=False)

        # Conectar ao Azure Blob Storage
        blob_service_client = BlobServiceClient(account_url=account_url, credential=credential)
        container_client = blob_service_client.get_container_client(container_name)

        # Upload do arquivo CSV para o Blob
        blob_client = container_client.get_blob_client(output_file)
        blob_client.upload_blob(io.BytesIO(csv_data.encode()), overwrite=True)
 
        # Log para indicar sucesso
        print(f"DataFrame enviado com sucesso para o Blob Storage como '{output_file}'.")

    except Exception as e:
        # Log de erro caso ocorra algum problema
        print(f"Erro ao processar a consulta e enviar o arquivo CSV: {e}")
        raise e

# # Definir a DAG do Airflow
# with DAG(
#     'ETL_INTER',  # Nome da DAG
#     default_args={'owner': 'airflow'},  # Propriedades padrão da DAG
#     schedule_interval='0 0 * * *',  # Intervalo de execução (meia-noite diariamente)
#     start_date=days_ago(1),  # Data de início
#     catchup=False,  # Não fazer execução retroativa
# ) as dag:

#     # Task 1: Consulta de valores a receber
#     query_receber = """
#         exec bq_pr_INTER_ParcelasReceber
#     """

#     file_name_receber = "financeiro_receber.csv"
#     task_1 = PythonOperator(
#         task_id="financeiro_receber",  # Nome da task
#         python_callable=query_to_csv,  # Função a ser executada
#         op_args=[query_receber, file_name_receber],  # Argumentos para a função
#     )

#     # Task 2: Consulta de valores recebidos
#     query_recebido = """
#         exec bq_pr_INTER_ParcelasRecebidas
#     """

#     file_name_recebido = "financeiro_recebido.csv"
#     task_2 = PythonOperator(
#         task_id="financeiro_recebido",  # Nome da task
#         python_callable=query_to_csv,  # Função a ser executada
#         op_args=[query_recebido, file_name_recebido],  # Argumentos para a função
#     )

#     # Task 3: Consulta de valores Atrasados
#     query_atrasado = """
#         exec bq_pr_INTER_ParcelasAtraso
#     """

#     file_name_atrasado = "financeiro_atrasado.csv"
#     task_3 = PythonOperator(
#         task_id="financeiro_atrasado",  # Nome da task
#         python_callable=query_to_csv,  # Função a ser executada
#         op_args=[query_atrasado, file_name_atrasado],  # Argumentos para a função
#     )

#     # Task que verifica falhas e cria o chamado
#     check_falhas_task = PythonOperator(
#         task_id='check_failed_tasks',
#         python_callable=check_failed_tasks,
#         provide_context=True,
#         op_kwargs={'schema': 'dbdwcorporativo'},
#         dag=dag,
#     )
 
#     # Definindo a ordem das tasks
#     task_1 >> task_2 >> task_3 >> check_falhas_task
 