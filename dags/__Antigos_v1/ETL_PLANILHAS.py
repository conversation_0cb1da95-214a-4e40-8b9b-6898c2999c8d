from datetime import datetime, timedelta
from airflow import DAG
from airflow.utils.dates import days_ago
from __Antigos_v1.Auxiliares_Planilhas.my_functions import *

dump_types = {
    0 : "Nulo", # Não interagir com as tabelas
    1 : "Carga_Inteira", # Criar uma tabela do zero a partir do dataframe gerado pelos dados da planilha
    2 : "Tranformação_Metas_Objetivos", # Transformação genérica dos dados da planilha para o formato da tabela metas_objetivos_valores
    3 : "Dados_MEF", # Consolidando planilhas do MEF
    4 : "Consolidando_Avaliacao_Periodo"# Consolidando planilhas de avaliação do período
}

# Definição do DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=3)
}

# with DAG(
#     'ETL_BAMAQ_PLANILHAS',
#     default_args=default_args,
#     description='DAG para ETL de dados de planilhas do sharepoint',
#     schedule_interval='45 2,14,17 * * *',
#     start_date=days_ago(1),
#     catchup=False,
# ) as dag:

#     start_tasks, end_tasks = create_tasks(dag, SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD, SITE_URL, dump_types)

#     start_tasks >> end_tasks