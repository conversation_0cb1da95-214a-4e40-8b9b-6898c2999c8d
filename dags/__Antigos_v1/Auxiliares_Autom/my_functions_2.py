from office365.runtime.auth.authentication_context import AuthenticationContext
from office365.sharepoint.client_context import ClientContext
import psycopg2
import pandas as pd
from io import BytesIO
from __Antigos_v1.Auxiliares_Autom.passwords import SHAREPOINT_PASSWORD
from __Antigos_v1.Auxiliares_Autom.variables import SITE_URL, SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD
from io import StringIO
import pymssql
import cx_Oracle
from __Antigos_v1.Auxiliares_Autom.variables import (
        gwm_bh_folder_id, gwm_bh_file_name_1, gwm_bh_file_name_2,
        gwm_cg_folder_id, gwm_cg_file_name_1, gwm_cg_file_name_2
    )

def PostgreSQL_Create(username, password, dbname, host, query):
    try:
        conn = psycopg2.connect(dbname=dbname, user=username, password=password, host=host)
        cursor = conn.cursor()
        cursor.execute(query)
        conn.commit()
        cursor.close()
        conn.close()
    except psycopg2.Error as e:
        print(f"Erro ao conectar ao banco de dados: {e}")

def PostgreSQL_Insert(username, password, dbname, host, data, table):
    conn = psycopg2.connect(dbname=dbname, user=username, password=password, host=host)
    cur = conn.cursor()
    # Criar um buffer de string para escrever os dados como CSV
    sio = StringIO()
    data.to_csv(sio, index=None, header=None)
    sio.seek(0)
    # Ler os dados do buffer de string e inserir em lote no PostgreSQL
    cur.copy_expert(f"COPY {table} FROM STDIN WITH CSV", sio)
    # Commit das alterações
    conn.commit()
    # Feche o cursor e a conexão
    cur.close()
    conn.close()
    return data

def get_data_from_db(db_type, username, password, dbname, host, port, query):
    if db_type == "Oracle":
        dsn_tns = cx_Oracle.makedsn(host, port, service_name=dbname)
        connection = cx_Oracle.connect(user=username, password=password, dsn=dsn_tns)
        data = pd.read_sql_query(query, connection)
    if db_type == "Mssql":
        hook = pymssql.connect(server=host, user=username, password=password, database=dbname, port=port)
        data = pd.read_sql_query(query, hook)
    if db_type == "Postgres":
        hook = psycopg2.connect(host=host, port=port, database=dbname, user=username, password=password)
        data = pd.read_sql_query(query, hook)
    return data

def get_sharepoint_excel_df(username, password, site_url, file_id, sheet_name = 0):
    try:
        ctx_auth = AuthenticationContext(site_url)
        if ctx_auth.acquire_token_for_user(username, password):
            ctx = ClientContext(site_url, ctx_auth)
            file = ctx.web.get_file_by_id(file_id)
            ctx.load(file)
            ctx.execute_query()
            file_stream = BytesIO(file.read())
            df = pd.read_excel(file_stream, sheet_name=sheet_name)
            return df
        else:
            raise Exception("Falha na autenticação")

    except Exception as e:
        print("Erro:", e)

def merge_and_clean_data(df1, df2):

    df1["VENDEDOR"] = df1["VENDEDOR"].replace({"Ú": "U", "Ã": "A", "Ç": "C", "Ê": "E"}, regex=True).str.upper()
    df2["VENDEDOR_DEALER"] = df2["VENDEDOR_DEALER"].replace({"Ú": "U", "Ã": "A", "Ç": "C", "Ê": "E"}, regex=True).str.upper()

    merge = pd.merge(df1, df2, how="left", left_on="VENDEDOR", right_on="VENDEDOR_DEALER")
    
    # Aplicando a lógica da cláusula CASE WHEN do SQL para a coluna "VENDEDOR_DEALER"
    merge["VENDEDOR_DEALER"] = merge.apply(lambda row: "Repasse" if row["REPASSE"] == "SIM" else row["VENDEDOR_DEALER"], axis=1)
    merge["COD_EMPRESA"] = merge["LOJA"].apply(translate_cod_empresa)
    merge["EMPRESA_SYS"] = merge["LOJA"].apply(translate_system)

    data = pd.DataFrame({
        "DATA": merge["DATA"], 
        "CLIENTE": merge["CLIENTE"],
        "DOCCLI": merge["DOCCLI"],
        "VALOR": merge["VALOR"],  # Tratando valores inválidos em 'VALOR'
        "CUSTO": merge["CUSTO"],
        "STATUS": merge["STATUS"],
        "NOVO_USADO": "Seminovo",
        "EMPRESA_SYS": merge["EMPRESA_SYS"],  # Obtendo o prefixo da loja
        "COD_EMPRESA": merge["COD_EMPRESA"],  # Obtendo o código da loja
        "EMITIU_NOTA": merge["EMITIU_NOTA"],
        "VENDEDOR_DEALER": merge["VENDEDOR_DEALER"],
        "TIPO_FATURAMENTO": merge["TIPO_FATURAMENTO"],
        "ITEM": merge["ITEM"],
        "DESCRICAO_MODELO": merge["DESCRICAO_MODELO"],
        "CHASSI": merge["CHASSI"],
        "PERIODOESTOQUE": merge["PERIODOESTOQUE"],
        "DGIRO": merge["DGIRO"],
        "TABREF": "Vendas"
    })

    return data

def ETL_BAMAQ_PREMIUM(data):

    sistema_origem = data["meta_objetivo_subitem"].apply(lambda value: translate_system(value) if value in ["Porsche (BH)", "Porsche (Salvador)", "GWM (BH)", "GWM (CG)", "Mercedes Benz (BH)", "Mercedes Benz (JF)"] else "DEALERNETWF")
    cod_empresa = data["meta_objetivo_subitem"].apply(lambda value: translate_cod_empresa(value) if value in ["Porsche (BH)", "Porsche (Salvador)", "GWM (BH)", "GWM (CG)", "Mercedes Benz (BH)", "Mercedes Benz (JF)"] else "23")
    df1 = pd.DataFrame({
        "metas_objetivos_id": data["metas_objetivos_id"],
        "sistema_origem": sistema_origem,
        "cod_empresa": cod_empresa,
        "periodo": data["periodo"],
        "valor": data["valor"],
        "tipo_registro": data["tipo_registro"],
        "meta_objetivo_subitem": data["meta_objetivo_subitem"].apply(lambda value: value if value not in ["Porsche (BH)", "Porsche (Salvador)", "GWM (BH)", "GWM (CG)", "Mercedes Benz (BH)", "Mercedes Benz (JF)"] else None)
    })

    return df1

def translate_cod_empresa(value):
    if value == "Porsche (BH)":
        return "401"
    if value == "Porsche (Salvador)":
        return "403"
    elif value == "GWM (BH)":
        return "601"
    elif value == "GWM (CG)":
        return "602"
    elif value == "Mercedes Benz (BH)":
        return "101"
    elif value == "Mercedes Benz (JF)":
        return "103"
    else:
        return "23"
    
def translate_system(value):
    if value == "Porsche (BH)" or value == "Porsche (Salvador)" or value == "GWM (BH)" or value == "GWM (CG)" or value == "Mercedes Benz (BH)" or value == "Mercedes Benz (JF)":
        return "NBS"
    else:
        return "DEALERNETWF"

def generate_etl_data():
    from __Antigos_v1.Auxiliares_Autom.variables import (
        bp_file_id, ps_bp_sheet_name, ps_bp_metas_objetivos_id, 
        nps_bp_sheet_name, nps_bp_metas_objetivos_id, 
        av_bp_sheet_name, av_bp_metas_objetivos_id, 
        lg_bp_sheet_name, lg_bp_metas_objetivos_id, 
        mpbh_file_id, bp_system, bp_cod_empresa, 
        cap_pp_sheet_name, cap_pp_metas_objetivos_id, 
        csi_mp_sheet_name, csi_mp_metas_objetivos_id, 
        nps_mp_sheet_name, nps_mp_metas_objetivos_id, 
        mvbh_file_id, mvbh_system, mvbh_cod_empresa, 
        mvjf_file_id, mvjf_system, mvjf_cod_empresa, 
        cp_mv_sheet_name, cp_mv_metas_objetivos_id, 
        csi_mv_sheet_name, csi_mv_metas_objetivos_id, 
        nps_mv_sheet_name, nps_mv_metas_objetivos_id, 
        vv_sheet_name, vv_mv_metas_objetivos_id, 
        at_mv_sheet_name, at_mv_metas_objetivos_id, 
        td_mv_sheet_name, td_mv_metas_objetivos_id, 
        ms_mv_sheet_name, ms_mv_metas_objetivos_id, 
        pvbh_file_id, pvbh_system, pvbh_cod_empresa, 
        pvba_file_id, pvba_system, pvba_cod_empresa, 
        cp_pv_sheet_name, cp_pv_metas_objetivos_id, 
        csi_pv_sheet_name, csi_pv_metas_objetivos_id, 
        ces_pv_sheet_name, ces_pv_metas_objetivos_id, 
        vv_pv_sheet_name, vv_pv_metas_objetivos_id, 
        cas_pv_sheet_name, cas_pv_metas_objetivos_id, 
        td_pv_sheet_name, td_pv_metas_objetivos_id, 
        ms_pv_sheet_name, ms_pv_metas_objetivos_id,
        mpbh_cod_empresa, mpbh_system, cp_pp_metas_objetivos_id,
        cp_pp_sheet_name, csi_pp_sheet_name, csi_pp_metas_objetivos_id,
        ces_pp_sheet_name, ces_pp_metas_objetivos_id,
        cp_mp_sheet_name, cp_mp_metas_objetivos_id,
        ppbh_file_id, ppbh_system, ppbh_cod_empresa,
        mpjf_file_id, mpjf_system, mpjf_cod_empresa,
        et_bp_sheet_name, et_bp_metas_objetivos_id,
        vs_bp_sheet_name, vs_bp_metas_objetivos_id,
        pp_mp_sheet_name, pp_mp_metas_objetivos_id,
        ps_mp_sheet_name, ps_mp_metas_objetivos_id,
        pp_pp_sheet_name, pp_pp_metas_objetivos_id,
        ps_pp_sheet_name, ps_pp_metas_objetivos_id,

    )

    etl_data = {}

    def add_etl_data(file_id, system, cod_empresa, sheets):
        etl_data[file_id] = {
            "system": system,
            "cod_empresa": cod_empresa,
            "sheets": sheets
        }

    add_etl_data(bp_file_id, bp_system, bp_cod_empresa, [
        {"sheet_name": ps_bp_sheet_name, "metas_objetivos_id": ps_bp_metas_objetivos_id},
        {"sheet_name": nps_bp_sheet_name, "metas_objetivos_id": nps_bp_metas_objetivos_id},
        {"sheet_name": av_bp_sheet_name, "metas_objetivos_id": av_bp_metas_objetivos_id},
        {"sheet_name": lg_bp_sheet_name, "metas_objetivos_id": lg_bp_metas_objetivos_id},
        {"sheet_name": et_bp_sheet_name, "metas_objetivos_id": et_bp_metas_objetivos_id},
        {"sheet_name": vs_bp_sheet_name, "metas_objetivos_id": vs_bp_metas_objetivos_id}
    ])

    add_etl_data(mpbh_file_id, mpbh_system, mpbh_cod_empresa, [
        {"sheet_name": cp_mp_sheet_name, "metas_objetivos_id": cp_mp_metas_objetivos_id},
        {"sheet_name": csi_mp_sheet_name, "metas_objetivos_id": csi_mp_metas_objetivos_id},
        {"sheet_name": nps_mp_sheet_name, "metas_objetivos_id": nps_mp_metas_objetivos_id},
        {"sheet_name": pp_mp_sheet_name, "metas_objetivos_id": pp_mp_metas_objetivos_id},
        {"sheet_name": ps_mp_sheet_name, "metas_objetivos_id": ps_mp_metas_objetivos_id}
    ])

    add_etl_data(mpjf_file_id, mpjf_system, mpjf_cod_empresa, [
        {"sheet_name": cp_mp_sheet_name, "metas_objetivos_id": cp_mp_metas_objetivos_id},
        {"sheet_name": csi_mp_sheet_name, "metas_objetivos_id": csi_mp_metas_objetivos_id},
        {"sheet_name": nps_mp_sheet_name, "metas_objetivos_id": nps_mp_metas_objetivos_id},
        {"sheet_name": pp_mp_sheet_name, "metas_objetivos_id": pp_mp_metas_objetivos_id},
        {"sheet_name": ps_mp_sheet_name, "metas_objetivos_id": ps_mp_metas_objetivos_id}
    ])

    add_etl_data(mvjf_file_id, mvjf_system, mvjf_cod_empresa, [
        {"sheet_name": cp_mv_sheet_name, "metas_objetivos_id": cp_mv_metas_objetivos_id},
        {"sheet_name": csi_mv_sheet_name, "metas_objetivos_id": csi_mv_metas_objetivos_id},
        {"sheet_name": nps_mv_sheet_name, "metas_objetivos_id": nps_mv_metas_objetivos_id},
        {"sheet_name": vv_sheet_name, "metas_objetivos_id": vv_mv_metas_objetivos_id},
        {"sheet_name": at_mv_sheet_name, "metas_objetivos_id": at_mv_metas_objetivos_id},
        {"sheet_name": td_mv_sheet_name, "metas_objetivos_id": td_mv_metas_objetivos_id},
        {"sheet_name": ms_mv_sheet_name, "metas_objetivos_id": ms_mv_metas_objetivos_id}
    ])

    add_etl_data(mvbh_file_id, mvbh_system, mvbh_cod_empresa, [
        {"sheet_name": cp_mv_sheet_name, "metas_objetivos_id": cp_mv_metas_objetivos_id},
        {"sheet_name": csi_mv_sheet_name, "metas_objetivos_id": csi_mv_metas_objetivos_id},
        {"sheet_name": nps_mv_sheet_name, "metas_objetivos_id": nps_mv_metas_objetivos_id},
        {"sheet_name": vv_sheet_name, "metas_objetivos_id": vv_mv_metas_objetivos_id},
        {"sheet_name": at_mv_sheet_name, "metas_objetivos_id": at_mv_metas_objetivos_id},
        {"sheet_name": td_mv_sheet_name, "metas_objetivos_id": td_mv_metas_objetivos_id},
        {"sheet_name": ms_mv_sheet_name, "metas_objetivos_id": ms_mv_metas_objetivos_id}
    ])

    add_etl_data(pvba_file_id, pvba_system, pvba_cod_empresa, [
        {"sheet_name": cp_pv_sheet_name, "metas_objetivos_id": cp_pv_metas_objetivos_id},
        {"sheet_name": csi_pv_sheet_name, "metas_objetivos_id": csi_pv_metas_objetivos_id},
        {"sheet_name": ces_pv_sheet_name, "metas_objetivos_id": ces_pv_metas_objetivos_id},
        {"sheet_name": vv_pv_sheet_name, "metas_objetivos_id": vv_pv_metas_objetivos_id},
        {"sheet_name": cas_pv_sheet_name, "metas_objetivos_id": cas_pv_metas_objetivos_id},
        {"sheet_name": td_pv_sheet_name, "metas_objetivos_id": td_pv_metas_objetivos_id},
        {"sheet_name": ms_pv_sheet_name, "metas_objetivos_id": ms_pv_metas_objetivos_id}
    ])

    add_etl_data(pvbh_file_id, pvbh_system, pvbh_cod_empresa, [
        {"sheet_name": cp_pv_sheet_name, "metas_objetivos_id": cp_pv_metas_objetivos_id},
        {"sheet_name": csi_pv_sheet_name, "metas_objetivos_id": csi_pv_metas_objetivos_id},
        {"sheet_name": ces_pv_sheet_name, "metas_objetivos_id": ces_pv_metas_objetivos_id},
        {"sheet_name": vv_pv_sheet_name, "metas_objetivos_id": vv_pv_metas_objetivos_id},
        {"sheet_name": cas_pv_sheet_name, "metas_objetivos_id": cas_pv_metas_objetivos_id},
        {"sheet_name": td_pv_sheet_name, "metas_objetivos_id": td_pv_metas_objetivos_id},
        {"sheet_name": ms_pv_sheet_name, "metas_objetivos_id": ms_pv_metas_objetivos_id}
    ])

    add_etl_data(ppbh_file_id, ppbh_system, ppbh_cod_empresa, [
        {"sheet_name": cp_pp_sheet_name, "metas_objetivos_id": cp_pp_metas_objetivos_id},
        {"sheet_name": csi_pp_sheet_name, "metas_objetivos_id": csi_pp_metas_objetivos_id},
        {"sheet_name": ces_pp_sheet_name, "metas_objetivos_id": ces_pp_metas_objetivos_id},
        {"sheet_name": cap_pp_sheet_name, "metas_objetivos_id": cap_pp_metas_objetivos_id},
        {"sheet_name": pp_pp_sheet_name, "metas_objetivos_id": pp_pp_metas_objetivos_id},
        {"sheet_name": ps_pp_sheet_name, "metas_objetivos_id": ps_pp_metas_objetivos_id}
    ])

    return etl_data
        
def ETL(username, password, site_url, file_id, index):
    try:
        ctx_auth = AuthenticationContext(site_url)
        if ctx_auth.acquire_token_for_user(username, password):
            ctx = ClientContext(site_url, ctx_auth)
            file = ctx.web.get_file_by_id(file_id)
            ctx.load(file)
            ctx.execute_query()
            file_stream = BytesIO(file.read())
        else:
            raise Exception("Falha na autenticação")

        df_merged = pd.DataFrame({
            "metas_objetivos_id": [],
            "sistema_origem": [],
            "cod_empresa": [],
            "periodo": [],
            "valor": [],
            "tipo_registro": [],
            "meta_objetivo_subitem": []
        })

        partial_dfs = []  # Lista para armazenar DataFrames parciais

        for sheet_info in index.get(file_id, {}).get("sheets", []):
            sheet_name = sheet_info["sheet_name"]
            metas_objetivos_id = sheet_info["metas_objetivos_id"]
            sistema_origem = index[file_id]["system"]
            cod_empresa = index[file_id]["cod_empresa"]

            df = pd.read_excel(file_stream, sheet_name=sheet_name)

            def add_to_partial_dfs(column_name, tipo_registro):
                if column_name in df.columns:
                    partial_dfs.append(pd.DataFrame({
                        "metas_objetivos_id": metas_objetivos_id,
                        "sistema_origem": sistema_origem,
                        "cod_empresa": cod_empresa,
                        "periodo": df["periodo"],
                        "valor": df[column_name],
                        "tipo_registro": tipo_registro,
                        "meta_objetivo_subitem": df.get("meta_objetivo_subitem", pd.Series([None]*len(df)))
                    }))

            # Sem alterações na obtenção dos dados do sheet

            add_to_partial_dfs("local", "R")
            add_to_partial_dfs("realizado", "R")
            add_to_partial_dfs("respondidas", "RP")
            add_to_partial_dfs("enviadas", "EV")
            add_to_partial_dfs("objetivo", "O")
            add_to_partial_dfs("nacional", "N")
            add_to_partial_dfs("captado", "CP")
            add_to_partial_dfs("starclass", "O")

        df_merged = pd.concat(partial_dfs, ignore_index=True) if partial_dfs else pd.DataFrame()

        df_merged = df_merged[df_merged["periodo"].notnull()]
        df_merged["periodo"] = df_merged["periodo"].astype(int)

        return df_merged
    except Exception as e:
        print("Erro:", e)

def ETL_FILA_ESPERA(df, cod_empresa):
    # Verificar se a coluna "telefone" contém apenas valores de texto
    if df["telefone"].dtype == "object":
        # Truncate values in the "telefone" column to 20 characters
        df["telefone"] = df["telefone"].str[:20]

    df = pd.DataFrame({
        "sistema_origem": "NBS",
        "cod_empresa": cod_empresa,
        "cliente_cpf": None,
        "cliente_nome": df["cliente_nome"],
        "modelo_interesse": df["modelo_interesse"],
        "vendedor_nome": df["vendedor"],
        "data_registro": df["data_registro"],
        "data_baixa": df["data_baixa"],
        "telefone": df["telefone"],
        "e_mail": df["email"]
    })

    return df

def ETL_VENDAS_GWM(cod_empresa):
    if cod_empresa == 601:
        folder_id = gwm_bh_folder_id
        file_name_1 = gwm_bh_file_name_1
        file_name_2 = gwm_bh_file_name_2
    if cod_empresa == 602:
        folder_id = gwm_cg_folder_id
        file_name_1 = gwm_cg_file_name_1
        file_name_2 = gwm_cg_file_name_2

    ctx_auth = AuthenticationContext(SITE_URL)
    if ctx_auth.acquire_token_for_user(SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD):
        ctx = ClientContext(SITE_URL, ctx_auth)
        folder = ctx.web.get_folder_by_id(folder_id)
        file = folder.files.get_by_url(file_name_1)
        ctx.load(file)
        ctx.execute_query()
        file_stream = BytesIO(file.read())
        df = pd.read_excel(file_stream)

        file = folder.files.get_by_url(file_name_2)
        ctx.load(file)
        ctx.execute_query()
        file_stream = BytesIO(file.read())
        df1 = pd.read_excel(file_stream)

    merge = pd.merge(df, df1, how="left", left_on="Pedido - ID Cliente", right_on="Customer Order ID")
    merge = merge[merge['CHASSI DO VEICULO'] != 'CANCELADO']
    print(merge.columns)

    df = merge.query('Status == "FATURADO"')
    df["CHASSI DO VEICULO"] = df["CHASSI DO VEICULO"].str[-7:]
    df = pd.DataFrame({
            "DATA": df["Data NF"],
            "CLIENTE": df["Cliente"],
            "DOCCLI": None,
            "VALOR": df["Valor Total Veículo"],
            "CUSTO": None,
            "STATUS": df["Status"],
            "NOVO_USADO": "Novo",
            "EMPRESA_SYS": "NBS",
            "COD_EMPRESA": cod_empresa,
            "EMITIU_NOTA": " Veículo Faturado",
            "VENDEDOR": df["Vendedor"],
            "TIPO_FATURAMENTO": df["Tipo Pgto"],
            "ITEM": df["Marca"],
            "DESCRICAO_MODELO": df["Modelo"],
            "CHASSI": df["CHASSI DO VEICULO"],
            "PERIODOESTOQUE": None,
            "DGIRO": None,
            "TAB_REF": "Vendas"
        })

    df1 = merge[merge["Data Entrega para Cliente"].notnull()]
    df1["CHASSI DO VEICULO"] = df1["CHASSI DO VEICULO"].str[-7:]
    df1 = pd.DataFrame({
            "DATA": df1["Data Entrega para Cliente"],
            "CLIENTE": df1["Cliente"],
            "DOCCLI": None,
            "VALOR": df1["Valor Total Veículo"],
            "CUSTO": None,
            "STATUS": df1["Status"],
            "NOVO_USADO": "Novo",
            "EMPRESA_SYS": "NBS",
            "COD_EMPRESA": cod_empresa,
            "EMITIU_NOTA": " Veículo Faturado",
            "VENDEDOR": df1["Vendedor"],
            "TIPO_FATURAMENTO": df1["Tipo Pgto"],
            "ITEM": df1["Marca"],
            "DESCRICAO_MODELO": df1["Modelo"],
            "CHASSI": df1["CHASSI DO VEICULO"],
            "PERIODOESTOQUE": None,
            "DGIRO": None,
            "TAB_REF": "Veiculos Entregues"
        })

    df_merged = pd.concat([df, df1], ignore_index=True)

    return df_merged

if __name__ == "__main__":
    
    print(ETL_VENDAS_GWM(601))