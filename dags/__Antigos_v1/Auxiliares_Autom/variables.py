from __Antigos_v1.Auxiliares_Autom.passwords import DW_CORPORATIVO_PASSWORD, DEALER_PASSWORD, SHAREPOINT_PASSWORD, NBS_PASSWORD, QUIVER_PASSWORD, DW_BAMAQ_PASSWORD

# Data inicial de inclusão dos regsitros nas querys
date_start = '2023-01-01'

# DW Corporativo
DW_CORPORATIVO_USERNAME = 'master'
DW_CORPORATIVO_PASSWORD = DW_CORPORATIVO_PASSWORD
DW_CORPORATIVO_DBNAME = 'postgres'
DW_CORPORATIVO_HOST = '***********'
DW_CORPORATIVO_PORT = '5432'
# Schema para a criação e inserção de registros do ETL
SCHEMA_CRIACAO_INSERT = 'staging'

# DealerNet
DEALER_USERNAME = 'iebt_daniel_laranjo'
DEALER_PASSWORD = DEALER_PASSWORD
DEALER_DBNAME = 'dbDealernetWF'
DEALER_HOST =  '***********'
DEALER_PORT = '65002'

# NBS
NBS_USERNAME = 'iebt_daniel_laranjo'
NBS_PASSWORD = NBS_PASSWORD
NBS_SERVICE_NAME = 'nbs.privatesubnet.natvcn.oraclevcn.com'
NBS_HOST =  '***********'
NBS_PORT = '1521'

# Quiver
QUIVER_USERNAME = 'bq_dwcorporativo_u'
QUIVER_PASSWORD = QUIVER_PASSWORD
QUIVER_DBNAME = 'dbQuiver_CGF'
QUIVER_HOST =  '***********'
QUIVER_PORT = '50666'

# Dw_BAMAQ (Consórcio)
DW_BAMAQ_USERNAME = 'bq_dwcorporativo_u'
DW_BAMAQ_PASSWORD = DW_BAMAQ_PASSWORD
DW_BAMAQ_DBNAME = 'DW_BAMAQ'
DW_BAMAQ_HOST =  '*********'
DW_BAMAQ_PORT = '1433'

# Sharepoint
SHAREPOINT_USERNAME = '<EMAIL>'
SHAREPOINT_PASSWORD = SHAREPOINT_PASSWORD
SITE_URL = 'https://bqmm.sharepoint.com/sites/business_intelligence/'

#### Variaveis com o final "file_id" representam o id do arquivo excel no sharepoint, podendo ser adquirido a partir de sua url
#### Variaveis com o final "system" representam o sistema da empresa mencionada na tabela de dimensão empresa do dw corporativo
#### Variaveis com o final "cod_empresa" representam o codigo da empresa mencionada na tabela de dimensão empresa do dw corporativo
#### Variaveis com o final "sheet_name" representam a localização da aba para determinada métrica (Começa do 0)
#### Variaveis com o final "metas_objetivos_id" representam o id da métrica no DW Corporativo

### BAMAQ_PREMIUM
bp_file_id = 'F3E39FBC-A90D-4806-BA09-AA3FDF66190F'
bp_system = 'DEALERNETWF'
bp_cod_empresa = '23'
# PESQUISA SATISFAÇÃO
ps_bp_sheet_name= 0
ps_bp_metas_objetivos_id= 5
# NPS
nps_bp_sheet_name= 1
nps_bp_metas_objetivos_id= 12
# AVALIAÇÃO DE VEÍCULO
av_bp_sheet_name= 2
av_bp_metas_objetivos_id= 14
# LEADS GERADOS
lg_bp_sheet_name= 4
lg_bp_metas_objetivos_id= 21
# ESTOQUE SEMINOVOS
et_bp_sheet_name= 5
et_bp_metas_objetivos_id= 15
# VENDA SEMINOVOS
vs_bp_sheet_name= 6
vs_bp_metas_objetivos_id= 16
 
### MERCEDES_POSVENDAS
mpbh_file_id = '2E36E189-C520-45B7-AF55-0A5C96F57D48'
mpbh_system = 'NBS'
mpbh_cod_empresa = '102'
## JF
mpjf_file_id = 'BDB78F9D-3664-47D2-A992-7EBD7475D7AD'
mpjf_system = 'NBS'
mpjf_cod_empresa = '103'
# CONVERSÃO DE PESQUISAS
cp_mp_sheet_name= 0
cp_mp_metas_objetivos_id= 6
# CSI
csi_mp_sheet_name= 1
csi_mp_metas_objetivos_id= 18
# NPS
nps_mp_sheet_name= 2
nps_mp_metas_objetivos_id= 17
# PERFORMANCE PEÇAS
pp_mp_sheet_name= 3
pp_mp_metas_objetivos_id= 3
# PERFORMANCE SERVIÇOS
ps_mp_sheet_name= 4
ps_mp_metas_objetivos_id= 4

### MERCEDES_VENDAS
mvbh_file_id = '8F02D27D-55EC-4146-BE59-DF73D3FA925C'
mvbh_system = 'NBS'
mvbh_cod_empresa = '101'
## JF
mvjf_file_id = '997C421F-4A19-495B-B7C8-D7798EA85D9B'
mvjf_system = 'NBS'
mvjf_cod_empresa = '103'
# CONVERSÃO DE PESQUISAS
cp_mv_sheet_name= 0
cp_mv_metas_objetivos_id= 5
# CSI  
csi_mv_sheet_name= 1
csi_mv_metas_objetivos_id= 7
# NPS  
nps_mv_sheet_name= 2
nps_mv_metas_objetivos_id= 12
# VENDA VEICULOS NOVOS
vv_sheet_name= 3
vv_mv_metas_objetivos_id= 1
# ATACADO  
at_mv_sheet_name= 4
at_mv_metas_objetivos_id= 13
# TEST DRIVE  
td_mv_sheet_name= 5
td_mv_metas_objetivos_id= 10
# MARKET SHARE  
ms_mv_sheet_name= 6
ms_mv_metas_objetivos_id= 11
    
### PORSCHE_VENDAS
##MG
pvbh_file_id = '9237123E-2B2D-40BA-AD4A-726503CB883C'
pvbh_system = 'NBS'
pvbh_cod_empresa = '401'
##BA
pvba_file_id = '815BCD7E-55FC-4017-8EA2-343BF31D4CE6'
pvba_system = 'NBS'
pvba_cod_empresa = '403'
# CONVERSÃO DE PESQUISAS
cp_pv_sheet_name= 0
cp_pv_metas_objetivos_id= 5
# CSI  
csi_pv_sheet_name= 1
csi_pv_metas_objetivos_id= 7
# CES
ces_pv_sheet_name= 2
ces_pv_metas_objetivos_id= 8
# VENDA VEICULOS NOVOS
vv_pv_sheet_name= 3
vv_pv_metas_objetivos_id= 1
# CONTATO APÓS SERVIÇO  
cas_pv_sheet_name= 4
cas_pv_metas_objetivos_id= 9
# TEST DRIVE  
td_pv_sheet_name= 5
td_pv_metas_objetivos_id= 10
# MARKET SHARE  
ms_pv_sheet_name= 6
ms_pv_metas_objetivos_id= 11

### PORSCHE_POSVENDAS
ppbh_file_id = '36DA166B-3E49-4386-8D9B-E366B1189770'
ppbh_system = 'NBS'
ppbh_cod_empresa = '401'
# CONVERSÃO DE PESQUISAS
cp_pp_metas_objetivos_id= 6
cp_pp_sheet_name= 0
# CSI
csi_pp_sheet_name= 1
csi_pp_metas_objetivos_id= 18
# CES
ces_pp_sheet_name= 2
ces_pp_metas_objetivos_id= 22
# CONTATO APÓS SERVIÇO
cap_pp_sheet_name= 3
cap_pp_metas_objetivos_id= 19
# PERFORMANCE PEÇAS
pp_pp_sheet_name= 4
pp_pp_metas_objetivos_id= 3
# PERFORMANCE SERVIÇOS
ps_pp_sheet_name= 5
ps_pp_metas_objetivos_id= 4

### COCKPIT
ck_pv_file_id = '4422E9E0-7DF3-41D0-A272-16E214A40172'

### FILA_ESPERA
## PORSCHE
fe_pv_file_id = 'C4ECFBBB-A5E1-4BE8-B371-070E0AC543A8'
## MERCEDES
# BH
fe_mvbh_file_id = 'F6C6859D-5105-4C62-96DD-357C3991C21A'
# JF
fe_mvjf_file_id = '809577AC-1D1C-4344-9E37-337B73947462'

### FERIADOS
feriados_file_id = '716A2628-B480-4ACA-8DAD-17A7650E8D50'

### VENDEDORES DEALER 
vendedores_file_id = 'EEE1467D-E0D4-4BAC-B6CF-1154263BCBED'
vendedores_sheet_name= 'Vendedores'

### VENDAS GWM
##BH
gwm_bh_folder_id = '76208bf0-dd7a-41af-99d5-0bc548f0298b'
gwm_bh_file_name_1 = 'Vendas_GWM_MG.xlsx'
gwm_bh_file_name_2 = 'Clientes_GWM_MG.xlsx'
##CG
gwm_cg_folder_id = '60cbebb6-dbd1-4847-ad51-354858d923d7'
gwm_cg_file_name_1 = 'Vendas_GWM_MS.xlsx'
gwm_cg_file_name_2 = 'Clientes_GWM_MS.xlsx'

### OBJETIVOS CONSORCIO
consorcio_file_name = 'd5f3c063-7d93-4335-8b7f-6d18edb9c4da'