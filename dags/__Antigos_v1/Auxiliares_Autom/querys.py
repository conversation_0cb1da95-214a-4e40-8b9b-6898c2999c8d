from __Antigos_v1.Auxiliares_Autom.variables import SCHEMA_CRIACAO_INSERT, date_start

query_criacao_tabelas = f"""
    DROP TABLE IF EXISTS {SCHEMA_CRIACAO_INSERT}.f_Vendas;
        CREATE TABLE {SCHEMA_CRIACAO_INSERT}.f_Vendas (
            DATA DATE,
            CLIENTE VARCHAR(255),
            DOCCLI VARCHAR(255),
            VALOR FLOAT,
            CUSTO FLOAT,
            STATUS VARCHAR(50),
            NOVO_USADO VARCHAR(50),
            EMPRESA_SYS VARCHAR(50),
            COD_EMPRESA INT,
            EMITIU_NOTA VARCHAR(50),
            VENDEDOR VARCHAR(255),
            TIPO_FATURAMENTO VARCHAR(255),
            ITEM VARCHAR(255),
            DESCRICAO_MODELO VARCHAR(255),
            CHASSI VARCHAR(50),
            PERIODOESTOQUE VARCHAR(50),
            D<PERSON><PERSON> FLOAT,
            TABREF VARCHAR(50)
        );
    GRANT ALL ON {SCHEMA_CRIACAO_INSERT}.f_Vendas TO bq_dwcorporativo_u;

    DROP TABLE IF EXISTS {SCHEMA_CRIACAO_INSERT}.f_PosVendas;
        CREATE TABLE {SCHEMA_CRIACAO_INSERT}.f_PosVendas (
            DATA DATE,
            VALOR FLOAT,
            EMPRESA_SYS VARCHAR(50),
            COD_EMPRESA INT,
            GRUPO VARCHAR(255),
            REF VARCHAR(50)
        );
    GRANT ALL ON {SCHEMA_CRIACAO_INSERT}.f_PosVendas TO bq_dwcorporativo_u;

    DROP TABLE IF EXISTS {SCHEMA_CRIACAO_INSERT}.f_Seguros;
        CREATE TABLE {SCHEMA_CRIACAO_INSERT}.f_Seguros (
            CHASSI VARCHAR(50)
        );
    GRANT ALL ON {SCHEMA_CRIACAO_INSERT}.f_Seguros TO bq_dwcorporativo_u;
            
    DROP TABLE IF EXISTS {SCHEMA_CRIACAO_INSERT}.dim_Feriados;
        CREATE TABLE {SCHEMA_CRIACAO_INSERT}.dim_Feriados (
            DATA DATE,
            DESCRICAO VARCHAR(50),
            ESCOPO VARCHAR(50)
        );
    GRANT ALL ON {SCHEMA_CRIACAO_INSERT}.dim_Feriados TO bq_dwcorporativo_u;

    TRUNCATE {SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos;

    TRUNCATE {SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores;

    TRUNCATE {SCHEMA_CRIACAO_INSERT}.bqtb_cockpit_porsche;

    DROP TABLE IF EXISTS {SCHEMA_CRIACAO_INSERT}.bqtb_fila_espera;
    CREATE TABLE {SCHEMA_CRIACAO_INSERT}.bqtb_fila_espera
    (
        sistema_origem character varying(15) COLLATE pg_catalog."default",
        cod_empresa integer,
        cliente_cpf character varying(15) COLLATE pg_catalog."default",
        cliente_nome character varying(255) COLLATE pg_catalog."default",
        modelo_interesse character varying(50) COLLATE pg_catalog."default",
        vendedor_nome character varying(50) COLLATE pg_catalog."default",
        data_registro date,
        data_baixa date,
        telefone character varying(20) COLLATE pg_catalog."default",
        e_mail character varying(255) COLLATE pg_catalog."default"
    );
    GRANT ALL ON {SCHEMA_CRIACAO_INSERT}.bqtb_fila_espera TO bq_dwcorporativo_u;

    DELETE FROM {SCHEMA_CRIACAO_INSERT}.f_Posicao_Estoque
    WHERE EXTRACT(MONTH FROM data) = EXTRACT(MONTH FROM CURRENT_DATE) 
    AND EXTRACT(YEAR FROM data) = EXTRACT(YEAR FROM CURRENT_DATE);

"""

date = date_start

query_metricas = """
SELECT id, nome_exibicao, descricao
	FROM dbdwcorporativo.bqtb_metas_objetivos;
"""

query_seguros = """
SELECT
  IIF(i.chassi = '',capacidade,CHASSI) as CHASSI
FROM TABELA_DOCSPARCSPREM PP
INNER JOIN Tabela_Documentos d
ON
PP.Documento = d.Documento and PP.Alteracao = d.Alteracao
LEFT JOIN TABELA_CLIENTES C 
ON 
D.CLIENTE = C.CLIENTE
LEFT JOIN TABELA_SEGURADORAS S 
ON D.SEGURADORA = S.SEGURADORA
LEFT JOIN TABELA_PRODUTOS P 
ON P.PRODUTO = D.PRODUTO
LEFT JOIN TABELA_TIPOSDOCEMISS TD 
ON
TD.TIPO_DOCUMENTO = D.TIPO_DOCUMENTO
LEFT JOIN TABELA_SUBTIPOSDOC TSD
ON
D.SUB_TIPO = TSD.SUB_TIPO
LEFT JOIN TABELA_DIVISOES DV 
ON C.DIVISAO = DV.DIVISAO
LEFT JOIN TABELA_GRUPOSHIERARQ GH 
ON 
GH.Grupo_hierarquico = C.Grupo_hierarquico
LEFT JOIN Tabela_Ramos R
ON
R.Ramo = P.Ramo
INNER JOIN Tabela_DocsItens i
ON d.DOCUMENTO = i.DOCUMENTO AND d.ALTERACAO = i.ALTERACAO
WHERE PP.DATA_PAGAMENTO IS NOT NULL     
AND R.Descricao = 'AUTOMÓVEL - CASCO'
AND CAST(D.DATA_EMISSAO AS DATE) >= '2021-10-01'
AND C.Cgc_cpf NOT IN ('')

"""

query_consorcio = f"""
select
       CONVERT(DATE, DATEADD(MONTH, DATEDIFF(MONTH, 0, v.dt_adesao), 0)) as DATA,
       NULL AS CLIENTE,
       NULL AS DOCCLI,
       sum(v.vl_bem_adesao) as VALOR,
       NULL AS CUSTO,
       NULL AS STATUS,
        case nm_ponto_venda
             when 'BMQ AUTOMOVEIS PORSCHE SALVADO' then 'Novo'
             when 'BAMAQ  AUTOMOVEIS' then 'Novo'
             when 'BAMAQ AUTO JF' then 'Novo'
             when 'BAMAQ PREMIUM LTDA' then 'Seminovo'
             when 'GWM BH' then 'Novo'
             when 'GWM CAMPO GRANDE' then 'Novo'
             when 'DELTA' then null
             when 'BMQ AUTOMOVEIS LTDA PORSCHE BH' then 'Novo'
             else null
       end as NOVO_USADO,
       case nm_ponto_venda
             when 'BMQ AUTOMOVEIS PORSCHE SALVADO' then 'NBS'
             when 'BAMAQ  AUTOMOVEIS' then 'NBS'
             when 'BAMAQ AUTO JF' then 'NBS'
             when 'BAMAQ PREMIUM LTDA' then 'DEALERNETWF'
             when 'GWM BH' then 'NBS'
             when 'GWM CAMPO GRANDE' then 'NBS'
             when 'DELTA' then null
             when 'BMQ AUTOMOVEIS LTDA PORSCHE BH' then 'NBS'
             else null
       end as EMPRESA_SYS,
       case nm_ponto_venda
             when 'BMQ AUTOMOVEIS PORSCHE SALVADO' then '403'
             when 'BAMAQ  AUTOMOVEIS' then '101'
             when 'BAMAQ AUTO JF' then '103'
             when 'BAMAQ PREMIUM LTDA' then '23'
             when 'GWM BH' then '601'
             when 'GWM CAMPO GRANDE' then '602'
             when 'DELTA' then null
             when 'BMQ AUTOMOVEIS LTDA PORSCHE BH' then '401'
             else null
       end as COD_EMPRESA,
       NULL AS EMITIU_NOTA,
       NULL AS VENDEDOR,
       NULL AS TIPO_FATURAMENTO,
       NULL AS ITEM,
       NULL AS DESCRICAO_MODELO,
       NULL AS CHASSI,
       NULL AS PERIODOESTOQUE,
       NULL AS DGIRO,
       'Consorcio' as TABREF
FROM vendas (NOLOCK) v
LEFT OUTER JOIN ponto_vendas (NOLOCK) pv ON pv.id_ponto_venda = v.id_ponto_venda
LEFT OUTER JOIN subcanal_venda_ponto_vendas (NOLOCK) scvpv ON scvpv.id_ponto_venda = pv.id_ponto_venda
LEFT OUTER JOIN subcanal_venda (NOLOCK) sv ON sv.id_subcanal_venda = scvpv.id_subcanal_venda
WHERE sv.id_subcanal_venda = 2 
        and v.dt_adesao >= '{date}'
group by
       CONVERT(DATE, DATEADD(MONTH, DATEDIFF(MONTH, 0, v.dt_adesao), 0)),
       pv.nm_ponto_venda
"""

query_nbs_estoque = """

SELECT
  --  VE.DATA_ENTRADA DATA,
    SYSDATE DATA,
    C.NOME AS CLIENTE,  
    CASE WHEN CD.CPF IS NULL THEN CD.CGC
         WHEN CD.CGC IS NULL THEN CD.CPF 
    END AS DOCCLI,
    VE.TOTAL_NOTA_FABRICA VALOR,  
    VE.CUSTO_TOTAL_FINAL CUSTO,
    VE.STATUS,
    CASE VE.NOVO_USADO
    WHEN 'N' THEN 'Novo'
    WHEN 'U' THEN 'Seminovo'
    WHEN 'P' THEN 'Seminovo'
    WHEN 'C' THEN 'Seminovo'
    ELSE VE.NOVO_USADO
    END AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
	E.COD_EMPRESA,
    CASE VE.VEICULO_PAGO
    WHEN 'S' THEN 'Pago'
    ELSE 'A pagar'
    END AS EMITIU_NOTA,
    NULL AS VENDEDOR,
    CASE VE.NOVO_USADO
    WHEN 'P' THEN 'Imobilizado'
    WHEN 'C' THEN 'Consignado'
    ELSE 'Estoque Próprio'
    END AS TIPO_FATURAMENTO,
    P.DESCRICAO_PRODUTO AS ITEM,
    PM.DESCRICAO_MODELO,
    SUBSTR(VE.CHASSI_COMPLETO, LENGTH(VE.CHASSI_COMPLETO) - 3) AS CHASSI,
    CASE 
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 0 AND 30 THEN '0 - 30 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) > 90 THEN 'Mais de 90 dias'
        ELSE NULL
  END AS PeriodoEstoque,
  (SYSDATE - VE.DATA_ENTRADA) AS DGIRO,
  'Estoque' as TABREF
FROM 
    VEICULOS VE  
    LEFT JOIN CLIENTES C ON C.COD_CLIENTE = VE.COD_CLIENTE
    LEFT JOIN CLIENTE_DIVERSO CD ON CD.COD_CLIENTE = C.COD_CLIENTE
    LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = VE.COD_EMPRESA
    LEFT JOIN PRODUTOS P ON P.COD_PRODUTO  = VE.COD_PRODUTO
    LEFT JOIN PRODUTOS_MODELOS PM ON PM.COD_PRODUTO = VE.COD_PRODUTO AND PM.COD_MODELO = VE.COD_MODELO
WHERE 
    VE.STATUS = 'E' AND
    not (VE.NOVO_USADO = 'C' AND VE.CONSIGNATO = 'N')
    
UNION ALL

SELECT
    SYSDATE DATA,
 --   VE.DATA_ENTRADA DATA,
    C.NOME AS CLIENTE,  
    CASE WHEN CD.CPF IS NULL THEN CD.CGC
         WHEN CD.CGC IS NULL THEN CD.CPF 
    END AS DOCCLI,
    VE.TOTAL_NOTA_FABRICA VALOR,  
    VE.CUSTO_TOTAL_FINAL CUSTO,
    VE.STATUS,
    'Seminovo' AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
	E.COD_EMPRESA,
    CASE VE.VEICULO_PAGO
    WHEN 'S' THEN 'Pago'
    ELSE 'A pagar'
    END AS EMITIU_NOTA,
    NULL AS VENDEDOR,
    'Imobilizado' AS TIPO_FATURAMENTO,
    P.DESCRICAO_PRODUTO AS ITEM,
    PM.DESCRICAO_MODELO,
    SUBSTR(VE.CHASSI_COMPLETO, LENGTH(VE.CHASSI_COMPLETO) - 3) AS CHASSI,
    CASE 
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) BETWEEN 0 AND 30 THEN '0 - 30 dias'
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) > 90 THEN 'Mais de 90 dias'
        ELSE NULL
  END AS PeriodoEstoque,
  ((SYSDATE - VE.DATA_ENTRADA) - 180) AS DGIRO,
  'Estoque' as TABREF
FROM 
    VEICULOS VE  
    LEFT JOIN CLIENTES C ON C.COD_CLIENTE = VE.COD_CLIENTE
    LEFT JOIN CLIENTE_DIVERSO CD ON CD.COD_CLIENTE = C.COD_CLIENTE
    LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = VE.COD_EMPRESA
    LEFT JOIN PRODUTOS P ON P.COD_PRODUTO  = VE.COD_PRODUTO
    LEFT JOIN PRODUTOS_MODELOS PM ON PM.COD_PRODUTO = VE.COD_PRODUTO AND PM.COD_MODELO = VE.COD_MODELO
WHERE 
    VE.STATUS = 'I'
    AND (SYSDATE - VE.DATA_ENTRADA) >= 180 
    AND VE.CHASSI_COMPLETO NOT IN (
        SELECT a.chassi_completo
        FROM veiculos a
        JOIN vendas v ON a.cod_produto = v.cod_produto
                      AND a.cod_modelo = v.cod_modelo
                      AND a.chassi_resumido = v.chassi_resumido
                      AND v.cod_operacao(+) NOT IN (75, 72, 32, 78, 22, 28, 33, 53, 54, 62, 68, 71, 72, 75, 78, 87, 89, 92, 93, 100, 101, 102, 103, 104, 105, 111, 110, 115, 113)
                      AND v.status(+) = 0
        JOIN cliente_diverso cli ON a.cod_cliente = cli.cod_cliente
        JOIN produtos_modelos b ON a.cod_produto = b.cod_produto AND a.cod_modelo = b.cod_modelo
        WHERE a.status = 'V'
    )
    and ve.chassi_completo not in ('970WF4AW8LM016218', '970WF4AW9LM015790')
"""

query_nbs_vendas = f"""
SELECT
    VE.DATA_VENDA DATA,
    C.NOME AS CLIENTE,  
    CASE WHEN CD.CPF IS NULL THEN CD.CGC
         WHEN CD.CGC IS NULL THEN CD.CPF 
    END AS DOCCLI,
    (VE.valor_vendido - VE.desconto_incondicional) VALOR,  
    CASE VE.NOVO_USADO
    WHEN 'P' THEN (VE.valor_vendido - VE.desconto_incondicional) * 0.96
    ELSE VE.CUSTO_TOTAL_FINAL 
    END AS CUSTO,
    VE.STATUS,
    CASE VE.NOVO_USADO
    WHEN 'N' THEN 'Novo'
    WHEN 'U' THEN 'Seminovo'
    WHEN 'P' THEN 'Seminovo'
    WHEN 'C' THEN 'Seminovo'
    ELSE VE.NOVO_USADO
    END AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
	E.COD_EMPRESA,
     ' Veículo Faturado' as EMITIU_NOTA,
    CASE
    WHEN EU.NOME_COMPLETO LIKE '%ADM%' and VE.NOVO_USADO <> 'N' THEN 'REPASSE'
    ELSE EU.NOME_COMPLETO 
    END AS VENDEDOR,
    CASE VE.NOVO_USADO
    WHEN 'P' THEN 'Imobilizado'
    WHEN 'C' THEN 'Consignado'
    ELSE 'Estoque Próprio'
    END AS TIPO_FATURAMENTO,
    P.DESCRICAO_PRODUTO AS ITEM,
    PM.DESCRICAO_MODELO,
    VE.CHASSI_COMPLETO AS CHASSI,
    CASE 
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 0 AND 30 THEN '0 - 30 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) > 90 THEN 'Mais de 90 dias'
        ELSE NULL
  END AS PeriodoEstoque,
  (SYSDATE - VE.DATA_ENTRADA) AS DGIRO,
  'Vendas' as TABREF
FROM 
    VEICULOS VE  
    LEFT JOIN CLIENTES C ON C.COD_CLIENTE = VE.COD_CLIENTE
    LEFT JOIN CLIENTE_DIVERSO CD ON CD.COD_CLIENTE = C.COD_CLIENTE
    LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = VE.COD_EMPRESA_VENDEDORA
    LEFT JOIN EMPRESAS E1 ON E1.COD_EMPRESA = VE.COD_EMPRESA
    LEFT JOIN EMPRESAS_USUARIOS EU ON EU.NOME = VE.VENDEDOR
    LEFT JOIN PRODUTOS P ON P.COD_PRODUTO  = VE.COD_PRODUTO
    LEFT JOIN PRODUTOS_MODELOS PM ON PM.COD_PRODUTO = VE.COD_PRODUTO AND PM.COD_MODELO = VE.COD_MODELO
WHERE 
    VE.DATA_VENDA >= TO_DATE('{date}', 'YYYY-MM-DD')
    AND VE.STATUS = 'V'
    AND (E.COD_EMPRESA LIKE '1%' OR E.COD_EMPRESA LIKE '4%' or E.COD_EMPRESA LIKE '6%')
    AND (
        (ve.NOVO_USADO = 'N' AND ve.extra = 'N') OR
        (ve.NOVO_USADO = 'U' AND nvl(ve.extra,'.') NOT IN ('0', 'F', 'X') AND nvl(ve.consignato,'N') = 'N' AND C.NOME <> 'BAMAQ PREMIUM LTDA') OR
        (ve.NOVO_USADO = 'C' AND nvl(ve.extra,'.') NOT IN ('F', 'X') AND nvl(ve.consignato,'N') = 'S' AND C.NOME <> 'BAMAQ PREMIUM LTDA') OR
        (ve.NOVO_USADO = 'N' AND ve.internet = 'I') OR
        (ve.NOVO_USADO = 'N' AND ve.internet = 'F') OR
        (nvl(ve.extra,'.') = 'X')
    )

UNION ALL

SELECT
  --  VE.DATA_ENTRADA DATA,
    SYSDATE DATA,
    C.NOME AS CLIENTE,  
    CASE WHEN CD.CPF IS NULL THEN CD.CGC
         WHEN CD.CGC IS NULL THEN CD.CPF 
    END AS DOCCLI,
    VE.TOTAL_NOTA_FABRICA VALOR,  
    VE.CUSTO_TOTAL_FINAL CUSTO,
    VE.STATUS,
    CASE VE.NOVO_USADO
    WHEN 'N' THEN 'Novo'
    WHEN 'U' THEN 'Seminovo'
    WHEN 'P' THEN 'Seminovo'
    WHEN 'C' THEN 'Seminovo'
    ELSE VE.NOVO_USADO
    END AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
	E.COD_EMPRESA,
    CASE VE.VEICULO_PAGO
    WHEN 'S' THEN 'Pago'
    ELSE 'A pagar'
    END AS EMITIU_NOTA,
    NULL AS VENDEDOR,
    CASE VE.NOVO_USADO
    WHEN 'P' THEN 'Imobilizado'
    WHEN 'C' THEN 'Consignado'
    ELSE 'Estoque Próprio'
    END AS TIPO_FATURAMENTO,
    P.DESCRICAO_PRODUTO AS ITEM,
    PM.DESCRICAO_MODELO,
    VE.CHASSI_COMPLETO AS CHASSI,
    CASE 
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 0 AND 30 THEN '0 - 30 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
        WHEN (SYSDATE - VE.DATA_ENTRADA) > 90 THEN 'Mais de 90 dias'
        ELSE NULL
  END AS PeriodoEstoque,
  (SYSDATE - VE.DATA_ENTRADA) AS DGIRO,
  'Estoque' as TABREF
FROM 
    VEICULOS VE  
    LEFT JOIN CLIENTES C ON C.COD_CLIENTE = VE.COD_CLIENTE
    LEFT JOIN CLIENTE_DIVERSO CD ON CD.COD_CLIENTE = C.COD_CLIENTE
    LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = VE.COD_EMPRESA
    LEFT JOIN PRODUTOS P ON P.COD_PRODUTO  = VE.COD_PRODUTO
    LEFT JOIN PRODUTOS_MODELOS PM ON PM.COD_PRODUTO = VE.COD_PRODUTO AND PM.COD_MODELO = VE.COD_MODELO
WHERE 
    VE.STATUS = 'E' AND
    not (VE.NOVO_USADO = 'C' AND VE.CONSIGNATO = 'N')

    
UNION ALL

SELECT
    SYSDATE DATA,
 --   VE.DATA_ENTRADA DATA,
    C.NOME AS CLIENTE,  
    CASE WHEN CD.CPF IS NULL THEN CD.CGC
         WHEN CD.CGC IS NULL THEN CD.CPF 
    END AS DOCCLI,
    VE.TOTAL_NOTA_FABRICA VALOR,  
    VE.CUSTO_TOTAL_FINAL CUSTO,
    VE.STATUS,
    'Seminovo' AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
	E.COD_EMPRESA,
    CASE VE.VEICULO_PAGO
    WHEN 'S' THEN 'Pago'
    ELSE 'A pagar'
    END AS EMITIU_NOTA,
    NULL AS VENDEDOR,
    'Imobilizado' AS TIPO_FATURAMENTO,
    P.DESCRICAO_PRODUTO AS ITEM,
    PM.DESCRICAO_MODELO,
    VE.CHASSI_COMPLETO AS CHASSI,
    CASE 
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) BETWEEN 0 AND 30 THEN '0 - 30 dias'
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
        WHEN ((SYSDATE - VE.DATA_ENTRADA) - 180) > 90 THEN 'Mais de 90 dias'
        ELSE NULL
  END AS PeriodoEstoque,
  ((SYSDATE - VE.DATA_ENTRADA) - 180) AS DGIRO,
  'Estoque' as TABREF
FROM 
    VEICULOS VE  
    LEFT JOIN CLIENTES C ON C.COD_CLIENTE = VE.COD_CLIENTE
    LEFT JOIN CLIENTE_DIVERSO CD ON CD.COD_CLIENTE = C.COD_CLIENTE
    LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = VE.COD_EMPRESA
    LEFT JOIN PRODUTOS P ON P.COD_PRODUTO  = VE.COD_PRODUTO
    LEFT JOIN PRODUTOS_MODELOS PM ON PM.COD_PRODUTO = VE.COD_PRODUTO AND PM.COD_MODELO = VE.COD_MODELO
WHERE 
    VE.STATUS = 'I'
    AND (SYSDATE - VE.DATA_ENTRADA) >= 180    
UNION ALL

select
    ev_agendados.data_baixa DATA,
    NULL AS CLIENTE,  
    NULL AS DOCCLI,
    NULL AS VALOR,  
    NULL AS CUSTO,
    null as STATUS,
    CASE veiculos.NOVO_USADO
    WHEN 'N' THEN 'Novo'
    WHEN 'U' THEN 'Seminovo'
    WHEN 'P' THEN 'Seminovo'
    WHEN 'C' THEN 'Seminovo'
    ELSE veiculos.NOVO_USADO
    END AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
	ev_agendados.cod_empresa_entrega,
    ' Veículo Faturado' AS EMITIU_NOTA,
    vendedor.NOME_COMPLETO AS VENDEDOR,
    'Estoque Próprio' AS TIPO_FATURAMENTO,
    produtos.DESCRICAO_PRODUTO AS ITEM,
    descricao_modelo,
    SUBSTR(veiculos.CHASSI_COMPLETO, -7) AS CHASSI,
    NULL AS PeriodoEstoque,
  null AS DGIRO,
  'Veiculos Entregues' as TABREF
from
    ev_agendados,
    veiculos,
    produtos,
    produtos_modelos,
    PARM_SYS2 P,
    empresas_usuarios vendedor
where
    ev_agendados.cod_produto         = veiculos.cod_produto
    and ev_agendados.cod_modelo          = veiculos.cod_modelo
    and ev_agendados.chassi_resumido     = veiculos.chassi_resumido
    and veiculos.vendedor = vendedor.nome(+)
    and veiculos.status in ('E', 'V')
    AND ev_agendados.COD_EMPRESA = P.COD_EMPRESA
    and ev_agendados.cod_produto         = produtos.cod_produto(+)
    and ev_agendados.cod_produto         = produtos_modelos.cod_produto(+)
    and ev_agendados.cod_modelo          = produtos_modelos.cod_modelo(+)
    and ev_agendados.status = 'E'
    and (ev_agendados.cod_empresa_entrega like '1%' or ev_agendados.cod_empresa_entrega like '4%' or ev_agendados.cod_empresa_entrega like '6%')
    AND ev_agendados.data_baixa >= TO_DATE('{date}', 'YYYY-MM-DD')
    AND (ev_agendados.cod_empresa_entrega LIKE '1%' OR ev_agendados.cod_empresa_entrega LIKE '4%')
"""

query_nbs_posvendas = f"""
SELECT 
    v.emissao AS PRIMEIRO_DIA_DO_MES,
    SUM(vi.qtde) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END AS GRUPO,
    'Pecas_Vendas_VN' as REF
FROM VENDAS V
LEFT JOIN VENDA_ITENS VI ON VI.COD_EMPRESA = V.COD_EMPRESA AND VI.CONTROLE = V.CONTROLE AND VI.SERIE = V.SERIE
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = V.COD_EMPRESA
LEFT JOIN ITENS I ON I.COD_ITEM = VI.COD_ITEM 
LEFT JOIN ITENS_GRUPO_INTERNO IGI ON IGI.COD_GRUPO_INTERNO = I.COD_GRUPO_INTERNO
WHERE 
   (v.emissao >= TO_DATE('{date}', 'YYYY-MM-DD'))  
    AND (V.COD_EMPRESA LIKE '1%' OR V.COD_EMPRESA LIKE '4%' OR V.COD_EMPRESA LIKE '6%')
    AND V.STATUS <> '1'
    AND V.COD_OPERACAO IN (1, 2, 3, 13, 19, 26)
--    AND VI.SERIE = '1' 
    AND VI.QTDE > 0
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END,
    v.emissao
    
UNION ALL

SELECT * FROM (
    SELECT
            TBL.EMISSAO,
            SUM(TBL.QTDE * TBL.PRECO_UNITARIO) - SUM(TBL.total_descontos) - 
            SUM((TBL.aliq_icms_item/100) * TBL.BASE_ICMS) -
            SUM((TBL.aliq_pis/100) * TBL.base_pis) - SUM((TBL.aliq_cofins/100) * TBL.base_cofins) - 
            SUM(TBL.QTDE * TBL.PRECO_CONTABIL) AS TOTAL,
            'NBS' AS EMPRESA_SYS,
            TBL.COD_EMPRESA,
            CASE 
                WHEN TBL.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
                WHEN TBL.INTERNO = 'S' THEN 'Interno'
                WHEN (TBL.status_os = 1 or TBL.status_os = 5) THEN 'Oficina'
                ELSE 'Balcão'
            END AS GRUPO,
            'Pecas_Margem_Liquida' AS REF
        FROM (
            SELECT 
                vI.controle,
                vI.cod_empresa,
                vI.serie,
                VI.COD_ITEM,
                vi.QTDE, 
                vi.PRECO_UNITARIO,
                vi.aliq_icms_item,
                vi.BASE_ICMS,
                vi.aliq_pis,
                vi.base_pis,
                vi.aliq_cofins,
                vi.base_cofins,
                vi.PRECO_CONTABIL,
                vi.total_descontos,
                OS.STATUS_OS,
                OS_TIPOS.INTERNO,
                OS_TIPOS.DESCRICAO,
                V.EMISSAO
            FROM vendas v
            LEFT JOIN VENDA_ITENS VI ON VI.COD_EMPRESA = V.COD_EMPRESA AND VI.CONTROLE = V.CONTROLE AND vi.serie = v.serie
            LEFT JOIN os ON v.numero_os = os.numero_os and v.cod_empresa = os.cod_empresa
            LEFT JOIN os_tipos ON os.tipo = os_tipos.tipo
            WHERE 
                (v.COD_EMPRESA LIKE '1%' OR v.COD_EMPRESA LIKE '4%' OR v.COD_EMPRESA LIKE '6%')
                AND trunc(v.emissao) >= TO_DATE('{date}', 'YYYY-MM-DD')
                AND v.status <> '1'
                AND v.cod_operacao IN (1, 2, 3, 13, 19, 22, 26)
                AND v.cortesia <> 'S'
        ) TBL
        GROUP BY
            TBL.COD_EMPRESA,
            TBL.EMISSAO,
            CASE 
                WHEN TBL.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
                WHEN TBL.INTERNO = 'S' THEN 'Interno'
                WHEN (TBL.status_os = 1 or TBL.status_os = 5) THEN 'Oficina'
                ELSE 'Balcão'
            END
) WHERE TOTAL IS NOT NULL

UNION ALL

SELECT * FROM (
    SELECT
            TBL.EMISSAO,
            SUM(TBL.QTDE * TBL.PRECO_UNITARIO) - SUM(TBL.total_descontos) AS TOTAL,
            'NBS' AS EMPRESA_SYS,
            TBL.COD_EMPRESA,
            CASE 
                WHEN TBL.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
                WHEN TBL.INTERNO = 'S' THEN 'Interno'
                WHEN (TBL.status_os = 1 or TBL.status_os = 5) THEN 'Oficina'
                ELSE 'Balcão'
            END AS GRUPO,
            'Performance_Pecas_Liquida' AS REF
        FROM (
            SELECT 
                vI.controle,
                vI.cod_empresa,
                vI.serie,
                VI.COD_ITEM,
                vi.QTDE, 
                vi.PRECO_UNITARIO,
                vi.total_descontos,
                OS.STATUS_OS,
                OS_TIPOS.INTERNO,
                OS_TIPOS.DESCRICAO,
                V.EMISSAO
            FROM vendas v
            LEFT JOIN VENDA_ITENS VI ON VI.COD_EMPRESA = V.COD_EMPRESA AND VI.CONTROLE = V.CONTROLE AND vi.serie = v.serie
            LEFT JOIN os ON v.numero_os = os.numero_os and v.cod_empresa = os.cod_empresa
            LEFT JOIN os_tipos ON os.tipo = os_tipos.tipo
            WHERE 
                (v.COD_EMPRESA LIKE '1%' OR v.COD_EMPRESA LIKE '4%' OR v.COD_EMPRESA LIKE '6%')
                AND trunc(v.emissao) >= TO_DATE('{date}', 'YYYY-MM-DD')
                AND v.status <> '1'
                AND v.cod_operacao IN (1, 2, 3, 13, 19, 22, 26)
                AND v.cortesia <> 'S'
        ) TBL
        GROUP BY
            TBL.COD_EMPRESA,
            TBL.EMISSAO,
            CASE 
                WHEN TBL.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
                WHEN TBL.INTERNO = 'S' THEN 'Interno'
                WHEN (TBL.status_os = 1 or TBL.status_os = 5) THEN 'Oficina'
                ELSE 'Balcão'
            END
) WHERE TOTAL IS NOT NULL

UNION ALL 

SELECT * FROM (
    SELECT
            TBL.data_entrada,
            SUM(TBL.preco_liquido_final) AS TOTAL,
            'NBS' AS EMPRESA_SYS,
            TBL.COD_EMPRESA,
            CASE 
                WHEN TBL.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
                WHEN TBL.INTERNO = 'S' THEN 'Interno'
                WHEN (TBL.status_os = 1 or TBL.status_os = 5) THEN 'Oficina'
                ELSE 'Balcão'
            END AS GRUPO,
            'Performance_Pecas_Liquida_Desconto' AS REF
        FROM (
            SELECT DISTINCT
                vI.cod_controle,
                vI.cod_empresa,
                VI.COD_ITEM,
                vi.preco_liquido_final,
                OS.STATUS_OS,
                OS_TIPOS.INTERNO,
                OS_TIPOS.DESCRICAO,
                c.data_entrada
              from compra c 
              inner join vendas v on c.cod_empresa = v.cod_empresa and c.dev_venda_controle = v.controle and c.dev_venda_serie = v.serie                             
              left join compras_itens vi on c.cod_controle = vi.cod_controle and c.cod_empresa = vi.cod_empresa
            LEFT JOIN os ON v.numero_os = os.numero_os and v.cod_empresa = os.cod_empresa
            LEFT JOIN os_tipos ON os.tipo = os_tipos.tipo
             where c.cod_operacao = 7                                 
               and c.cod_status = 0                                   
               and (c.COD_EMPRESA LIKE '1%' OR c.COD_EMPRESA LIKE '4%' OR c.COD_EMPRESA LIKE '6%')                   
               and c.data_entrada >= TO_DATE('{date}', 'YYYY-MM-DD')                                            
               and v.cod_operacao in (1, 2, 3, 13, 19, 26)            
               and not exists (select 1 from compras_itens ci         
                                       where ci.dev_venda_controle= v.controle and ci.dev_venda_serie = v.serie and ci.cod_empresa = v.cod_empresa)
             and v.cortesia <> 'S' 
        ) TBL
        GROUP BY
            TBL.COD_EMPRESA,
            TBL.data_entrada,
            CASE 
                WHEN TBL.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
                WHEN TBL.INTERNO = 'S' THEN 'Interno'
                WHEN (TBL.status_os = 1 or TBL.status_os = 5) THEN 'Oficina'
                ELSE 'Balcão'
            END
) WHERE TOTAL IS NOT NULL

UNION ALL

 SELECT
    V.EMISSAO,
    SUM(v.total_servicos) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    V.COD_EMPRESA,
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
    ELSE 'Oficina/Garantia'
    END AS GRUPO,
    'Performance_Servicos_Liquida' AS REF
from vendas v
left join os on v.numero_os = os.numero_os and v.cod_empresa = os.cod_empresa
LEFT JOIN os_tipos ON os.tipo = os_tipos.tipo
where 
(v.COD_EMPRESA LIKE '1%' OR v.COD_EMPRESA LIKE '4%' OR v.COD_EMPRESA LIKE '6%')
 and trunc(v.emissao) >= TO_DATE('{date}', 'YYYY-MM-DD')
 and v.status <> '1'
 and v.cod_operacao in (1, 2, 3, 13, 19, 22, 26)
 and v.cortesia <> 'S'
 AND v.total_servicos > 0
GROUP BY
    V.COD_EMPRESA,
    V.EMISSAO,
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
    ELSE 'Oficina/Garantia'
    END
    
UNION ALL

SELECT
    a.DATA,
    SUM(a.VALOR_SERVICOS_BRUTO) AS TOTAL,
    a.EMPRESA_SYS,
    a.COD_EMPRESA,
    a.GRUPO,
    'Performance_Servicos_Liquida' AS REF
FROM
(SELECT DISTINCT
    OS.DATA_ENCERRADA AS DATA,
    'NBS' AS EMPRESA_SYS,
    OS.COD_EMPRESA,
    'Interno' AS GRUPO,
    OS.DATA_ENCERRADA,
    OS.NUMERO_OS,
    OS.NOME,
    OS.VALOR_ITENS_BRUTO,
    OS.VALOR_SERVICOS_BRUTO,
    OS.DESCONTOS_ITENS,
    OS.DESCONTOS_SERVICOS
from 
os
LEFT JOIN os_tipos ON os.tipo = os_tipos.tipo
LEFT JOIN VENDA_OS VOS ON OS.NUMERO_OS = VOS.NUMERO_OS AND OS.COD_EMPRESA = VOS.COD_EMPRESA
LEFT JOIN VENDAS V ON V.SERIE = VOS.SERIE AND V.CONTROLE = VOS.CONTROLE AND V.COD_EMPRESA = VOS.COD_EMPRESA
where 
(OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
AND trunc(os.data_encerrada) >= TO_DATE('{date}', 'YYYY-MM-DD') 
 and OS_TIPOS.INTERNO = 'S'
   ) A
GROUP BY
    a.COD_EMPRESA,
    a.EMPRESA_SYS,
    a.DATA,
    a.grupo

UNION ALL
SELECT 
    TRUNC(os.data_encerrada, 'MM') AS data,
    ROUND(SUM(S.PRECO_VENDA - ((OS.DESCONTOS_SERVICOS / OS.VALOR_SERVICOS_BRUTO) * S.PRECO_VENDA)),2) AS total,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END AS GRUPO,
    'Funilaria' AS REF
FROM OS
LEFT JOIN OS_SERVICOS S ON S.NUMERO_OS = OS.NUMERO_OS
LEFT JOIN SERVICOS SV ON S.cod_servico = SV.cod_servico
JOIN OS_TIPOS ON OS.TIPO = OS_TIPOS.TIPO
LEFT JOIN EMPRESAS E ON S.COD_EMPRESA = E.COD_EMPRESA
WHERE 
    os.status_os not in (5,6) 
    AND S.COD_SERVICO LIKE '%%FUN%%'
    and  SV.terceiros = 'S'
    and  NVL(os.cortesia, 'N') = 'N' 
    And Nvl(OS.Apagar_Ao_Sair, 'N') = 'N'
    AND OS.COD_EMPRESA = S.COD_EMPRESA
    AND (S.COD_EMPRESA LIKE '1%' OR S.COD_EMPRESA LIKE '4%' OR S.COD_EMPRESA LIKE '6%')
    AND os.data_encerrada >= TO_DATE('{date}', 'YYYY-MM-DD')
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    TRUNC(os.data_encerrada, 'MM'),
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END
    
UNION ALL
SELECT 
    os.data_encerrada AS data,
    SUM(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS total,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END AS GRUPO,
    'Clientes' AS REF
FROM OS
JOIN OS_TIPOS ON OS.TIPO = OS_TIPOS.TIPO
LEFT JOIN EMPRESAS E ON OS.COD_EMPRESA = E.COD_EMPRESA
LEFT JOIN VENDAS V ON OS.cod_empresa = V.cod_empresa AND OS.numero_os = V.numero_os
WHERE os.status_os IN (1, 5)
  AND OS_TIPOS.garantia = 'N'
  AND os_tipos.interno = 'N'
  AND os_tipos.retorno = 'N'
  AND os_tipos.outro_concessionaria = 'N'
  AND os_tipos.produtiva = 'S'
  AND nvl(os.cortesia, 'N') = 'N'
  AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
  AND v.status = 0
  AND v.total_servicos > 0
  AND ((v.cod_operacao IN (2, 3)) OR (v.cod_operacao = 13))
  AND os.data_encerrada >= TO_DATE('{date}', 'YYYY-MM-DD')
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    os.data_encerrada,
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END
    
UNION ALL
SELECT 
    os.data_encerrada AS data,
    SUM(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS total,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN OT.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END AS GRUPO,
    'Garantia' AS REF      
FROM OS OS 
LEFT JOIN EMPRESAS E ON OS.COD_EMPRESA = E.COD_EMPRESA
LEFT JOIN vendas V ON V.cod_empresa = OS.cod_empresa 
                   AND V.numero_os = OS.numero_os     
                   AND V.status = 0                   
                   AND V.total_servicos > 0           
                   AND V.cod_operacao IN (2, 3, 13)   
INNER JOIN OS_TIPOS OT ON OT.tipo = OS.tipo         
                     AND OT.garantia = 'S' 
INNER JOIN EMPRESAS E2 ON E2.cod_empresa = OS.cod_empresa         
WHERE OS.status_os IN (1, 5)                
  AND nvl(OS.cortesia, 'N') = 'N'            
  AND os.data_encerrada >= TO_DATE('{date}', 'YYYY-MM-DD')  
  AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
  AND os.numero_os = nvl((SELECT T2.numero_os 
                          FROM OS T2 
                          JOIN OS_TIPOS T3 ON T2.tipo = T3.tipo
                          JOIN OS_TIPOS T4 ON T2.tipo = T4.tipo
                          WHERE OS.cod_empresa = T2.cod_empresa 
                            AND OS.numero_os_fabrica = T2.numero_os_fabrica 
                            AND OS.status_os = 1 
                            AND T2.status_os = 5 
                            AND T3.garantia = 'S' 
                            AND T3.interno = 'S' 
                            AND T3.seguradora = 'S' 
                            AND T4.garantia = 'S' 
                            AND T4.interno = 'S' 
                            AND T4.seguradora = 'S'), OS.numero_os)
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    os.data_encerrada,
    CASE 
    WHEN OT.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END
    
UNION ALL
SELECT 
    V.emissao AS data,
    SUM(V.TOTAL_SERVICOS - V.descontos_servicos) AS total,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN OT.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END AS GRUPO,
    'Concessionaria' AS REF                             
FROM VENDAS V
JOIN OS OS ON V.COD_EMPRESA = OS.COD_EMPRESA 
          AND V.NUMERO_OS = OS.NUMERO_OS
JOIN OS_TIPOS OT ON OS.TIPO = OT.TIPO
JOIN EMPRESAS E ON V.COD_EMPRESA = E.COD_EMPRESA
WHERE OS.status_os = 1
  AND OT.outro_concessionaria = 'S'
  AND nvl(OS.cortesia, 'N') = 'N'
  AND V.status <> 1
  AND V.total_servicos > 0
  AND V.cod_operacao = 2
  AND V.emissao >= TO_DATE('{date}', 'YYYY-MM-DD')
  AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    V.emissao,
    CASE 
    WHEN OT.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END
    
UNION ALL
SELECT 
    os.data_encerrada AS data,
    SUM(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS total,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN OT.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END AS GRUPO,
    'Internos' AS REF                                  
FROM OS OS 
LEFT JOIN EMPRESAS E ON OS.COD_EMPRESA = E.COD_EMPRESA
LEFT JOIN OS_TIPOS OT ON OS.TIPO = OT.TIPO
WHERE OT.interno = 'S' 
  AND OS.status_os IN (1, 5)  
  AND nvl(OS.cortesia, 'N') = 'N' 
  AND OS.data_encerrada >= TO_DATE('{date}', 'YYYY-MM-DD')
  AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
  AND OS.numero_os = nvl((SELECT T2.numero_os 
                          FROM OS T2 
                          JOIN OS_TIPOS T3 ON T2.tipo = T3.tipo
                          JOIN OS_TIPOS T4 ON T2.tipo = T4.tipo
                          WHERE OS.cod_empresa = T2.cod_empresa 
                            AND OS.numero_os_fabrica = T2.numero_os_fabrica 
                            AND OS.status_os = 1 
                            AND T2.status_os = 5 
                            AND T3.garantia = 'S' 
                            AND T3.interno = 'S' 
                            AND T3.seguradora = 'S' 
                            AND T4.garantia = 'S' 
                            AND T4.interno = 'S' 
                            AND T4.seguradora = 'S'), OS.numero_os)
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    os.data_encerrada,
    CASE
    WHEN OT.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END
    
UNION ALL
SELECT 
    os.data_encerrada AS data,
    SUM(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS total,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN OS_TIPOS.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END AS GRUPO,
    'Retorno' AS REF
FROM OS OS
JOIN OS_TIPOS ON OS.TIPO = OS_TIPOS.TIPO
JOIN EMPRESAS E ON OS.cod_empresa = E.cod_empresa
WHERE OS.STATUS_os IN (1, 5)
  AND OS_TIPOS.retorno = 'S'
  AND OS.data_encerrada >= TO_DATE('{date}', 'YYYY-MM-DD')
  AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
  AND OS.numero_os = nvl((SELECT T2.numero_os 
                          FROM os T2 
                          JOIN os_tipos T3 ON T2.tipo = T3.tipo
                          JOIN os_tipos T4 ON T2.tipo = T4.tipo
                          WHERE OS.cod_empresa = T2.cod_empresa 
                            AND OS.numero_os_fabrica = T2.numero_os_fabrica 
                            AND OS.status_os = 1 
                            AND T2.status_os = 5 
                            AND T3.garantia = 'S' 
                            AND T3.interno = 'S' 
                            AND T3.seguradora = 'S' 
                            AND T4.garantia = 'S' 
                            AND T4.interno = 'S' 
                            AND T4.seguradora = 'S'), OS.numero_os)
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    os.data_encerrada,
    CASE
    WHEN OS_TIPOS.DESCRICAO LIKE '%%FUN%%' THEN 'Funilaria'
    ELSE 'Serviços Oficina'
    END
    
UNION ALL
SELECT  
    TRUNC(os.data_encerrada, 'MM') AS PRIMEIRO_DIA_DO_MES,
    COUNT(os.numero_os) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN ost.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
    WHEN OST.interno = 'S' THEN 'Interno'
    ELSE 'Oficina/Garantia'
    END AS GRUPO,
    'Passagens_Ticket_Medio' as REF
    
FROM OS
LEFT JOIN OS_Tipos OST ON OST.TIPO = OS.TIPO 
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = OS.COD_EMPRESA
WHERE 
   (os.data_encerrada >= TO_DATE('{date}', 'YYYY-MM-DD')) 
    AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
    AND NVL(os.orcamento, 'N') = 'N' 
    AND NVL(OS.Apagar_Ao_Sair, 'N') = 'N' 
    AND NVL(OS.Complemento, 'N') = 'N'    
   -- AND OST.interno = 'N'
    AND os.status_os = 1
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    TRUNC(os.data_encerrada, 'MM'),
    CASE 
    WHEN ost.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
    WHEN OST.interno = 'S' THEN 'Interno'
    ELSE 'Oficina/Garantia'
    END 
    
UNION ALL
SELECT  
    TRUNC(os.data_emissao , 'MM') AS PRIMEIRO_DIA_DO_MES,
    COUNT(os.numero_os) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN ost.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
    WHEN OST.interno = 'S' THEN 'Interno'
    ELSE 'Oficina/Garantia'
    END AS GRUPO,
    'Passagens' as REF
FROM OS
LEFT JOIN OS_Tipos OST ON OST.TIPO = OS.TIPO 
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = OS.COD_EMPRESA
WHERE 
   (os.data_emissao  >= TO_DATE('{date}', 'YYYY-MM-DD'))  
    AND (OS.COD_EMPRESA LIKE '1%' OR OS.COD_EMPRESA LIKE '4%' OR OS.COD_EMPRESA LIKE '6%')
    AND NVL(os.orcamento, 'N') = 'N' 
    AND NVL(OS.Apagar_Ao_Sair, 'N') = 'N' 
    AND NVL(OS.Complemento, 'N') = 'N'    
 --   AND OST.interno = 'N'
    AND OST.PRODUTIVA = 'S'
--    AND OST.TIPO <> 'C1'
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    TRUNC(os.data_emissao , 'MM'),
    CASE 
    WHEN ost.DESCRICAO LIKE '%FUN%' THEN 'Funilaria'
    WHEN OST.interno = 'S' THEN 'Interno'
    ELSE 'Oficina/Garantia'
    END 
    
UNION ALL 
SELECT 
    v.emissao AS PRIMEIRO_DIA_DO_MES,
    SUM(vi.preco_liquido_final + nvl(vi.desconto_icms_item, 0)) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END AS GRUPO,
    'Pecas_Performance' as REF
FROM VENDAS V
LEFT JOIN VENDA_ITENS VI ON VI.COD_EMPRESA = V.COD_EMPRESA AND VI.CONTROLE = V.CONTROLE AND VI.SERIE = V.SERIE
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = V.COD_EMPRESA
LEFT JOIN ITENS I ON I.COD_ITEM = VI.COD_ITEM 
LEFT JOIN ITENS_GRUPO_INTERNO IGI ON IGI.COD_GRUPO_INTERNO = I.COD_GRUPO_INTERNO
WHERE 
   (v.emissao >= TO_DATE('{date}', 'YYYY-MM-DD'))  
    AND (V.COD_EMPRESA LIKE '1%' OR V.COD_EMPRESA LIKE '4%' OR V.COD_EMPRESA LIKE '6%')
    AND V.STATUS <> '1'
    AND V.COD_OPERACAO IN (1, 2, 3, 13, 19, 26)
--    AND VI.SERIE = '1' 
    AND VI.QTDE > 0
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END,
    v.emissao
    
UNION ALL
SELECT 
    TRUNC(v.emissao, 'MM') AS PRIMEIRO_DIA_DO_MES,
    ROUND(SUM((vi.preco_liquido_final + nvl(vi.desconto_icms_item, 0)) - (vi.preco_contabil*vi.qtde)),4) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END AS GRUPO,
    'Pecas_Margem' as REF
FROM VENDAS V
INNER JOIN VENDA_ITENS VI ON VI.COD_EMPRESA = V.COD_EMPRESA AND VI.CONTROLE = V.CONTROLE AND VI.SERIE = V.SERIE
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = V.COD_EMPRESA
LEFT JOIN ITENS I ON I.COD_ITEM = VI.COD_ITEM 
LEFT JOIN ITENS_GRUPO_INTERNO IGI ON IGI.COD_GRUPO_INTERNO = I.COD_GRUPO_INTERNO
WHERE 
   (v.emissao >= TO_DATE('{date}', 'YYYY-MM-DD'))
    AND (V.COD_EMPRESA LIKE '1%' OR V.COD_EMPRESA LIKE '4%' OR V.COD_EMPRESA LIKE '6%')
    AND V.STATUS <> '1'
    AND V.COD_OPERACAO IN (1, 2, 3, 13, 19, 26)
--    AND VI.SERIE = '1' 
    AND VI.QTDE > 0
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END,
    TRUNC(v.emissao, 'MM')
    
UNION ALL
SELECT 
    TRUNC(v.emissao, 'MM') AS PRIMEIRO_DIA_DO_MES,
    ROUND(SUM((vi.preco_liquido_final + nvl(vi.desconto_icms_item, 0)) - (vi.preco_contabil*vi.qtde)),2) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END AS GRUPO,
    'Pecas_Margem_Bruta' as REF
FROM VENDAS V
INNER JOIN VENDA_ITENS VI ON VI.COD_EMPRESA = V.COD_EMPRESA AND VI.CONTROLE = V.CONTROLE AND VI.SERIE = V.SERIE
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = V.COD_EMPRESA
LEFT JOIN ITENS I ON I.COD_ITEM = VI.COD_ITEM 
LEFT JOIN ITENS_GRUPO_INTERNO IGI ON IGI.COD_GRUPO_INTERNO = I.COD_GRUPO_INTERNO
WHERE 
   (v.emissao >= TO_DATE('{date}', 'YYYY-MM-DD'))  
    AND (V.COD_EMPRESA LIKE '1%' OR V.COD_EMPRESA LIKE '4%' OR V.COD_EMPRESA LIKE '6%')
--    AND V.STATUS <> '1'
    AND V.COD_OPERACAO IN (1, 2, 3, 13, 19, 26)
    AND VI.SERIE = '1' 
    AND VI.QTDE > 0
GROUP BY
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END,
    TRUNC(v.emissao, 'MM')
    
UNION ALL
SELECT
    CASE 
    WHEN MN1.MonthNumber = 0 THEN TRUNC(SYSDATE, 'MM')
    ELSE LAST_DAY(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -MN1.MonthNumber)) 
    END AS DATA,
    ROUND(SUM(CC.CUSTO_RESULTANTE * CC.QTDE_SALDO_ESTOQUE), 2) AS TOTAL,
    'NBS' AS EMPRESA_SYS,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END AS GRUPO,
    'Pecas_Estoque' AS REF
FROM
    (
        SELECT LEVEL - 1 AS MonthNumber
        FROM dual
        CONNECT BY LEVEL <= 24
    ) MN1  
JOIN CARDEX_CONTABIL CC ON 1=1
INNER JOIN       
    (
        SELECT
            LAST_DAY(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -MN.MonthNumber)) AS DATA,
            CDX.COD_EMPRESA,
            CDX.COD_FORNECEDOR,
            CDX.COD_ITEM,
            MAX(CDX.DATA) AS MAXDATA
        FROM
            CARDEX_CONTABIL CDX
        INNER JOIN
            (
                SELECT DISTINCT COD_ITEM, COD_FORNECEDOR, COD_EMPRESA
                FROM ITENS_HISTORICO IH
                WHERE IH.COD_OPERACAO NOT IN (21, 31)
            ) ITENS ON
            ITENS.COD_ITEM = CDX.COD_ITEM
            AND ITENS.COD_FORNECEDOR = CDX.COD_FORNECEDOR
            AND ITENS.COD_EMPRESA = CDX.COD_EMPRESA 
        JOIN (
            SELECT LEVEL - 1 AS MonthNumber
            FROM dual
            CONNECT BY LEVEL <= 24
        ) MN ON 1=1 
        WHERE
            CDX.DATA < LAST_DAY(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -MN.MonthNumber)) + 1
            AND CDX.status <> 'C'
        GROUP BY
            LAST_DAY(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -MN.MonthNumber)),
            CDX.COD_EMPRESA,
            CDX.COD_FORNECEDOR,
            CDX.COD_ITEM
    ) MAXDT ON
    MAXDT.COD_EMPRESA = CC.COD_EMPRESA
    AND MAXDT.COD_FORNECEDOR = CC.COD_FORNECEDOR
    AND MAXDT.COD_ITEM = CC.COD_ITEM
    AND MAXDT.MAXDATA = CC.DATA
    AND MAXDT.DATA = LAST_DAY(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -MN1.MonthNumber))
LEFT JOIN EMPRESAS E ON E.COD_EMPRESA = CC.COD_EMPRESA
LEFT JOIN ITENS I ON I.COD_ITEM = CC.COD_ITEM 
LEFT JOIN ITENS_GRUPO_INTERNO IGI ON IGI.COD_GRUPO_INTERNO = I.COD_GRUPO_INTERNO
WHERE
    CC.QTDE_SALDO_ESTOQUE <> 0
    AND IGI.DESCRICAO <> 'PATRIMONIO'
    AND (CC.COD_EMPRESA LIKE '1%' OR CC.COD_EMPRESA LIKE '4%' OR CC.COD_EMPRESA LIKE '6%')
GROUP BY 
    E.NOME,
    E.CIDADE,
    E.COD_EMPRESA,
    CASE 
    WHEN IGI.DESCRICAO LIKE '%TEQUIPMENT%' THEN 'T-Equipment'
    WHEN IGI.DESCRICAO LIKE '%PDS%' THEN 'PDS'
    WHEN IGI.DESCRICAO LIKE '%COLLECTION%' THEN 'Collection'
    ELSE 'Peças Oficina'
    END,
    CASE 
    WHEN MN1.MonthNumber = 0 THEN TRUNC(SYSDATE, 'MM')
    ELSE LAST_DAY(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -MN1.MonthNumber)) 
    END
"""

query_dealer_vendas = f"""
SELECT distinct
            CAST(nf.NotaFiscal_DataEmissao as date) as DATA,
            pc.Pessoa_Nome AS CLIENTE,  
            pc.Pessoa_DocIdentificador AS DOCCLI,
            NotaFiscalItem_ValorPresenteNF VALOR,  
			CASE 
				WHEN NaturezaOperacao_Descricao LIKE '%IMOBILIZADO%' THEN NotaFiscalItem_ValorPresenteNF * 0.96
			ELSE NotaFiscalItem_ValorPresenteNF - NotaFiscalItem_ValorLucroBruto -- NotaFiscalItem_ValorMargemGerencial
			END AS CUSTO,
            null AS STATUS,
            CASE 
                WHEN dp.Departamento_Descricao like '%%novo%%' THEN 'Novo'
                WHEN dp.Departamento_Descricao like '%%usad%%' THEN 'Seminovo'
                WHEN dp.Departamento_Descricao like '%%imob%%' THEN 'Seminovo'
                WHEN dp.Departamento_Descricao like '%%consig%%' THEN 'Seminovo'
                ELSE V.Veiculo_Status
            END AS NOVO_USADO,	
            'DEALERNETWF' as EMPRESA_SYS,
            '23' AS COD_EMPRESA,
            ' Veículo Faturado' AS EMITIU_NOTA,
            U.Usuario_Nome AS VENDEDOR,
            CASE 
				WHEN NaturezaOperacao_Descricao LIKE '%CONSIG%'
				THEN 'Consignado'
				WHEN NaturezaOperacao_Descricao LIKE '%IMOBILIZADO%'
				THEN 'Imobilizado'
				ELSE 'Estoque Próprio'
			END AS TIPO_FATURAMENTO,
            fv.FamiliaVeiculo_Descricao AS ITEM,
            MV.ModeloVeiculo_Descricao AS DESCRICAO_MODELO,
            V.Veiculo_Chassi AS CHASSI,
            NULL AS PERIODOESTOQUE,
        null AS DGIRO,
            'Vendas' as TABREF
        FROM NotaFiscal nf 
            INNER JOIN NotaFiscalItem ni ON ni.NotaFiscal_Codigo = nf.NotaFiscal_Codigo
            INNER JOIN Pessoa pc ON nf.NotaFiscal_PessoaCod = pc.Pessoa_Codigo
            INNER JOIN Empresa e ON e.Empresa_Codigo = nf.NotaFiscal_EmpresaCod
            INNER JOIN Empresa em ON em.Empresa_Codigo = e.Empresa_EmpresaCodMatriz
            LEFT JOIN NaturezaOperacao onf ON onf.NaturezaOperacao_Codigo = nf.NotaFiscal_NaturezaOperacaoCod
            LEFT JOIN NaturezaOperacaoTipoItem tno ON tno.NaturezaOperacao_Codigo = onf.NaturezaOperacao_Codigo
            LEFT JOIN veiculo v ON v.Veiculo_Codigo = ni.NotaFiscalItem_VeiculoCod
            LEFT JOIN ModeloVeiculo mv ON mv.ModeloVeiculo_Codigo = v.Veiculo_ModeloVeiculoCod
            LEFT JOIN FamiliaVeiculo fv ON fv.FamiliaVeiculo_Codigo = (case WHEN v.Veiculo_Status = 'N' then mv.ModeloVeiculo_FamiliaVeiculoCod_Novos else mv.ModeloVeiculo_FamiliaVeiculoCod_Usados END)
            LEFT JOIN Departamento dp on dp.Departamento_Codigo = nf.NotaFiscal_DepartamentoCod
            LEFT JOIN Usuario u on u.Usuario_Codigo = nf.NotaFiscal_UsuCodVendedor
            LEFT JOIN NotaFiscalNFReferencia nfr ON nf.NotaFiscal_Codigo = nfr.NotaFiscalNFReferencia_NFCod 
        WHERE   NF.NotaFiscal_Movimento = 'S' 
                and NF.NotaFiscal_Status in ('EMI')
                and e.Empresa_Codigo = 23
                and onf.NaturezaOperacao_GrupoMovimento = 'VEN'
                and tno.NaturezaOperacao_TipoItem = 'V'
                and isnull(nfr.NotaFiscalNFReferencia_Tipo,'') <> 'DEV'
                and CAST(nf.NotaFiscal_DataEmissao as date) > '{date}'
                

UNION ALL

SELECT distinct
    GETDATE(),
    NULL AS CLIENTE,  
    NULL AS DOCCLI,
    P.Proposta_Valor AS VALOR,  
    null AS CUSTO,
    P.Proposta_Status,
    CASE 
        WHEN Estoque_Descricao LIKE '%novo%' THEN 'Novo'
        WHEN Estoque_Descricao LIKE '%%usad%%' THEN 'Seminovo'
        WHEN Estoque_Descricao LIKE '%%imob%%' THEN 'Seminovo'
        WHEN Estoque_Descricao LIKE '%%consig%%' THEN 'Seminovo'
        ELSE V.Veiculo_Status
    END AS NOVO_USADO,
            'DEALERNETWF' as EMPRESA_SYS,
            '23' AS COD_EMPRESA,
    'Proposta confirmada' AS EMITIU_NOTA,
    U1.Usuario_Nome AS VENDEDOR,
    CASE 
				WHEN EXISTS (SELECT TOP(1) 1 FROM VeiculoMovimento VM 
							 INNER JOIN Estoque EST ON EST.Estoque_Codigo = VM.VeiculoMovimento_EstoqueCod 
							 WHERE VM.Veiculo_Codigo = v.Veiculo_Codigo AND EST.Estoque_descricao LIKE '%%consig%%') 
				THEN 'Consignado'
				WHEN EXISTS (SELECT TOP(1) 1 FROM VeiculoMovimento VM 
							 INNER JOIN Estoque EST ON EST.Estoque_Codigo = VM.VeiculoMovimento_EstoqueCod 
							 WHERE VM.Veiculo_Codigo = v.Veiculo_Codigo AND EST.Estoque_descricao LIKE '%%imobi%%') 
				THEN 'Imobilizado'
				ELSE 'Estoque Próprio'
			END AS TIPO_FATURAMENTO,
    fv.FamiliaVeiculo_Descricao AS ITEM,
    MDV.ModeloVeiculo_Descricao,
    V.Veiculo_Chassi AS CHASSI,
    CASE 
        WHEN DATEDIFF(day, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS date), CAST(GETDATE() AS date)) BETWEEN 0 AND 30 THEN '0 - 30 dias'
        WHEN DATEDIFF(day, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS date), CAST(GETDATE() AS date)) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
        WHEN DATEDIFF(day, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS date), CAST(GETDATE() AS date)) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
        WHEN DATEDIFF(day, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS date), CAST(GETDATE() AS date)) > 90 THEN 'Mais de 90 dias'
        ELSE NULL
    END AS PeriodoEstoque,
        DATEDIFF(DAY, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS DATE), CAST(GETDATE() AS DATE)) AS DGIRO,
    'Vendas' AS TABREF
FROM 
	[HULKBUSTER\ERP].dbDealernetWF.dbo.PROPOSTA P
	left join [HULKBUSTER\ERP].dbDealernetWF.dbo.Atendimento ATD ON ATD.Atendimento_Codigo = p.Atendimento_Codigo
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.VeiculoEstoque VE ON P.Proposta_VeiculoCod = VE.VeiculoEstoque_VeiculoCod
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.Veiculo V ON V.Veiculo_Codigo = VE.VeiculoEstoque_VeiculoCod
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.ModeloVeiculo MDV ON MDV.ModeloVeiculo_Codigo = V.Veiculo_ModeloVeiculoCod
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.NotaFiscal NFEntrada ON NFEntrada.NotaFiscal_Codigo = VE.VeiculoEstoque_NotaFiscalCodCompra
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.Estoque E ON E.Estoque_Codigo = VE.VeiculoEstoque_EstoqueCod
	INNER JOIN (
		SELECT VeiculoMovimento.Veiculo_Codigo, NotaFiscal.NotaFiscal_Codigo, MIN(VeiculoMovimento.VeiculoMovimento_Data) VeiculoMovimento_Data
		FROM [HULKBUSTER\ERP].dbDealernetWF.dbo.VeiculoMovimento
		INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.NotaFiscal ON NotaFiscal.NotaFiscal_Codigo = VeiculoMovimento.VeiculoMovimento_NotaFiscalCod
		INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.NaturezaOperacao ON NaturezaOperacao.NaturezaOperacao_Codigo = NotaFiscal.NotaFiscal_NaturezaOperacaoCod
		WHERE NaturezaOperacao.NaturezaOperacao_Codigo IN (224, 226)
		GROUP BY VeiculoMovimento.Veiculo_Codigo, NotaFiscal.NotaFiscal_Codigo
	) AS dtEntradaVeiculo ON dtEntradaVeiculo.Veiculo_Codigo = V.Veiculo_Codigo AND dtEntradaVeiculo.NotaFiscal_Codigo = NFEntrada.NotaFiscal_Codigo	
	LEFT JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.Usuario U1 ON ATD.Atendimento_UsuarioCod = U1.Usuario_Codigo
	LEFT JOIN FamiliaVeiculo fv ON fv.FamiliaVeiculo_Codigo = (case WHEN v.Veiculo_Status = 'N' then MDV.ModeloVeiculo_FamiliaVeiculoCod_Novos else MDV.ModeloVeiculo_FamiliaVeiculoCod_Usados END)
WHERE 
	P.Proposta_EmpresaCod = 23 
	and P.Proposta_Status = 'AUT' 
"""

query_dealer_estoque = """
    SELECT 
       GETDATE() AS DATA,
       null AS CLIENTE,  
       NULL AS DOCCLI,
       NFEntrada.NotaFiscal_ValorTotal AS VALOR,  
       NFEntrada.NotaFiscal_ValorTotal AS CUSTO,
       'Normal' AS STATUS,
       CASE WHEN Estoque_Descricao LIKE '%novo%' THEN 'Novo'
            WHEN Estoque_Descricao LIKE '%%usad%%' THEN 'Seminovo'
            WHEN Estoque_Descricao LIKE '%%imob%%' THEN 'Seminovo'
            WHEN Estoque_Descricao LIKE '%%consig%%' THEN 'Seminovo'
            ELSE V.Veiculo_Status END AS NOVO_USADO,
    'NBS' AS EMPRESA_SYS,
		CASE 
		WHEN LVC.LocalVeiculo_Descricao LIKE '%RAJA%' THEN '101'
		WHEN LVC.LocalVeiculo_Descricao LIKE '%PORSCHE BH%' THEN '401'
		WHEN LVC.LocalVeiculo_Descricao LIKE '%JUIZ%' THEN '103'
		END AS COD_EMPRESA,
       CASE WHEN VE.VeiculoEstoque_NotaFiscalCodCompra IS NOT NULL THEN 'Pago'
            ELSE 'A pagar' END AS EMITIU_NOTA,
       U.Usuario_Nome AS VENDEDOR,
	   CASE WHEN Estoque_Descricao LIKE '%%novo%%' THEN 'Estoque Próprio'
            WHEN Estoque_Descricao LIKE '%%usad%%' THEN 'Estoque Próprio'
            WHEN Estoque_Descricao LIKE '%%imob%%' THEN 'Imobilizado'
            WHEN Estoque_Descricao LIKE '%%consig%%' THEN 'Consignado'
            ELSE Estoque_Descricao END AS TIPO_FATURAMENTO,
       fv.FamiliaVeiculo_Descricao AS ITEM,
       MDV.ModeloVeiculo_Descricao as DESCRICAO_MODELO,
       V.Veiculo_Chassi AS CHASSI,
       CASE WHEN DATEDIFF(DAY, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS DATE), CAST(GETDATE() AS DATE)) BETWEEN 0 AND 30 THEN '0 - 30 dias'
            WHEN DATEDIFF(DAY, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS DATE), CAST(GETDATE() AS DATE)) BETWEEN 30.001 AND 60 THEN '31 - 60 dias'
            WHEN DATEDIFF(DAY, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS DATE), CAST(GETDATE() AS DATE)) BETWEEN 60.001 AND 90 THEN '61 - 90 dias'
            WHEN DATEDIFF(DAY, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS DATE), CAST(GETDATE() AS DATE)) > 90 THEN 'Mais de 90 dias'
            ELSE NULL END AS PeriodoEstoque,
        DATEDIFF(DAY, CAST(dtEntradaVeiculo.VeiculoMovimento_Data AS DATE), CAST(GETDATE() AS DATE)) AS DGIRO,
       'Estoque' AS TABREF
FROM 
	[HULKBUSTER\ERP].dbDealernetWF.dbo.VeiculoEstoque VE
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.Veiculo V ON V.Veiculo_Codigo = VE.VeiculoEstoque_VeiculoCod
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.ModeloVeiculo MDV ON MDV.ModeloVeiculo_Codigo = V.Veiculo_ModeloVeiculoCod
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.NotaFiscal NFEntrada ON NFEntrada.NotaFiscal_Codigo = VE.VeiculoEstoque_NotaFiscalCodCompra
	INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.Estoque E ON E.Estoque_Codigo = VE.VeiculoEstoque_EstoqueCod
	inner JOIN (SELECT VeiculoMovimento.Veiculo_Codigo, NotaFiscal.NotaFiscal_Codigo, MIN(VeiculoMovimento.VeiculoMovimento_Data) VeiculoMovimento_Data
				FROM [HULKBUSTER\ERP].dbDealernetWF.dbo.VeiculoMovimento
				INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.NotaFiscal ON NotaFiscal.NotaFiscal_Codigo = VeiculoMovimento.VeiculoMovimento_NotaFiscalCod
				INNER JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.NaturezaOperacao ON NaturezaOperacao.NaturezaOperacao_Codigo = NotaFiscal.NotaFiscal_NaturezaOperacaoCod
	--			WHERE NaturezaOperacao.NaturezaOperacao_Codigo IN (224, 226, 231)
				GROUP BY VeiculoMovimento.Veiculo_Codigo, NotaFiscal.NotaFiscal_Codigo) AS dtEntradaVeiculo ON dtEntradaVeiculo.Veiculo_Codigo = V.Veiculo_Codigo AND dtEntradaVeiculo.NotaFiscal_Codigo = NFEntrada.NotaFiscal_Codigo
	LEFT JOIN [HULKBUSTER\ERP].dbDealernetWF.dbo.Usuario U ON V.Veiculo_UsuarioCodVendedor = U.Usuario_Codigo
	LEFT JOIN FamiliaVeiculo fv ON fv.FamiliaVeiculo_Codigo = (case WHEN v.Veiculo_Status = 'N' then MDV.ModeloVeiculo_FamiliaVeiculoCod_Novos else MDV.ModeloVeiculo_FamiliaVeiculoCod_Usados END)
	left join VeiculoTransfLocal VTL on VTL.veiculo_codigo = VE.VeiculoEstoque_VeiculoCod AND VTL.VeiculoTransfLocal_DataHora = (select max(vtfl.VeiculoTransfLocal_DataHora) from VeiculoTransfLocal vtfl where vtfl.veiculo_codigo = VE.VeiculoEstoque_VeiculoCod)
	left join LocalVeiculo LVC on VTL.LocalVeiculo_Codigo = LVC.LocalVeiculo_Codigo
WHERE 
	VE.VeiculoEstoque_EmpresaCod = 23 AND E.Estoque_Tipo <> 'VD' AND VE.VeiculoEstoque_VeiculoMovCodSaida IS NULL AND Estoque_Descricao NOT LIKE '%NBS%'
"""