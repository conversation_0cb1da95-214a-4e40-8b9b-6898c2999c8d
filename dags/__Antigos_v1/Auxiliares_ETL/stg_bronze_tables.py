tables_dw_bamaq = {
    "vendas": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "ponto_vendas": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "subcanal_venda_ponto_vendas": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "subcanal_venda": {
        "times_per_day": 3,
        "kwargs": {
        }
    }
} 

tables_nbs = {
    "BAIXA_CONTAS_RECEBER": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "baixa_adiantamento": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "BQ_MOBILIDADE_ESTOQUE": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "BQ_MOBILIDADE_OS": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "ADIANTAMENTO": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
    "cardex_contabil": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_empresa", "id_cardex_item", "cod_item", "cod_fornecedor"],
            # "date_columns": ["data", "data_created"]
        }
    },
    "cartao_credito": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_empresa", "cod_cartao_credito"]
        }
    },
    "cidades": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "centro_custo": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "clientes": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_cliente"],
            # "date_columns": ["data_admissao", "data_admissao_trabalho", "data_inicio_res", "data_proxima_ligacao", "data_proxima_visita", "data_ultima_visita", "hora_final_ligar", "hora_inicial_ligar"]
        }
    },
    "cliente_diverso": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_cliente", "cod_tipo_cliente"],
            # "date_columns": ["data_cadastro", "data_alt_saf", "dataalt_validacao_celular", "dataalt_validacao_email", "data_envio_h3s_honda", "data_envio_myhonda", "data_lib_bloq", "data_limite_credito", "data_limite_credito_compl", "data_proximo_fat_cli", "data_saf_envio", "data_ultima_atualizacao", "data_validade", "data_validade_serasa", "data_validou_celular", "data_validou_email", "dt_consulta_crivo", "venc_cart_motorista"],
            # "normal_columns": ["cgc", "cpf", "cliente_especial_grupo", "cod_cidades"]
        }
    },
    "conta_corrente": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["cod_empresa", "cod_conta_corrente"]
        }
    },
    "CONTAS_RECEBER": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_controle_cartao"],
            # "date_columns": ["data"]
        }
    },
    "creceber_cartao": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_controle_cartao"],
            # "date_columns": ["data"]
        }
    },
    "creceber_cartao_parc": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_controle_cartao","parcela"],
            # "date_columns": ["data", "data_baixa", "data_vencimento"]
        }
    },
    "compra": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "CUSTOS_ESPECIFICOS": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "empresas": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "empresas_departamentos": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_empresa", "cod_empresa_departamento"]
        }
    },
    "empresas_usuarios": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["COD_EMPRESA_DIVISAO", "COD_EMPRESA_DEPARtAMENtO", "COD_FUNCAO", "COD_EMPRESA"],
            # "date_columns": ["data_ultima_atualizacao", "agenda_desbloqueio_auto", "data_envio_h3s_honda", "dt_admissao", "dt_demissao", "dt_fim_bloqueio", "dt_ini_bloqueio", "dt_nascimento", "entrega_data_saida", "ferias_final", "ferias_inicial", "fila_autobusca", "fila_crmparts", "fila_pos_vendas", "fila_reclamacao", "fila_whatsapp", "reserva_final", "reserva_inicial", "ultima_agenda", "ultima_agenda_fone", "ultima_atualizacao_pabx", "ultimo_evento_perdido", "ultimo_lead_canal_midia"],
            # "normal_columns": ["nome", "nome_completo"]
        }
    },
    "ev_agendados": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_entrega"],
            # "date_columns": ["data_criacao", "data_agendada", "data_baixa", "data_cancelamento", "data_reativacao"],
            # "normal_columns": ["agendado_fora", "chassi_resumido", "cod_cliente", "cod_cliente_entrega", "cod_empresa", "cod_empresa_entrega", "cod_evento", "cod_modelo", "cod_motivo", "cod_produto", "cod_proposta", "cod_sala", "cor", "entrega_manual", "entrega_realizada", "novo_usado", "placa", "plataforma", "quem_agendou", "quem_alterou", "quem_baixou", "quem_cancelou", "quem_criou", "quem_mot_reagendamento", "quem_reativou", "recusa_checklist", "responsavel_entrega", "status", "vendedor"]
        }
    },
    "FATURA_RECEBER": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "fornecedor_estoque": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "forma_cobranca": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_empresa", "cod_forma_cobranca"]
        }
    },
    "forma_pgto": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_empresa", "cod_forma_pgto"]
        }
    },
    "grupo_pc": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_grupo_pc"]
        }
    },
    "itens": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "itens_classe_contabil": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "itens_fornecedor": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "itens_grupo_interno": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "ITENS_SUB_GRUPO": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "marcas": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "natureza_receita_despesa": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_natureza_receita_despesa", "cod_grupo_pc"]
        }
    },
    "operacoes": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["numero_os"],
            # "date_columns": ["data_emissao", "data_cancelamento", "data_encerrada"],
            # "normal_columns": ["descontos_itens", "orc_item_desconto", "cod_centro_custo", "cod_setor_venda", "cod_modelo", "cod_produto", "cod_cliente", "apagar_ao_sair", "cod_empresa", "cod_grupo_pc", "complemento", "cortesia", "descontos_servicos", "numero_os_fabrica", "orcamento", "status_os", "tipo", "valor_servicos_bruto", "quem_abriu"]
        }
    },
    "os_cancelamento": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os_dados_veiculos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os_servicos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os_status": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os_tipos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os_tempos_executados": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "os_requisicoes": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "pagamento_venda": {
        "times_per_day": 1,
        "kwargs": {
            # "id_columns": ["controle", "serie", "cod_empresa"]
        }
    },
    "parm_sys2": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "parm_sys3": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "produtos": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_produto"]
        }
    },
    "produtos_modelos": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_modelo"],
            # "date_columns": ["data_inclusao", "data_saf_alt", "data_saf_envio", "interface_atualizacao"]
        }
    },
    "servicos": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_servico"],
            "date_columns": ["data_inclusao", "data_saf_alt", "data_saf_envio"]
        }
    },
    "servicos_tecnicos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "servicos_setores": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "setor_venda": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "TIPO_ADIANTAMENTO": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
    "totalizador_mensal": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "venda_itens": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["cod_fornecedor", "cod_empresa", "cod_item", "controle", "serie"],
            # "date_columns": ["data", "data_atualizacao_fab"]
        }
    },
    "vendas": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_empresa", "controle", "serie"],
            "date_columns": ["emissao", "data_emissao_sistema", "data_envio_h3s_honda", "data_envio_myhonda", "data_faturamento", "data_fixsped", "data_hora_saida", "data_inclusao", "data_saf_alt", "data_saf_envio", "data_venda_detran", "emissao_nf_eletronica", "proposta_data_dev", "tranf_icms_mes_ano"],
            # "normal_columns": ["descontos_servicos", "grupo", "cod_proposta", "vendedor", "cod_cliente", "cod_modelo", "cod_operacao", "cod_produto", "cod_setor", "numero_os", "status", "total_servicos", "cod_grupo_pc", "cod_natureza", "total_descontos", "cod_empresa_divisao", "cod_empresa_departamento", "total_nota"]
        }
    },
    "venda_os": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veic_kardex": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculos_custos_especificos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "VEIC_CUSTOS_VENDIDOS": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculos": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["chassi_completo"],
            # "date_columns": ["data_nota", "data_venda", "data_entrada", "data_aprovacao", "data_auditoria_vda", "data_autorizada", "data_baixa_financiamento", "data_chegada", "data_demo", "data_dev_compras", "data_dev_venda", "data_emplacamento", "data_ent_transito", "data_faturamento", "data_forplan", "data_gravame", "data_montagem", "data_nota_original", "data_pagamento", "data_pgto_antecipado", "data_producao", "data_retorno", "data_retorno_transf", "data_saida", "data_transferencia", "data_ultima_atualizacao", "data_vencimento_pagto", "data_venda_original", "di_data", "rtemp_inicio"]
        }
    },
    "veiculos_propostas": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["cod_proposta"],
            "date_columns": ["emissao", "data_aprovacao", "data_cancelamento", "data_devolucao", "data_envio_h3s_honda", "data_envio_myhonda", "data_envio_pedido_h3s_honda", "data_pedido_bndv", "data_previsao_emplacamento", "data_prevista", "data_reserva", "data_resposta_perfil", "data_ultima_atualizacao", "data_venda", "previsao_entrega", "validade"]
        }
    },
    "vw_endereco": {
        "times_per_day": 1,
        "kwargs": {
            # "id_columns": ["cod_cliente"]
        }
    },
    "itens_historico": {
        "times_per_day": 3,
        "kwargs": {
        }
    }
}

tables_dealer = {
    "agentecobrador": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["agentecobrador_codigo"],
            "date_columns": ["agentecobrador_dataCriacao", "agentecobrador_dataAlteracao"]
        }
    },
    "atendimento": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "bqvw_DwErpPesados_FaturamentoVeiculos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaoempresa": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissao": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaonatureza": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaoperfil": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaoperfilregra": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaoperfilregrafaixa": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaosetorservico": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "comissaotipoproduto": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "condicaopagamento": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["condicaopagamento_codigo"],
            "date_columns": ["condicaopagamento_dataCriacao", "condicaopagamento_dataAlteracao"]
        }
    },
    "cor": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "custo": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["custo_codigo"],
            "date_columns": ["custo_dataCriacao", "custo_dataAlteracao"]
        }
    },
    "departamento": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["departamento_codigo"],
            "date_columns": ["departamento_dataCriacao", "departamento_dataAlteracao"]
        }
    },
    "empresa": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["empresa_codigo"],
            "date_columns": ["empresa_dataCriacao", "empresa_dataAlteracao"]
        }
    },
    "empresaestoque": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "empresatabelapreco": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "estado": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["estado_codigo"],
            "date_columns": ["estado_dataCriacao", "estado_dataAlteracao"]
        }
    },
    "estoque": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["estoque_codigo"],
            "date_columns": ["estoque_dataCriacao", "estoque_dataAlteracao"]
        }
    },
    "estoqueempresa": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "estoquenatoperacao": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "FichaRazao": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "FichaRazaoMov": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "TipoFichaRazao": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "familiaveiculo": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["familiaveiculo_codigo"],
            "date_columns": ["familiaveiculo_dataCriacao", "familiaveiculo_dataAlteracao"]
        }
    },
    "grupoproduto": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["grupoproduto_codigo"],
            "date_columns": ["grupoproduto_dataCriacao", "grupoproduto_dataAlteracao"]
        }
    },
    "grupolucratividade": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["grupolucratividade_codigo"],
            "date_columns": ["grupolucratividade_dataCriacao", "grupolucratividade_dataAlteracao"]
        }
    },
    "inventario": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "inventarioproduto": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "localizacaoproduto": {
        "times_per_day": 3,
        "kwargs": {
            }
    },
    "localveiculo": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["localveiculo_codigo"],
            "date_columns": ["localveiculo_dataCriacao", "localveiculo_dataAlteracao"]
        }
    },
    "marca": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["marca_codigo"],
            "date_columns": ["marca_dataCriacao", "marca_dataAlteracao"]
        }
    },
    "modeloveiculo": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["modeloveiculo_codigo"],
            "date_columns": ["modeloveiculo_dataCriacao", "modeloveiculo_dataAlteracao"]
        }
    },
    "motvendaperdida": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "movimentoestoque": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "movimentoestoquehistorico": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "movimentoestoqueitem": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "movimentoestoqueitemmoeda": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "municipio": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["municipio_codigo"],
            "date_columns": ["municipio_dataCriacao", "municipio_dataAlteracao"]
        }
    },
    "naturezaoperacao": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["naturezaoperacao_codigo"],
            "date_columns": ["naturezaoperacao_dataCriacao", "naturezaoperacao_dataAlteracao"]
        }
    },
    "naturezaoperacaotipoitem": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "notafiscal": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "notafiscalitem": {
        "times_per_day": 2,
        "kwargs": {
        }
    },
    "notafiscalitemcusto": {
        "times_per_day": 2,
        "kwargs": {
        }
    },
    "notafiscalitemoficinaproduto": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "notafiscalitemoficinaservico": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "notafiscalitemtributo": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["notafiscal_codigo", "notafiscalitem_codigo", "notafiscalitemtributo_tributocod"]
        }
    },
    "notafiscalnfreferencia": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "nti": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["nti_codigo"],
            #"date_columns": [""]
        }
    },
    "ntihistorico": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["nti_codigo"],
            # "date_columns": ["ntiHistorico_data"]
        }
    },
    "ntiitem": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["nti_codigo", "ntiItem_codigo"],
            #"date_columns": [""]
        }
    },
    "oficinamarcacao": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["oficinamarcacao_codigo"],
            # "date_columns": []
        }
    },
    "oficinaproduto": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["oficinaproduto_codigo"],
            "date_columns": []
        }
    },
    "oficinarequisicao": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["oficinarequisicao_codigo"],
            "date_columns": ["oficinarequisicao_data"]
        }
    },
    "oficinarequisicaoitem": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["oficinarequisicao_codigo", "oficinarequisicaoitem_codigo"],
            "date_columns": []
        }
    },
    "oficinaservico": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["oficinaservico_codigo"],
            # "date_columns": ["oficinaservico_dataPrevisao", "oficinaservico_dataTerminoAnt"]
        }
    },
    "oficinaservicorateio": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["oficinaservico_codigo"],
            # "date_columns": ["oficinaservico_dataPrevisao", "oficinaservico_dataTerminoAnt"]
        }
    },
    "os": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["os_codigo"],
            # "date_columns": ["os_dataPrometida", "os_dataCriacao", "os_dataLiberacaoVeiculo", "os_dataRecepcao"]
        }
    },
    "ostipoos": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["os_codigo", "ostipoos_tipooscod"]
        }
    },
    "ostipooshistorico": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["os_codigo", "ostipoos_tipooscod"],
            # "date_columns": ["ostipooshistorico_data"]
        }
    },
    "pessoa": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
    "pessoaendereco": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["pessoa_codigo","pessoaendereco_codigo"],
            # "date_columns": ["pessoaendereco_dataCriacao"]
        }
    },
    "pessoatelefone": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["pessoa_codigo","pessoatelefone_codigo"],
            # "date_columns": ["pessoatelefone_dataAlteracao"]
        }
    },
    "proposta": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["proposta_codigo"],
            # "date_columns": ["proposta_dataCriacao", "proposta_dataConfirmacao"]
        }
    },
    "propostahistorico": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["proposta_codigo"],
            # "date_columns": ["propostahistorico_data"]
        }
    },
    "propostaparcela": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["proposta_codigo", "propostaparcela_codigo"],
            # "date_columns": ["propostaparcela_dataCriacao", "propostaparcela_dataVencimento",  "propostaparcela_dataAlteracao"]
        }
    },
    "produto": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["produto_codigo"]
        }
    },
    "produtomarca": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["produto_codigo", "produtomarca_marcacod"]
        }
    },
    "produtoestoque": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "produtoestoquelocalizacao": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "produtoestoquemoeda": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "produtoreferencia": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "ramoatividade": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "rel_Veiculos": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "segmentomercado": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "tabelapreco": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "tipodocumento": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["tipodocumento_codigo"],
            "date_columns": ["tipodocumento_dataCriacao", "tipodocumento_dataAlteracao"]
        }
    },
    "tipoendereco": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["tipoendereco_codigo"],
            "date_columns": ["tipoendereco_dataCriacao", "tipoendereco_dataAlteracao"]
        }
    },
    "tipopagproposta": {
        "times_per_day": 3,
        "kwargs": {            
        }
    },
    "tipoproduto": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["tipoproduto_codigo"],
            "date_columns": ["tipoproduto_dataCriacao", "tipoproduto_dataAlteracao"]
        }
    },
    "tipoos": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["tipoos_codigo"],
            "date_columns": ["tipoos_dataCriacao", "tipoos_dataAlteracao"]
        }
    },
    "tiposervico": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["tiposervico_codigo"],
            "date_columns": ["tiposervico_dataCriacao", "tiposervico_dataAlteracao"]
        }
    },
    "tipoosemp": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "tipotitulo": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "titulo": {
        "times_per_day": 1,
        "kwargs": {
            # "id_columns": ["titulo_codigo"],
            # "date_columns": ["titulo_dataEmissao"]
        }
    },
    "tmo": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["tmo_codigo"],
            "date_columns": ["tmo_dataCriacao", "tmo_dataAlteracao"]
        }
    },
    "tributo": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "usuario": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["usuario_codigo"],
            "date_columns": ["usuario_dataCriacao", "usuario_dataAlteracao"]
        }
    },
    "usuarioperfilacesso": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculo": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["veiculo_codigo"],
            "date_columns": ["veiculo_dataCriacao", "veiculo_dataAlteracao"]
        }
    },
    "veiculoano": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["veiculoano_codigo"],
            "date_columns": ["veiculoano_dataCriacao", "veiculoano_dataAlteracao"]
        }
    },
    "veiculobloqueio": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculoestoque": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculomovimento": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculoprecousado": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "veiculotransflocal": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "vendaperdida": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "VeiculoValorAgregado": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "vendaperdidahistorico": {
        "times_per_day": 3,
        "kwargs": {
        }
    }   
    
}

tables_quiver = {
        "tabela_clientender": {
            "times_per_day": 3,
            "kwargs": {
                #"id_columns": ["Cliente", "Cliente_end"],
                #"date_columns": ["Data_inclusao", "Data_alteracao"],
                #"normal_columns": []
            }
        },
        "tabela_clientes": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["Cliente"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_clientfones": {
            "times_per_day": 3,
            "kwargs": {
            }
        },
        "tabela_documentos": {
            "times_per_day": 3,
            "kwargs": {
                #"id_columns": ["Documento", "Alteracao"],
                #"date_columns": ["Data_inclusao", "Data_alteracao"],
                #"normal_columns": []
            }
        },
        "tabela_docsparcscom": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": [],
                "date_columns": [],
                "normal_columns": []
            }
        },
        "tabela_docsparcsprem": {
            "times_per_day": 3,
            "kwargs": {
                #"id_columns": ["documento", "alteracao", "parcela"],
                #"date_columns": ["data_vencimento", "data_pagamento", "data_inclusao"],
                #"normal_columns": []
            }
        },
        "Tabela_DocsItens": {
            "times_per_day": 3,
            "kwargs": {
            }
        },
        "tabela_docsrepasses": {
            "times_per_day": 3,
            "kwargs": {
                #"id_columns": ["Documento", "Alteracao", "Nivel"],
                #"date_columns": ["Data_inclusao", "Data_alteracao"],
                #"normal_columns": ["Divisao"]
            }
        },
        "tabela_divisoes": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["Divisao"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_formasreccom": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["forma_recebimento"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_gruposhierarq": {
            "times_per_day": 3,
            "kwargs": {
                #"id_columns": ["Grupo_hierarquico"],
                #"date_columns": ["Data_inclusao", "Data_alteracao"],
                #"normal_columns": []
            }
        },
        "tabela_meiospagto": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["meio_pagto"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_niveishierarq": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["Nivel"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_produtos": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["Produto"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_ramos": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["Ramo"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_seguradoras": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["Seguradora"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_subtiposdoc": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["sub_tipo"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_tiposdocemiss": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["tipo_documento"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        },
        "tabela_tiposendereco": {
            "times_per_day": 3,
            "kwargs": {
                "id_columns": ["tipo_endereco"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
            }
        }
}

tables_syonet = {
    "tb_BD_BUDGET2": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["id_evento"],
            #"date_columns": ["dt_inc", "dt_alt"]
        }
    },
    "syo_evento": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["id_evento"],
            #"date_columns": ["dt_inc", "dt_alt"]
        }
    },
    "syo_acao": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "syo_empresa": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["id_empresa"],
            #"date_columns": ["dt_inc", "dt_alt"]
        }
    },
    "syo_usuario": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "syo_registrointerface": {
        "times_per_day": 3,
        "kwargs": {
           # "id_columns": ["id_registrointerface"],
           # "date_columns": ["dt_inc", "dt_alt"]
        }
    },
    "syo_camposregistrointerface": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["id_camposregistrointerface"],
            #"date_columns": ["dt_inc", "dt_alt"],
            #"normal_columns": ["id_registrointerface", "ds_valor", "id_campointerfacenegociacao"]
        }
    },
    "syo_motivoresultado": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["id_motivoresultado"],
            #"date_columns": ["dt_inc", "dt_alt"]
        }
    },
    "syo_tipoevento": {
        "times_per_day": 3,
        "kwargs": {            
        }
    }
}

tables_newcon = {
    "conbe001": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "conbe001": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "conbe003": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "conbe007": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_bem"]
        }
    },
    "conbi003": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "concc025": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "conco021a": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_conco021a"]
        }
    },
    "conco021i": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_conco021i","id_conco021a"]
        }
    },
    "confi002": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "confi005": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_movimento_grupo"]
        }
    },
    "confi005c": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_movimento_grupo"]
        }
    },
    "congr001": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "conve001": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_ponto_venda"]
        }
    },
    "conve002": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_cota"],
            "date_columns": ["dh_inclusao"]
        }
    },
    "conve002b": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_cota"]
        }
    },
    "conve002d": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_cota"],
            # "date_columns": ["dt_cadastro","dh_alteracao_concp016"]
        }
    },
    "conve009": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_equipe_venda"],
            "date_columns": ["dh_inclusao","dh_alteracao"]
        }
    },
    "conve014": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_comissionado","id_pessoa"]
        }
    },
    "conve034": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_conve034"]
        }
    },
    "conve041": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_plano_venda"]
        }
    },
    "corcc000": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "corcc015": {
        "times_per_day": 3,
        "kwargs": {
            "id_columns": ["id_cidade"]
        }
    },
    "corcc019": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "corcc023": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_pessoa"],
            # "date_columns": ["dt_inclusao_pep","dt_exclusao_pep"]
        }
    },
    "corcc026": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_endereco","id_pessoa"]
        }
    },
    "corcc027": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_telefone"]
        }
    },
    "corcc030": {
        "times_per_day": 3,
        "kwargs": {
            # "id_columns": ["id_pessoa"]
        }
    },
    "corcc036": {
        "times_per_day": 3,
        "kwargs": {
        }
    }
    }

tables_corpore = {
    "gccusto": {
        "times_per_day": 3,
        "kwargs": { 
            #"id_columns": ["cod_entrega"],
            #"date_columns": ["data_criacao", "data_agendada", "data_baixa", "data_cancelamento", "data_reativacao"],
            #"normal_columns": ["agendado_fora", "chassi_resumido", "cod_cliente", "cod_cliente_entrega", "cod_empresa", "cod_empresa_entrega", "cod_evento", "cod_modelo", "cod_motivo", "cod_produto", "cod_proposta", "cod_sala", "cor", "entrega_manual", "entrega_realizada", "novo_usado", "placa", "plataforma", "quem_agendou", "quem_alterou", "quem_baixou", "quem_cancelou", "quem_criou", "quem_mot_reagendamento", "quem_reativou", "recusa_checklist", "responsavel_entrega", "status", "vendedor"]
        }
    },
    "gcalend": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "gcoligada": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "gferiado": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "gfilial": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "pfrateiofixo": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "pfunc": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["codigo"],
            #"date_columns": ["reccreatedon", "recmodifiedon"],
            "normal_columns": ["codcoligada", "codfilial","chapa","codsituacao","codtipo","codsecao","codfuncao","antigasecao","codpessoa","reccreatedon","recmodifiedon"]
        }
    },
    "pfuncao": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "ppessoa": {
        "times_per_day": 3,
        "kwargs": {    
            "id_columns": ["codigo"],
            "date_columns": ["reccreatedon", "recmodifiedon"],
            "normal_columns": ["nome", "email", "cpf"]
        }
    }, 
    "psecao": {
        "times_per_day": 3,
        "kwargs": {
            #"id_columns": ["codigo"],
            #"date_columns": ["reccreatedon", "recmodifiedon"],
            "normal_columns": ["codcoligada", "codigo","descricao","cgc","secaodesativada","codfilial","codigopai","reccreatedon","recmodifiedon"]
        }
    },
    "psubstchefe": {
        "times_per_day": 3,
        "kwargs": {
        }
    }    
}

tables_koneq = {
    "device_consumption_average": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "equipments": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "model_family": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "models": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "regionals": {
        "times_per_day": 3,
        "kwargs": {
        }
    },
    "tlm_devices": {
        "times_per_day": 3,
        "kwargs": {
        }
    }
}

tables_sync = {
    "bqvw_todoschamados": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
    "atividadedochamado": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
    "statusdaatividadedochamado": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
    "tipodestatusdeatividade": {
        "times_per_day": 5,
        "kwargs": {
        }
    },
}

tables_netadmin = {

}

tables_netadmin_aud = {
    "Office365License": {
        "times_per_day": 1,
        "kwargs": {
        }
    },
    "Office365User": {
        "times_per_day": 1,
        "kwargs": {
        }
    }
}