from Global_Vars.Queries import Query

tables_view = {
    "faturamento_sistemas_silver": {
        "query": Query.View.faturamento_sistemas_silver,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {},
            "index": {
                "idx_view_faturamento_silver_doc_cli": ["doccli"],
                "idx_view_faturamento_silver_codempresa": ["codempresa"],
                "idx_view_faturamento_silver_chave_sistemaorigem": ["chave_faturamento", "sistemaorigem"]
            }
        }
    },
    "autom": {
        "query": Query.View.autom,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "icons": {
        "query": Query.View.icons,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    }
}