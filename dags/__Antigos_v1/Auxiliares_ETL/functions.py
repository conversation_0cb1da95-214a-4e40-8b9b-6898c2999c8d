import logging
from logging.handlers import <PERSON><PERSON>tingF<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from io import StringIO

import cx_Oracle
import pandas as pd
import psycopg2
import pymssql
from airflow.exceptions import AirflowSkipException, AirflowFailException
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator

from __Antigos_v1.Auxiliares_ETL.variables import *


# Configura o logger para rodar os logs
handler = RotatingFileHandler(
    'etl_log.log', maxBytes=10**7, backupCount=5, encoding='utf-8'
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[handler, logging.StreamHandler()]
)

logger = logging.getLogger(__name__)

def get_hook(dbname):
    """
    Establish source connection based on the database name.
    
    Args:
        dbname (str): The name of the database to connect to.

    Returns:
        Connection object: A connection object for the specified database.
    """
    logger.info(f"Establishing connection for database: {dbname}")
    try:
        if dbname == "syonet":
            hook = pymssql.connect(server=SYONET_HOST, user=SYONET_USERNAME, password=SYONET_PASSWORD, database=SYONET_DBNAME, port=SYONET_PORT)
        elif dbname == "newcon":
            #Trecho para conexão com os bancos Primarios e Secundarios identificando qual servidor está ativo no momento            
            try:
                logger.info(f"Trying to connect to primary server: {NEWCON_HOST_NAME}") 
                #Tentativa de conexão com o Batman
                hook = pymssql.connect(server=NEWCON_HOST, user=NEWCON_USERNAME, password=NEWCON_PASSWORD, database=NEWCON_DBNAME,port=NEWCON_PORT)
                logger.info(f"Connected to primary server: {NEWCON_HOST_NAME}")
                return hook
            except pymssql.OperationalError as e:
                logger.error(f"Error establishing connection for database: {NEWCON_HOST_NAME}", exc_info=True)
                logger.info(f"Trying to connect to secondary server: {NEWCON_HOST_NAME_2}")
                #Tentativa de conexão com o Jocker
                hook = pymssql.connect(server=NEWCON_HOST_2, user=NEWCON_USERNAME, password=NEWCON_PASSWORD, database=NEWCON_DBNAME,port=NEWCON_PORT)
                logger.info(f"Connected to secondary server: {NEWCON_HOST_NAME_2}") 
                return hook 
        elif dbname == "quiver":
            hook = pymssql.connect(server=QUIVER_HOST, user=QUIVER_USERNAME, password=QUIVER_PASSWORD, database=QUIVER_DBNAME, port=QUIVER_PORT)
        elif dbname == "sge":
            hook = pymssql.connect(server=SGE_HOST, user=SGE_USERNAME, password=SGE_PASSWORD, database=SGE_DBNAME, port=SGE_PORT)
        elif dbname == "sync":
            hook = pymssql.connect(server=SYNC_HOST, user=SYNC_USERNAME, password=SYNC_PASSWORD, database=SYNC_DBNAME, port=SYNC_PORT)
        elif dbname == "dealer":
            hook = pymssql.connect(server=DEALER_HOST, user=DEALER_USERNAME, password=DEALER_PASSWORD, database=DEALER_DBNAME, port=DEALER_PORT)
        elif dbname == "dw_bamaq":
            #Trecho para conexão com os bancos Primarios e Secundarios identificando qual servidor está ativo no momento
            try:
                logger.info(f"Trying to connect to primary server: {DW_BAMAQ_HOST_NAME}")
                #Tentativa de conexão com o Batman
                hook = pymssql.connect(server=DW_BAMAQ_HOST, user=DW_BAMAQ_USERNAME, password=DW_BAMAQ_PASSWORD, database=DW_BAMAQ_DBNAME,port=DW_BAMAQ_PORT)
                logger.info(f"Connected to primary server: {DW_BAMAQ_HOST_NAME}")
                return hook
            except pymssql.OperationalError as e:
                logger.error(f"Error establishing connection for database: {DW_BAMAQ_HOST}", exc_info=True)
                #Tentativa de conexão com o Jocker
                logger.info(f"Trying to connect to secondary server: {DW_BAMAQ_HOST_NAME_2}")
                hook = pymssql.connect(server=DW_BAMAQ_HOST_2, user=DW_BAMAQ_USERNAME, password=DW_BAMAQ_PASSWORD, database=DW_BAMAQ_DBNAME,port=DW_BAMAQ_PORT)
                logger.info(f"Connected to secondary server: {DW_BAMAQ_HOST_NAME_2}") 
                return hook               
        elif dbname == "dw_corporativo":
            hook = psycopg2.connect(dbname=DW_CORPORATIVO_DBNAME, user=DW_CORPORATIVO_USERNAME, password=DW_CORPORATIVO_PASSWORD, host=DW_CORPORATIVO_HOST)
        elif dbname == "koneq":
            hook = psycopg2.connect(dbname=KONEQ_DBNAME, user=KONEQ_USERNAME, password=KONEQ_PASSWORD, host=KONEQ_HOST,port=KONEQ_PORT)
        elif dbname == "corpore":
            hook = pymssql.connect(server=CORPORE_HOST, user=CORPORE_USERNAME, password=CORPORE_PASSWORD, database=CORPORE_DBNAME, port=CORPORE_PORT)
        elif dbname == "netadmin":
            hook = pymssql.connect(server=NETADMIN_HOST, user=NETADMIN_USERNAME, password=NETADMIN_PASSWORD, database=NETADMIN_DBNAME, port=NETADMIN_PORT)
        elif dbname == "netadmin_aud":
            hook = pymssql.connect(server=NETADMIN_HOST, user=NETADMIN_USERNAME, password=NETADMIN_PASSWORD, database=NETADMIN_AUD_DBNAME, port=NETADMIN_PORT)
        elif dbname == "nbs":
            dsn_tns = cx_Oracle.makedsn(NBS_HOST, NBS_PORT, service_name=NBS_SERVICE_NAME)
            hook = cx_Oracle.connect(user=NBS_USERNAME, password=NBS_PASSWORD, dsn=dsn_tns)
        logger.info(f"Connection established for database: {dbname}")
        return hook
    except Exception as e:
        logger.error(f"Error establishing connection for database: {dbname}", exc_info=True)
        raise e

def fetch_data_in_chunks(query, hook, chunk_size=50000):
    """
    Fetch data from the database in chunks.
    
    Args:
        query (str): The SQL query to execute.
        hook (Connection object): The database connection object.
        chunk_size (int): The size of each data chunk to fetch.

    Yields:
        DataFrame: A chunk of data from the query result.
    """
    logger.info(f"Fetching data in chunks with query: {query}")
    try:
        for chunk in pd.read_sql_query(query, hook, chunksize=chunk_size):
            logger.info(f"Fetched chunk with {len(chunk)} records")
            yield chunk
    except Exception as e:
        logger.error("Error fetching data in chunks", exc_info=True)
        raise e

def insert_data_in_chunks(data, table, cur, chunk_size=10000):
    """
    Insert data into the database in chunks.

    Args:
        data (DataFrame): The data to insert.
        table (str): The target table for the data.
        cur (Cursor object): The database cursor.
        chunk_size (int): The size of each data chunk to insert.
    """
    def insert_chunk(chunk, table, cur, use_quotes=False):
        sio = StringIO()
        if use_quotes:
            for col in chunk.columns:
                if chunk[col].dtype == 'object':
                    chunk.loc[:, col] = chunk[col].apply(lambda x: f'"{x}"' if pd.notnull(x) else x)
            chunk.to_csv(sio, index=None, header=None, quotechar='"', quoting=3, escapechar='\\')
        else:
            chunk.to_csv(sio, index=None, header=None)
        sio.seek(0)
        quote_option = " QUOTE '\"'" if use_quotes else ""
        cur.copy_expert(f"COPY {table} FROM STDIN WITH CSV{quote_option}", sio)

    def insert_chunk_with_bytes_conversion(chunk, table, cur, use_quotes=False):
        for col in chunk.columns:
            if chunk[col].dtype == 'object':
                try:
                    chunk[col] = chunk[col].apply(lambda x: x.read() if isinstance(x, cx_Oracle.LOB) else x.decode('utf-8') if isinstance(x, bytes) else str(x) if x is not None else x)
                except Exception as e:
                    logger.warning(f"Failed to convert bytes in column {col}. Error: {e}")
        insert_chunk(chunk, table, cur, use_quotes)

    def clean_data(chunk):
        for col in chunk.columns:
            if chunk[col].dtype == 'object':
                chunk[col] = chunk[col].apply(lambda x: x.replace('\r', '').replace('\n', ' ') if isinstance(x, str) else x)
                chunk[col] = chunk[col].apply(lambda x: x.replace('"', '""') if isinstance(x, str) else x)

    logger.info(f"Inserting data into table: {table}")
    for i in range(0, len(data), chunk_size):
        chunk = data.iloc[i:i + chunk_size].copy()  # Create a copy to avoid SettingWithCopyWarning
        attempt_insert = True
        use_quotes = False
        attempt_count = 0
        bytes_conversion_done = False

        while attempt_insert and attempt_count < 8:
            try:
                cur.execute("SAVEPOINT before_insert")
                clean_data(chunk)
                if bytes_conversion_done:
                    bytes_conversion_done = False
                    insert_chunk_with_bytes_conversion(chunk, table, cur, use_quotes)
                    logger.info(f"Inserted chunk with {len(chunk)} records into {table} after bytes conversion.")
                else:
                    insert_chunk(chunk, table, cur, use_quotes)
                    logger.info(f"Inserted chunk with {len(chunk)} records into {table}")
                attempt_insert = False
            except psycopg2.errors.BadCopyFileFormat as e:
                logger.warning(f"Error with default insert: {str(e)}. Retrying with quoted CSV.")
                cur.execute("ROLLBACK TO SAVEPOINT before_insert")
                use_quotes = True
                attempt_count += 1
            except (psycopg2.errors.StringDataRightTruncation, psycopg2.errors.InvalidDatetimeFormat) as e:
                logger.error(f"StringDataRightTruncation encountered: {str(e)}. Attempting to alter table and retry.")
                cur.execute("ROLLBACK TO SAVEPOINT before_insert")
                error_message = str(e).split("\n")
                column_name = None
                for line in error_message:
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip()
                        break
                if column_name:
                    alter_table_query = f"ALTER TABLE {table} ALTER COLUMN {column_name} TYPE TEXT;"
                    try:
                        cur.execute(alter_table_query)
                        logger.info(f"Altered column {column_name} to TEXT in table {table}. Retrying insert.")
                    except Exception as alter_e:
                        logger.error(f"Failed to alter column: {str(alter_e)}", exc_info=True)
                        raise alter_e
                else:
                    logger.error("Could not identify the problematic column for StringDataRightTruncation error.")
                    raise e
            except psycopg2.errors.DatetimeFieldOverflow as e:
                logger.error(f"DatetimeFieldOverflow encountered: {str(e)}. Attempting to clean column and retry.")
                cur.execute("ROLLBACK TO SAVEPOINT before_insert")
                error_message = str(e).split("\n")
                column_name = None
                for line in error_message:
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip()
                        break
                if column_name:
                    if chunk[column_name].dtype == 'object':
                        chunk[column_name] = chunk[column_name].str.strip()
                        chunk[column_name] = pd.to_datetime(chunk[column_name], errors='coerce', format='%d/%m/%Y', dayfirst=True)
                    else:
                        chunk[column_name] = pd.to_datetime(chunk[column_name], errors='coerce')
                else:
                    logger.error("Could not identify the problematic column for DatetimeFieldOverflow error.")
                    raise e
            except TypeError as e:
                attempt_count += 1
                if "__str__ returned non-string (type bytes)" in str(e):
                    logger.warning(f"Encountered TypeError due to bytes. Retrying with bytes conversion.")
                    cur.execute("ROLLBACK TO SAVEPOINT before_insert")
                    bytes_conversion_done = True
                else:
                    logger.error(f"Failed to insert chunk with {len(chunk)} records into {table}", exc_info=True)
                    raise e
            except psycopg2.errors.InvalidTextRepresentation as e:
                logger.warning(f"InvalidTextRepresentation encountered: {str(e)}. Attempting to clean data and retry.")
                cur.execute("ROLLBACK TO SAVEPOINT before_insert")
                clean_data(chunk)
                insert_chunk(chunk, table, cur, use_quotes)
                logger.info(f"Inserted chunk with {len(chunk)} records into {table} after cleaning data.")
                attempt_insert = False
            except Exception as e:
                logger.error(f"Failed to insert chunk with {len(chunk)} records into {table}", exc_info=True)
                raise e

            if attempt_count >= 8:
                logger.error(f"Failed to insert chunk with {len(chunk)} records into {table} after {attempt_count} attempts.")
                raise RuntimeError(f"Max attempts reached for inserting chunk with {len(chunk)} records into {table}")

def delete_data_in_chunks(data, table, cur, id_columns, chunk_size=10000):
    """
    Delete data from the database in chunks.
    
    Args:
        data (DataFrame): The data containing IDs to delete.
        table (str): The target table for deletion.
        cur (Cursor object): The database cursor.
        id_columns (list): List of column names used as identifiers.
        chunk_size (int): The size of each data chunk to process.
    """
    logger.info(f"Deleting data from table: {table}")
    data.columns = data.columns.str.lower()
    id_columns = [col.lower() for col in id_columns]
    available_columns = data.columns.tolist()
    logger.info(f"Available columns in DataFrame: {available_columns}")
    missing_columns = [col for col in id_columns if col not in data.columns]
    
    if missing_columns:
        raise KeyError(f"Missing columns in DataFrame: {missing_columns}")

    for i in range(0, len(data), chunk_size):
        chunk = data.iloc[i:i + chunk_size]
        where_clause_parts = []

        if len(id_columns) > 1:
            for _, row in chunk.iterrows():
                row_where_parts = []
                for col in id_columns:
                    if col in row:
                        row_where_parts.append(f"{col} = '{row[col]}'")
                    else:
                        raise KeyError(f"Column '{col}' not found in DataFrame row")
                where_clause_parts.append(f"({' AND '.join(row_where_parts)})")
            where_clause = " OR ".join(where_clause_parts)
        else:
            col = id_columns[0]
            if col in chunk:
                ids_to_delete = ', '.join(f"'{str(id)}'" for id in chunk[col].tolist())
                where_clause = f"{col} IN ({ids_to_delete})"
            else:
                raise KeyError(f"Column '{col}' not found in DataFrame")
        
        query = f"DELETE FROM {table} WHERE {where_clause}"
        logger.info(f"Executing delete query: {query}")
        cur.execute(query)
        logger.info(f"Deleted chunk with {len(chunk)} records from {table}")

def get_max_value(id_columns, date_columns, schema, table_name, cur):
    """
    Get the maximum value of the specified column from the table.
    
    Args:
        id_columns (list): List of ID column names.
        date_columns (list): List of date column names.
        schema (str): The database schema.
        table_name (str): The target table name.
        cur (Cursor object): The database cursor.

    Returns:
        str: The maximum value of the specified column.
    """
    logger.info(f"Fetching max value for table: {schema}.{table_name}")
    try:
        column = date_columns[0] if date_columns else id_columns[0]
        query = f"SELECT MAX({column}) FROM {schema}.{table_name}"
        logger.info(f"Executing query: {query}")
        cur.execute(query)
        data = cur.fetchone()
        max_value = data[0] if data else None
        logger.info(f"Max value for column {column} in table {schema}.{table_name}: {max_value}")
        return max_value
    except Exception as e:
        logger.error(f"Error fetching max value for table: {schema}.{table_name}", exc_info=True)
        return None

def create_query(dbname, table, normal_columns, id_columns, date_columns, last_max_value, query_type="full-query"):
    """
    Create SQL query for data extraction.
    
    Args:
        dbname (str): The name of the database.
        table (str): The target table name.
        normal_columns (list): List of normal column names.
        id_columns (list): List of ID column names.
        date_columns (list): List of date column names.
        last_max_value (str): The last maximum value for incremental loading.
        query_type (str): The type of query ("full-query" or "sample").

    Returns:
        str: The created SQL query.
    """
    logger.info(f"Creating query for table: {table} in database: {dbname}")
    date_columns = date_columns if date_columns else []
    id_columns = id_columns if id_columns else []
    normal_columns = normal_columns if normal_columns else []
    
    columns = id_columns + date_columns + normal_columns if normal_columns else ["*"]
    if query_type == "sample" and dbname in ('quiver', 'syonet', 'dealer', 'newcon', 'dwbamaq'):
        query = f"SELECT top(1) {', '.join(columns)} FROM {table}" 
    else:
        query = f"SELECT {', '.join(columns)} FROM {table}"

    if id_columns and last_max_value:
        where_clause = []
        if date_columns:
            formatted_value = (f"TO_DATE('{last_max_value}', 'YYYY-MM-DD HH24:MI:SS')" if dbname == "nbs" else
                               f"'{int(last_max_value)}'" if dbname == "syonet" and table != "syo_empresa" else
                               f"CAST('{last_max_value}'AS DATE)" if dbname == "newcon" else
                               f"'{last_max_value}'")
            where_clause = [f"{col} >= {formatted_value}" for col in date_columns]
        else:
            where_clause = [f"{id_columns[0]} > {last_max_value}"]
        query += f" WHERE {' OR '.join(where_clause)}"

    if query_type == "sample" and dbname in ('dw_coporativo'):
        query += " LIMIT 1"
    elif query_type == "sample" and dbname in ('nbs'):
        query += " FETCH FIRST 1 ROW ONLY"

    logger.info(f"Query created: {query}")
    return query

def dataframe_to_create_table_query(df, table_name, column_types):
    """
    Create a SQL query to create a table based on a DataFrame.
    
    Args:
        df (DataFrame): The DataFrame to base the table structure on.
        table_name (str): The name of the table to create.
        column_types (dict): Optional dictionary of column types.

    Returns:
        str: The SQL query to create the table.
    """
    # Mapeia os tipos de dados do pandas para os tipos de dados do PostgreSQL
    pg_data_types = {
        'int64': 'double precision',
        'float64': 'double precision',
        'object': 'text',
        'datetime64[ns]': 'timestamp',
        'bool': 'varchar(15)'
    }

    # Inicializa a lista de colunas
    columns = []

    # Itera sobre as colunas do DataFrame
    for col_name, col_type in df.dtypes.items():
        # Verifica se a coluna está no dicionário column_types
        if column_types and col_name.lower() in column_types:
            pg_type = column_types[col_name.lower()]
        elif col_name.lower().startswith("data_"):
            pg_type = "timestamp"
        else:
            # Aplica a lógica padrão de mapeamento
            if col_type == 'object':
                # Trata valores nulos e converte para string
                col_data = df[col_name].fillna('').apply(lambda x: str(x).encode('utf-8', errors='ignore').decode('utf-8'))
                
                # Checa se a coluna realmente suporta operações de string
                if col_data.apply(lambda x: isinstance(x, str)).all():
                    # Calcula o comprimento máximo
                    max_length = col_data.str.len().max()
                    if max_length <= 100:
                        pg_type = 'varchar(100)'
                    elif max_length <= 150:
                        pg_type = 'varchar(150)'
                    elif max_length <= 200:
                        pg_type = 'varchar(200)'
                    else:
                        pg_type = 'text'
                else:
                    pg_type = 'text'  # Se houver dados não compatíveis, define como 'text'
            else:
                pg_type = pg_data_types.get(str(col_type), 'text')  # Default para texto se o tipo não for mapeado

            # Verifica se a coluna deve ser sempre 'text'
            if col_name.lower() in text_columns:
                pg_type = 'text'

        columns.append(f'{col_name.replace(" ", "")} {pg_type}')

    # Junta todas as colunas em uma string separada por vírgulas
    columns_str = ",\n    ".join(columns)
    # Monta a query completa para criar a tabela
    create_table_query = f"DROP TABLE IF EXISTS {table_name}; \nCREATE TABLE {table_name} (\n    {columns_str}\n);\n"

    for user in all_grants:
        create_table_query += f"grant all on table {table_name} to {user};\n"
    
    if "koneq" in table_name:
        for user in specific_grants:
            create_table_query += f"grant all on table {table_name} to {user};\n"
    elif "gold_chamados_formalizacao_credito_bamaq_assinatura" in table_name:
        for user in specific_grants_sync_tables:
            create_table_query += f"grant all on table {table_name} to {user};\n"
        
    return create_table_query

def compare_and_recreate_table(df, table_name, cur, schema, kwargs, stage):
    """
    Compare and recreate table if structure differs from DataFrame.
    
    Args:
        df (DataFrame): The DataFrame to compare with the table structure.
        table_name (str): The name of the table to compare.
        cur (Cursor object): The database cursor.
        schema (str): The database schema.
        column_types (dict): Optional dictionary of column types.
        stage (str): The ETL stage.
    """
    table_name = table_name.lower()
    logger.info(f"Comparing and potentially recreating table: {schema}.{table_name}")
    try:
        if stage in ('silver', 'gold', 'view'):
            if kwargs.get("index"):
                for idx_name, idx_columns in kwargs.get("index").items():
                    if idx_name and idx_columns:
                        check_query = f"""
                        SELECT EXISTS (
                            SELECT 1
                            FROM pg_indexes 
                            WHERE schemaname = '{schema}' 
                            AND tablename = '{table_name}' 
                            AND indexname = '{idx_name}'
                        );
                        """
                        cur.execute(check_query)
                        index_exists = cur.fetchone()[0]
                        
                        if index_exists:
                            idx_query = f"DROP INDEX {schema}.{idx_name};"
                            try:
                                cur.execute(idx_query)
                                logger.info(f"Successfully dropped index with: {idx_query}")
                            except Exception as e:
                                logger.error(f"Failed to drop index with: {idx_query}. Error: {e}")

        query = f"SELECT column_name FROM information_schema.columns WHERE table_name='{table_name}' AND table_schema='{schema}';"
        existing_columns = pd.read_sql_query(query, cur.connection)
        existing_columns_list = [col.lower() for col in existing_columns['column_name'].tolist()]
        new_columns_list = [col.lower() for col in df.columns.tolist()]
        same_structure = existing_columns_list == new_columns_list
        logger.info(f"Existing columns: {existing_columns_list}")
        logger.info(f"New columns: {new_columns_list}")

        if not same_structure:
            logger.info(f"Structure mismatch detected for table: {schema}.{table_name}")
            create_table_query = dataframe_to_create_table_query(df, f"{schema}.{table_name}", kwargs.get("column_types"))
            logger.info(f"Recreating table with statement:\n{create_table_query}")
            cur.execute(create_table_query)

        elif stage in ('silver', 'gold', 'view'):
            cur.execute(f'truncate table {schema}.{table_name}')

    except Exception as e:
        logger.error(f"Error comparing and recreating table: {schema}.{table_name}", exc_info=True)
        raise e

def etl_task(query, dbname, origin_table, target_table, schema, kwargs, stage):
    """
    Perform ETL task for a given table.
    
    Args:
        query (str): The SQL query to execute.
        dbname (str): The name of the source database.
        origin_table (str): The name of the source table.
        target_table (str): The name of the target table.
        schema (str): The database schema.
        kwargs (dict): Additional arguments.
        stage (str): The ETL stage.
    """
    logger.info(f"Starting ETL task for table: {target_table}")
    source_hook = get_hook(dbname)
    target_conn = get_hook('dw_corporativo')

    try:
        with target_conn:
            with target_conn.cursor() as cur:
                def handle_bronze_stage():
                    last_max_value = None
                    sample_query = create_query(dbname, origin_table, kwargs.get("normal_columns"), kwargs.get("id_columns"), kwargs.get("date_columns"), last_max_value, "sample")
                    data_sample = pd.read_sql_query(sample_query, source_hook)
                    compare_and_recreate_table(data_sample, target_table, cur, schema, kwargs, stage)
                    
                    if kwargs.get("id_columns"):
                        last_max_value = get_max_value(kwargs.get("id_columns"), kwargs.get("date_columns"), schema, target_table, cur)

                    cont_origin = pd.read_sql_query(f'select count(*) from {origin_table}', source_hook).iloc[0, 0]
                    
                    return create_query(dbname, origin_table, kwargs.get("normal_columns"), kwargs.get("id_columns"), kwargs.get("date_columns"), last_max_value), last_max_value, cont_origin

                def handle_freeze_stage():
                    delete_query = f"""
                        DELETE FROM {schema}.{target_table} 
                        WHERE 
                            EXTRACT(MONTH FROM {kwargs.get("data_ref")}) = EXTRACT(MONTH FROM CURRENT_DATE - INTERVAL '1 day') 
                            AND EXTRACT(YEAR FROM {kwargs.get("data_ref")}) = EXTRACT(YEAR FROM CURRENT_DATE - INTERVAL '1 day');
                    """

                    cur.execute(delete_query)

                if stage == "bronze":
                    query, last_max_value, cont_origin = handle_bronze_stage()
                else:
                    last_max_value = None
                
                chunk_iterator = fetch_data_in_chunks(query, source_hook)
                initial_chunk = next(chunk_iterator)
                
                if stage in ("silver", "gold", "view") or (stage == "freeze" and kwargs.get("auto_create") == "yes"):
                    compare_and_recreate_table(initial_chunk, target_table, cur, schema, kwargs, stage)
                
                if stage == "freeze" and kwargs.get("data_ref"):
                    handle_freeze_stage()
                
                if stage == "bronze":
                    if last_max_value:
                        delete_data_in_chunks(initial_chunk, f'{schema}.{target_table}', cur, kwargs.get("id_columns"))
                    else:
                        cur.execute(f'truncate {schema}.{target_table}')

                insert_data_in_chunks(initial_chunk, f'{schema}.{target_table}', cur)
                
                for chunk in chunk_iterator:
                    insert_data_in_chunks(chunk, f'{schema}.{target_table}', cur)
                    if stage == "bronze" and last_max_value:
                        delete_data_in_chunks(chunk, f'{schema}.{target_table}', cur, kwargs.get("id_columns"))

                if kwargs.get("index"):
                    for idx_name, idx_columns in kwargs.get("index").items():
                        if idx_name and idx_columns:
                            idx_query = f"CREATE INDEX {idx_name} ON {schema}.{target_table} ({', '.join(idx_columns)});"
                            try:
                                cur.execute(idx_query)
                                logger.info(f"Successfully created index with: {idx_query}")
                            except Exception as e:
                                logger.error(f"Failed to create index with: {idx_query}. Error: {e}")

                if stage == "bronze":
                    cont_destin = pd.read_sql_query(f'select count(*) from {schema}.{target_table}', target_conn).iloc[0, 0]

                    cur.execute(f"""
                        DELETE FROM dbdwcorporativo.contagem_linhas_etl WHERE tabela = '{target_table}'
                    """)

                    # Convertendo para tipos nativos do Python
                    cont_origin = int(cont_origin) if cont_origin else None
                    cont_destin = int(cont_destin) if cont_destin else None
                    dif_origem = cont_origin - cont_destin if cont_origin and cont_destin else None
                    tipo_merge = "date_merge" if kwargs.get("date_columns") and kwargs.get("id_columns") else \
                                 "id_merge" if kwargs.get("id_columns") else None

                    cur.execute("""
                        INSERT INTO dbdwcorporativo.contagem_linhas_etl 
                        (data_ref, schema, tabela, contagem_dw, contagem_origem, diferenca_origem, tipo_merge) 
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """, (datetime.now(), schema, target_table, cont_destin, cont_origin, dif_origem, tipo_merge))

    except Exception as e:
        target_conn.rollback()  # Rollback in case of error
        logger.error(f"Error in ETL task for table: {target_table}", exc_info=True)
        raise e
    
    finally:
        source_hook.close()
        target_conn.close()
        logger.info(f"Completed ETL task for table: {schema}.{target_table}")

def create_ext_tasks(dag, schema, stage_tables):
    """
    Create Airflow tasks for the ETL process.
    
    Args:
        dag (DAG): The Airflow DAG object.
        schema (str): The database schema.
        stage_tables (dict): Dictionary of tables and their configurations for the stage.

    Returns:
        Tuple: Start and end Airflow tasks for the ETL process.
    """
    start_ext_task = EmptyOperator(task_id='start_etl', dag=dag)
    end_ext_task = EmptyOperator(task_id='end_etl', dag=dag, trigger_rule='all_done')

    def skip_task_message():
        raise AirflowSkipException("Task skipped as it was not scheduled for this time.")

    stage = "bronze" if stage_tables == stg_bronze_tables else \
            "silver" if stage_tables == stg_silver_tables else  \
            "gold" if stage_tables == stg_gold_tables else  \
            "view" if stage_tables == stg_view_tables else  \
            "freeze" if stage_tables == stg_freeze_tables else None

    for table_name, table_info in stage_tables.items():
        task_id = f'{stage}_{schema}_{table_name.lower()}' 
        current_hour = (datetime.now().hour - 3) % 24

        if ((table_info.get("times_per_day") == 1 or stage_tables == stg_freeze_tables) and current_hour in (0, 1, 2, 3)) or \
           (table_info.get("times_per_day") == 2 and current_hour in (0, 1, 2, 3, 12, 13)) or \
           (table_info.get("times_per_day") == 3 and current_hour in (0, 1, 2, 3, 10, 11, 14, 15)) or \
           (table_info.get("times_per_day") == 4 and current_hour in (0, 1, 2, 3, 10, 11, 12, 13, 16, 17)) or \
           table_info.get("times_per_day") == 5:

            task = PythonOperator(
                task_id=task_id,
                python_callable=etl_task,
                op_args=[table_info.get("query"), 
                         table_info.get("database"), 
                         table_name.replace(f"{table_info.get('database')}_", ""),
                         f'{stage}_{table_name.lower()}', 
                         schema, table_info.get("kwargs"), stage],
                trigger_rule='all_done',
                dag=dag
            )
        else:
            task = PythonOperator(
                task_id=task_id,
                python_callable=skip_task_message,
                trigger_rule='all_done',
                dag=dag                
            )
            
        start_ext_task >> task >> end_ext_task

    return start_ext_task, end_ext_task

def check_failed_tasks(**kwargs):
    dag_run = kwargs['dag_run']
    dag_id = kwargs['dag'].dag_id
    task_instances = dag_run.get_task_instances()
    
    failed_tasks = [ti for ti in task_instances if ti.state == 'failed']
    
    if failed_tasks and kwargs.get("schema").lower() == "dbdwcorporativo":
        failed_task_names = [task.task_id for task in failed_tasks]

        description = (
            f"Failed Tasks: {', '.join(failed_task_names)}\n"
        )

        # Truncate the description to 4000 characters
        if len(description) > 4000:
            description = description[:4000]

        # Log para debugging
        logging.info(f"Falha nas seguintes tasks: {description}")

        # Executa a procedure no banco de dados
        try:
            hook = get_hook('sync')
            conn = hook  # Use a conexão diretamente
            cur = conn.cursor()

            # Escapando caracteres especiais para a string da descrição
            escaped_description = description.replace("'", "''")

            cur.execute(f"EXEC bqpr_SyncAPI_AberturaChamado_FalhaExecucaoDagAirflow '{dag_id}', '{escaped_description}'")
            conn.commit()
            cur.close()
        except Exception as e:
            logging.error(f"Erro ao executar a procedure: {e}")
