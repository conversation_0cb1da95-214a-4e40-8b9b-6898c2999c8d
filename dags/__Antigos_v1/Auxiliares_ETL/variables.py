from __Antigos_v1.Auxiliares_ETL.stg_bronze_tables import *
from __Antigos_v1.Auxiliares_ETL.stg_silver_tables import *
from __Antigos_v1.Auxiliares_ETL.stg_gold_tables import *
from __Antigos_v1.Auxiliares_ETL.stg_view_tables import *
from __Antigos_v1.Auxiliares_ETL.stg_freeze_tables import *
from __Antigos_v1.Auxiliares_ETL.models import *
from Global_Vars.Conn_vars import *

def add_database(dict_tables, database):
    updated_dict_tables = {}
    for table, table_info in dict_tables.items():
        table_info["database"] = database
        new_table_name = table
        if table_info["database"] != 'dw_corporativo':
            new_table_name = f'{database}_{table}'
        updated_dict_tables[new_table_name] = table_info
    return updated_dict_tables

# Querys para o ETL do stage bronze
stg_bronze_tables = {
    **add_database(tables_nbs, 'nbs'),
    **add_database(tables_dealer, 'dealer'),
    **add_database(tables_quiver, 'quiver'),
    **add_database(tables_syonet, 'syonet'),
    **add_database(tables_dw_bamaq, 'dw_bamaq'),
    **add_database(tables_newcon, 'newcon'),
    **add_database(tables_corpore, 'corpore'),
    **add_database(tables_koneq, 'koneq'),
    **add_database(tables_sync, 'sync'),
    **add_database(tables_netadmin, 'netadmin'),
    **add_database(tables_netadmin_aud, 'netadmin_aud')
}

# Querys para o ETL do stage silver
stg_silver_tables = {
    **add_database(tables_direct_quiver, 'quiver'),
    **add_database(tables_direct_dealer, 'dealer'),
    **add_database(tables_direct_dw_bamaq, 'dw_bamaq'),
    **add_database(tables_direct_nbs, 'nbs'),
    **add_database(tables_direct_newcon, 'newcon'),
    **add_database(tables_direct_syonet, 'syonet'),
    **add_database(tables_direct_corpore, 'corpore'),
    **add_database(tables_silver_dealer, 'dw_corporativo'),
    **add_database(tables_silver_quiver, 'dw_corporativo'),
    **add_database(tables_silver_dw_bamaq, 'dw_corporativo'),
    **add_database(tables_silver_nbs, 'dw_corporativo'),
    **add_database(tables_silver_syonet, 'dw_corporativo'),
    **add_database(tables_silver_corpore, 'dw_corporativo'),
    **add_database(tables_silver_mef, 'dw_corporativo'),
    **Index.generate_silver_queries()
}

# Querys para o ETL do stage gold
stg_gold_tables = {
    **add_database(tables_gold, 'dw_corporativo'),
    **add_database(tables_gold_sge, 'sge')
}

# Querys para o ETL do stage view
stg_view_tables = {
    **add_database(tables_view, 'dw_corporativo')
}

# Querys para o ETL de congelamento de dados
stg_freeze_tables = {
    **add_database(tables_freeze_nbs, 'nbs'),
    **add_database(tables_freeze_dealer, 'dealer'),
    **add_database(tables_freeze_newcon, 'newcon'),
    **add_database(tables_freeze_dw_corporativo, 'dw_corporativo'),
    **add_database(tables_freeze_syonet, 'syonet')
}

# Usuarios com permissoes gerais nas tabelas
all_grants = ["master", "iebt_daniel_laranjo", "ext_leticia_furletti", "bq_dwcorporativo_u", "ext_rafael_neto"]
specific_grants = ["bq_reinilson_silva"]
specific_grants_sync_tables = ["bq_im_powerbi_u","bq_lucas_ulhoa_u","bq_vitor_barros_u"]

# Colunas que devem ser criada como tipo texto
text_columns = []