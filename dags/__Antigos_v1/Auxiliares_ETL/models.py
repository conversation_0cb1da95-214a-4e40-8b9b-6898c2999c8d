class Index:
    index = []  # Armazena todas as instâncias de Index e subclasses

    def __init__(self, **kwargs):
        self.silver = kwargs.get('silver', None)  # Defina o sistema caso precise
        self.times_per_day_val = 3  # Valor padrão
        self.column_types_val = {}  # Dicionário vazio para tipos de colunas
        Index.index.append(self)  # Adiciona cada nova instância à lista

    def times_per_day(self, times):
        # Atualiza o atributo times_per_day_val na instância correta
        for i in Index.index:
            if getattr(i, 'silver', None) == self.silver:
                i.times_per_day_val = times
        return self

    def column_types(self, column_types):
        # Atualiza o atributo column_types_val na instância correta
        for i in Index.index:
            if getattr(i, 'silver', None) == self.silver:
                i.column_types_val = column_types
        return self

    @classmethod
    def generate_bronze_queries(cls):
        queries_by_sys = {}
        for instance in cls.index:
            if isinstance(instance, Bronze):
                for table in instance.tables:
                    table_name = f"dbdwcorporativo.bronze_{instance.sys}_{table['name']}"
                    columns = ', '.join(table['columns']) if table['columns'] != '*' else '*'
                    query = f"SELECT {columns} FROM {table_name}"
                    
                    if instance.sys not in queries_by_sys:
                        queries_by_sys[instance.sys] = []
                    queries_by_sys[instance.sys].append(query)

        return queries_by_sys

    @classmethod
    def generate_silver_queries(cls):
        queries_by_destination = {}
        column_sets_by_destination = {}

        for instance in cls.index:
            if isinstance(instance, Silver):
                if instance.to_table not in column_sets_by_destination:
                    column_sets_by_destination[instance.to_table] = set(instance.column_dict.keys())
                else:
                    column_sets_by_destination[instance.to_table].update(instance.column_dict.keys())

        for instance in cls.index:
            if isinstance(instance, Silver):
                complete_column_set = column_sets_by_destination[instance.to_table]
                columns = ", ".join([
                    f"{instance.column_dict.get(col, 'NULL')}::text AS {col}"
                    for col in complete_column_set
                ])
                query = f"SELECT {columns}, '{instance.sys}' as sistema FROM {instance.from_table}"
                
                if instance.to_table not in queries_by_destination:
                    queries_by_destination[instance.to_table] = []
                queries_by_destination[instance.to_table].append(query)

        union_queries = {}
        for to_table, queries in queries_by_destination.items():
            union_query = " UNION ALL ".join(queries)
            index_instance = next((i for i in cls.index if isinstance(i, Silver) and i.to_table == to_table), None)
            if index_instance:
                union_queries[to_table] = {
                    "query": union_query,
                    "times_per_day": index_instance.times_per_day_val, 
                    "kwargs": {"column_types": index_instance.column_types_val},
                    "database": "dw_corporativo"
                }
        return union_queries

class Silver(Index):
    def __init__(self, to_table):
        super().__init__(silver=to_table)
        self.to_table = to_table

    def __str__(self):
        return self.to_table

    def sys(self, sys):
        self.sys = sys
        return self     

    def table(self, from_table):
        self.from_table = f'dbdwcorporativo.bronze_{self.sys}_{from_table}'
        return self 

    def columns(self, column_dict):
        self.column_dict = column_dict
        return self 

class Bronze(Index):
    def __init__(self, sys):
        super().__init__()  
        self.sys = sys
        self.tables = []

    def add_tables(self, table_list):
        for table in table_list:
            if isinstance(table, dict):
                table_name = table.get('name')
                columns = table.get('columns', '*')
            else:
                table_name = table
                columns = '*'
            
            self.tables.append({
                'name': table_name,
                'columns': columns
            })
        return self

try:
    # Criando instâncias de Silver e configurando com Index
    Silver('modelos').sys('nbs').table('produtos_modelos').columns({
        'id': 'cod_modelo',
        'descricao': 'descricao_modelo',
        'novo_usado': 'linha'
    })
    Silver('modelos').sys('dealer').table('modeloveiculo').columns({
        'id': 'modeloveiculo_codigo',
        'descricao': 'modeloveiculo_descricao',
        'marca': 'modeloveiculo_modelomarca',
        'ativo': 'modeloveiculo_ativo'
    })

    Silver('veiculos').sys('dealer').table('veiculo').columns({
        'veiculocod': 'Veiculo_Codigo',
        'dataentrega': 'veiculo_dataentrega',
        'COD_MODELO': 'Veiculo_ModeloVeiculoCod',
        'Chassi_completo': 'Veiculo_Chassi'
    })
    Silver('veiculos').sys('nbs').table('veiculos').columns({
        'COD_EMPRESA': 'COD_EMPRESA',
        'data_entrada': 'data_entrada',
        'data_venda': 'data_venda',
        'COD_PRODUTO': 'COD_PRODUTO',
        'COD_MODELO': 'COD_MODELO',
        'CHASSI_RESUMIDO': 'CHASSI_RESUMIDO',
        'Chassi_completo': 'Chassi_completo',
        'novo_usado': 'novo_usado',
        'valor_vendido': 'valor_vendido',
        'custo_total_final': 'custo_total_final',
        'veiculo_pago': 'veiculo_pago',
        'nota_fabrica': 'nota_fabrica',
        'total_nota_fabrica': 'total_nota_fabrica',
        'com_final_gerente': 'com_final_gerente',
        'com_final_vendedor': 'com_final_vendedor',
        'com_final_terceiros': 'com_final_terceiros',
        'desagio': 'desagio'
    })

    Silver('clientes').sys('dealer').table('pessoa').columns({
        'cod_cliente': 'pessoa_codigo',
        'nm_cliente': 'pessoa_nome',
        'email': 'pessoa_email'
    })
    Silver('clientes').sys('nbs').table('clientes').columns({
        'cod_cliente': 'cod_cliente',
        'nm_cliente': 'nome',
        'email': 'ENDERECO_ELETRONICO'
    }).where('cod empresa > 10')

    Silver('vendas').sys('dealer').table('notafiscal').columns({
        'nf_numero': 'notafiscal_numero',
        'nf_status': 'notafiscal_status',
        'nf_total': 'notafiscal_valortotal'
    })
    Silver('vendas').sys('nbs').table('vendas').columns({
        'nf_numero': 'numero_nf_eletronica',
        'nf_status': 'status',
        'nf_total': 'total_nota'
    })

    Index(silver="modelos").times_per_day(5).column_types({
        'id': 'NUMERIC',
        'descricao': 'TEXT',
        'novo_usado': 'VARCHAR(10)',
        'marca': 'VARCHAR(40)',
        'ativo': 'VARCHAR(10)'
    })
    Index(silver="veiculos").times_per_day(5)
    Index(silver="clientes").times_per_day(5)
    Index(silver="vendas").times_per_day(5)

    # print(Index.generate_silver_queries())
except Exception as e:
    print(f"Erro na construção do index: {e}")

if __name__ == "__main__":
    # Criando várias instâncias de `Bronze` com tabelas e colunas específicas
    Bronze('nbs').add_tables([
        'vendas',  
        {'name': 'modelo', 'columns': ['id', 'descricao', 'usuario']},  
        {'name': 'venda_itens', 'columns': ['id', 'tipo', 'usuario']}  
    ])
    Bronze('dealer').add_tables([
        'vendas', 
        {'name': 'modelo', 'columns': ['id', 'descricao', 'usuario']},  
        {'name': 'venda_itens', 'columns': ['id', 'tipo', 'usuario']}  
    ])

    # Criando várias instâncias de `Silver`
    Silver('modelo').sys('nbs').table('modelo').columns({
        'id': 'id',
        'data': 'emissao',
        'descricao': 'modelo_descricao',
        'grupo': 'grupo_modelo'
    })
    Silver('modelo').sys('dealer').table('modeloveiculo').columns({
        'id': 'id',
        'data': 'emissao',
        'descricao': 'modelo_descricao',
        'usuario': 'usuario'
    })
    Silver('vendas').sys('nbs').table('vendas').columns({
        'id': 'nf_numero',
        'data': 'data'
    })
    Silver('vendas').sys('dealer').table('notafiscal').columns({
        'id': 'notafiscal_numero',
        'data': 'notafiscal_data',
        'usuario': 'usuario'
    })

    # Exibindo todas as consultas geradas para `Bronze` com agrupamento por sistema
    # print(Index.generate_bronze_queries())

    # Exibindo todas as consultas geradas para `Silver`
    # print(Index.generate_silver_queries())
