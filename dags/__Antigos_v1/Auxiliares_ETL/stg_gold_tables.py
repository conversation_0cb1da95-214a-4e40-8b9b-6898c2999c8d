from Global_Vars.Queries import Query

tables_gold = {
     "faturamento": {
        "query": Query.Gold.faturamento,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "quiver_cgf_producao": {
        "query": Query.Gold.quiver_cgf_producao,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "quiver_cgf_renovacoes": {
        "query": Query.Gold.quiver_cgf_renovacoes,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "quiver_cgf_seguros_automotivos": {
        "query": Query.Gold.quiver_cgf_seguros_automotivos,
        "times_per_day": 4,
        "kwargs": {
            "column_types": {}
        }
    },
    "syonet_cgf_vendas": {
        "query": Query.Gold.syonet_cgf_vendas,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "syonet_cgf_renovacoes": {
        "query": Query.Gold.syonet_cgf_renovacoes,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "autom_vendas": {
        "query": Query.Gold.autom_vendas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "financeiro_sistemas": {
        "query": Query.Gold.financeiro_sistemas,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {},
            "index": {
                "idx_gold_financeiro_sistemas_doc_cli": ["doccli"]
            }
        }
    },
    "faturamento_sistemas": {
        "query": Query.Gold.faturamento_sistemas,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {},
            "index": {
                "idx_gold_faturamento_sistemas_doc_cli": ["doccli"],
                "idx_gold_faturamento_sistemas_codempresa": ["codempresa"],
                "idx_gold_faturamento_sistemaorigem": ["sistemaorigem"]
            }
        }
    },
    "formapagto_faturamento_sistemas": {
        "query": Query.Gold.formapagto_faturamento_sistemas,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {},
            "index": {
                "idx_gold_formapagto_faturamento_sistemas_formapagto" : ["forma_pagto"],
                "idx_gold_formapagto_faturamento_sistemas_tiporecurso" : ["tipo_recurso"],
                "idx_gold_formapagto_faturamento_sistemas_doccli" : ["doccli"],
                "idx_gold_formapagto_faturamento_sistemas_chave_sistemaorigem" : ["chave_faturamento","sysorigem"]
            }
        }
    },
    "clientes_sistemas": {
        "query": Query.Gold.clientes_sistemas,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {},
            "index": {
                "idx_gold_clientes_sistemas_doccli": ["doccli"]
            }
        }
    },
    "clientes_sistemas_cadunico": {
        "query": Query.Gold.clientes_sistemas_cadunico,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {},
            "index": {
                "idx_gold_clientes_sistemas_cadunico_doc_cli": ["doccli"]
            }
        }
    },
    "autom_pecas_balcao": {
        "query": Query.Gold.autom_pecas_balcao,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "autom_pecas_detalhamento": {
        "query": Query.Gold.autom_pecas_detalhamento,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "autom_servicos": {
        "query": Query.Gold.autom_servicos,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "autom_pecas_oficina": {
        "query": Query.Gold.autom_pecas_oficina,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "autom_veiculos": {
        "query": Query.Gold.autom_veiculos,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "calendario_projetado": {
        "query": Query.Gold.calendario_projetado,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
     },
    "consorcio_consolidado": {
        "query": Query.Gold.consorcio_consolidado,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_gerencial_veiculos": {
        "query": Query.Gold.controladoria_gerencial_veiculos,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_gerencial_pecas": {
        "query": Query.Gold.controladoria_gerencial_pecas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_gerencial_servicos": {
        "query": Query.Gold.controladoria_gerencial_servicos,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_interno_vendedores": {
        "query": Query.Gold.controladoria_interno_vendedores,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_interno_veiculos": {
        "query": Query.Gold.controladoria_interno_veiculos,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_interno_pecas": {
        "query": Query.Gold.controladoria_interno_pecas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_interno_servicos": {
        "query": Query.Gold.controladoria_interno_servicos,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "controladoria_interno_financeiro": {
        "query": Query.Gold.controladoria_interno_financeiro,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     },
    "dealer_cohort_maquinas": {
        "query": Query.Gold.dealer_cohort_maquinas,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
     },
    "dealer_cohort_servicos": {
        "query": Query.Gold.dealer_cohort_servicos,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
     },
    "chamados_formalizacao_credito_bamaq_assinatura": {
        "query": Query.Gold.sync_chamados_formalizacao_credito_bamaq_assinatura,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
     },
    "chamados_encerrados_facilities": {
        "query": Query.Gold.sync_chamados_encerrados_facilities,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
     },
    "autom_veiculos_detalhamento": {
        "query": Query.Gold.autom_veiculos_detalhamento,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
     }
}

tables_gold_sge = {
    "bandeirasgrupobamaq": {
        "query": "select * from bqtb_EmpresasGrupoBamaq",
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    }
}