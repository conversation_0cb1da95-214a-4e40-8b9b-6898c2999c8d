from Global_Vars.Queries import Query

#region direct querys
tables_direct_dw_bamaq = {

}

tables_direct_syonet = {
    "crm_auto": {
        "query": Query.Direct_Queries.crm_auto,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "crm_equipe": {
        "query": Query.Direct_Queries.crm_equipe,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "crm_auto_bicrmautomotivo": {
        "query": Query.Direct_Queries.crm_auto_bicrmautomotivo,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
     "crm_auto_testdrive": {
        "query": Query.Direct_Queries.crm_auto_testdrive,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
     "crm_auto_historico_etapas": {
        "query": Query.Direct_Queries.crm_auto_historico_etapas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "crm_auto_faturamento": {
    "query": Query.Direct_Queries.crm_auto_faturamento,
    "times_per_day": 5,
    "kwargs": {
        "column_types": {}
        }
    }
    ## ,
    ## "crm_auto_motivotestedrive": {
    ##    "query": Query.Direct_Queries.crm_auto_clientes,
    ##    "times_per_day": 5,
    ##    "kwargs": {
    ##        "column_types": {}
    ##    }
    ##}
}

tables_direct_quiver = {
    "faturamento": {
        "query": Query.Direct_Queries.quiver_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "clientes": {
        "query": Query.Direct_Queries.quiver_clientes,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "financeiro": {
        "query": Query.Direct_Queries.quiver_financeiro,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    }
}

tables_direct_nbs = {
    "formapagto_faturamento": {
        "query": Query.Direct_Queries.nbs_formapagto_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "faturamento": {
        "query": Query.Direct_Queries.nbs_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "clientes": {
        "query": Query.Direct_Queries.nbs_clientes,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "financeiro": {
        "query": Query.Direct_Queries.nbs_financeiro,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "posvendas": {
        "query": Query.Direct_Queries.nbs_posvendas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "fat_pecas": {
        "query": Query.Direct_Queries.nbs_fat_pecas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "fat_posvendas": {
        "query": Query.Direct_Queries.nbs_fat_posvendas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    }
}

tables_direct_dealer = {
    "formapagto_faturamento": {
        "query": Query.Direct_Queries.dealer_formapagto_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "faturamento": {
        "query": Query.Direct_Queries.dealer_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "clientes": {
        "query": Query.Direct_Queries.dealer_clientes,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "financeiro": {
        "query": Query.Direct_Queries.dealer_financeiro,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    }
}

tables_direct_newcon = {
    "formapagto_faturamento": {
        "query": Query.Direct_Queries.newcon_formapagto_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "faturamento": {
        "query": Query.Direct_Queries.newcon_faturamento,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "clientes": {
        "query": Query.Direct_Queries.newcon_clientes,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "financeiro": {
        "query": Query.Direct_Queries.newcon_financeiro,
        "times_per_day": 1,
        "kwargs": {
            "column_types": {}
        }
    },
    "indicacao_propostas": {
        "query": Query.Direct_Queries.newcon_indicacao_propostas,
        "times_per_day": 1,
        "kwargs": {
            # "column_types": {}
        }
    },
    "indicacao_vendas": {
        "query": Query.Direct_Queries.newcon_indicacao_vendas,
        "times_per_day": 1,
        "kwargs": {
            # "column_types": {}
        }
    }
}

tables_direct_corpore = {
    "custo_folha": {
        "query": Query.Direct_Queries.copore_custo_folha,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    }
}
#endregion direct querys

#region bronze to silver querys
tables_silver_dw_bamaq = {
    "dw_bamaq_consorcio": {
        "query": Query.Bronze_to_silver.select_silver_dw_bamaq_consorcio,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    }
}

tables_silver_quiver = {
    "quiver_seguros": {
        "query": Query.Bronze_to_silver.select_silver_quiver_seguros,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
     "quiver_cgf": {
        "query": Query.Bronze_to_silver.select_silver_quiver_cgf,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    }
}

tables_silver_syonet= {
     "syo_cgf": {
        "query": Query.Bronze_to_silver.select_silver_syo_cgf,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    }
}

tables_silver_nbs = {
    # "nbs_pecas": {
    #     "query": Query.Bronze_to_silver.select_silver_nbs_pecas,
    #     "times_per_day": 1,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # },
    # "nbs_servicos": {
    #     "query": Query.Bronze_to_silver.select_silver_nbs_servicos,
    #     "times_per_day": 1,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # },
    # "nbs_veiculos": {
    #     "query": Query.Bronze_to_silver.select_silver_nbs_veiculos,
    #     "times_per_day": 1,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # }
    "nbs_vendas": {
        "query": Query.Bronze_to_silver.select_silver_nbs_vendas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    }
    # ,
    # "nbs_posicao_estoque": {
    #     "query": Query.Bronze_to_silver.select_silver_posicao_estoque,
    #     "times_per_day": 3,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # }
}

tables_silver_dealer = {
    # "dealer_faturamento": {
    #     "query": Query.Bronze_to_silver.select_silver_dealer_faturamento,
    #     "times_per_day": 3,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # },
    "dealer_cpc47": {
        "query": Query.Bronze_to_silver.select_silver_dealer_cpc47,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_vendas": {
        "query": Query.Bronze_to_silver.select_silver_dealer_vendas,
        "times_per_day": 5,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_valor_os": {
        "query": Query.Bronze_to_silver.select_silver_dealer_valor_os,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_valor_ostipoos": {
        "query": Query.Bronze_to_silver.select_silver_dealer_valor_ostipoos,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_fat_maquinas": {
        "query": Query.Bronze_to_silver.select_silver_dealer_fat_maquinas,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_fat_pecas": {
        "query": Query.Bronze_to_silver.select_silver_dealer_fat_pecas,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    # "dealer_fat_pecas_historico": {
    #     "query": Query.Bronze_to_silver.select_silver_dealer_fat_pecas_historico,
    #     "times_per_day": 3,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # },
    "dealer_fat_tipoos": {
        "query": Query.Bronze_to_silver.select_silver_dealer_fat_tipoos,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_estoque_maquinas": {
        "query": Query.Bronze_to_silver.select_silver_dealer_estoque_maquinas,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_fat_produtivo": {
        "query": Query.Bronze_to_silver.select_silver_dealer_fat_produtivo,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_fat_produtivo": {
        "query": Query.Bronze_to_silver.select_silver_dealer_fat_produtivo,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_veiculos_dadoscompl": {
        "query": Query.Bronze_to_silver.select_silver_dealer_veiculos_dadoscompl,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    },
    "dealer_titulos": {
        "query": Query.Bronze_to_silver.select_silver_dealer_titulos,
        "times_per_day": 3,
        "kwargs": {
            "column_types": {}
        }
    }
    # ,
    # "dealer_posicao_estoque": {
    #     "query": Query.Bronze_to_silver.select_silver_dealer_posicao_estoque,
    #     "times_per_day": 3,
    #     "kwargs": {
    #         "column_types": {}
    #     }
    # }
}

tables_silver_newcon = {

}

tables_silver_corpore = {
    "centrocustocolaborador": {
            "query": Query.Bronze_to_silver.select_silver_corpore_centrocustocolaborador,
            "times_per_day": 3,
            "kwargs": {
                "column_types": {}
            }
        },
    "empresaferiado": {
            "query": Query.Bronze_to_silver.select_silver_corpore_empresaferiado,
            "times_per_day": 3,
            "kwargs": {
                "column_types": {}
            }
        }
}

tables_silver_mef = {
    "mef_orcado": {
            "query": Query.Bronze_to_silver.select_silver_mef_orcado,
            "times_per_day": 3,
            "kwargs": {
                "column_types": {}
            }
        },
    "mef_realizado": {
            "query": Query.Bronze_to_silver.select_silver_mef_realizado,
            "times_per_day": 3,
            "kwargs": {
                "column_types": {}
            }
        }
}
#endregion bronze to silver querys