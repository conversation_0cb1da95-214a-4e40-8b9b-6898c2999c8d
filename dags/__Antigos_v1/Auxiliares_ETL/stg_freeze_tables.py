from Global_Vars.Queries import Query

tables_freeze_nbs = {
    "estoque": {
        "query": Query.Freeze.nbs_estoque,
        "kwargs": {
            "column_types": {},
            "data_ref": "data"
        }
    }
}

tables_freeze_dealer = {
    "estoque": {
        "query": Query.Freeze.dealer_estoque,
        "kwargs": {
            "data_ref": "data"
        }
    },
    "estoque_filiais": {
        "query": Query.Freeze.dealer_estoque_filiais,
        "kwargs": {
            "data_ref": "data_posicao"
        }
    },
    "estoque_maquinas": {
        "query": Query.Freeze.dealer_estoque_maquinas,
        "kwargs": {
            "data_ref": "data_posicao"
        }
    },
    "estoque_pecas": {
        "query": Query.Freeze.dealer_estoque_pecas,
        "kwargs": {
            "data_ref": "data_posicao"
        }
    },
    "estoque_pecas_analitico": {
        "query": Query.Freeze.dealer_estoque_pecas_analitico,
        "kwargs": {            
            "data_ref": "data_posicao"    
        }        
    },
    "os_pendentes": {
        "query": Query.Freeze.dealer_os_pendentes,
        "kwargs": {
            "data_ref": "data_posicao"
        }
    }
}

tables_freeze_newcon = {
}

tables_freeze_dw_corporativo = {
    "posicao_estoque": {
        "query": Query.Freeze.dw_posicao_estoque,
        "kwargs": {
            "data_ref": "data_ref"
            # ,"auto_create": "yes"
        }
    },
    "office365_license": {
        "query": Query.Freeze.dw_office365_license,
        "kwargs": {
            "data_ref": "data_ref"
            # ,"auto_create": "yes"
        }
    }
    # ,
    # "office365_user": {
    #     "query": Query.Freeze.dw_office365_user,
    #     "kwargs": {
    #         "data_ref": "data_ref"
    #         # ,"auto_create": "yes"
    #     }
    # }
}

tables_freeze_syonet = {

}
