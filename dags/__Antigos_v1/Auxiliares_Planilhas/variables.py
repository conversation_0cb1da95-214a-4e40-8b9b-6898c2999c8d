from __Antigos_v1.Auxiliares_Planilhas.passwords import *

# DW Corporativo
DW_CORPORATIVO_USERNAME = 'bq_dwcorporativo_u'
DW_CORPORATIVO_PASSWORD = DW_PASSWORD
DW_CORPORATIVO_DBNAME = 'postgres'
DW_CORPORATIVO_HOST = '***********'
DW_CORPORATIVO_PORT = '5432'
# Schema para a criação e inserção de registros do ETL
SCHEMA_CRIACAO_INSERT = 'dbdwcorporativo'

# Sharepoint
SHAREPOINT_USERNAME = '<EMAIL>'
SHAREPOINT_USERNAME = '<EMAIL>'
SHAREPOINT_PASSWORD = SHAREPOINT_PASSWORD
SITE_URL = 'https://bqmm.sharepoint.com/sites/business_intelligence/'

WINDOWS_USERNAME = r'MMBQ\powerbi.consorcio'
WINDOWS_PASSWORD = WINDOWS_PASSWORD

# Usuarios com permissoes gerais nas tabelas
all_grants = ["master", "iebt_daniel_laranjo", "ext_leticia_furletti", "bq_dwcorporativo_u", "ext_rafael_neto"]
specific_grants = ["bq_reinilson_silva"]

index = {
    "PWBI Painel de Vagas Grupo Bamaq R&S 2024 revisado.xlsm": {
        "file_id": "4680b28b-9fab-4b70-8585-49f68c6b4fb1",
        "pages": {
            "0": {
                "sheet_name": "Planilha2",
                "dump_type": 0
            },
            "1": {
                "sheet_name": "Desempenho Dash",
                "dump_type": 0
            },
            "2": {
                "sheet_name": "Etapas R&S",
                "dump_type": 0
            },
            "3": {
                "sheet_name": "SLA vagas",
                "dump_type": 0
            },
            "4": {
                "sheet_name": "Cad_C",
                "dump_type": 0
            },
            "5": {
                "sheet_name": "PS",
                "dump_type": 1,
                "table_name": "Colaboradores",
                "column_names": ["ID", "Processos_Seletivos", "Status_da_Vaga", "Nivel", "Motivo", "Nome_em_caso_de_Substituicao", "Confidencial", "Autorizado", "Salario", "Comissao", "Numero_do_Chamado", "Data_de_Abertura", "Reabertura_de_Processo", "Data_de_Cancelamento", "Data_de_Congelamento", "Data_Atual", "Dias_em_Aberto", "Meta", "Cliente", "Cidade", "BU", "Setor", "Gestor_da_Vaga", "Gestor_do_Processo", "Status_do_Processo", "Alinhamento_de_Perfil", "Envio_de_Perfis", "Fim_do_Envio_de_Perfis", "Retorno_de_Perfis_do_Gestor", "Feedback_para_Gestor", "Entrevista_RH", "PI", "Parecer_do_Gestor", "Entrevista_do_Gestor", "Feedback_do_Gestor", "Feedback_dos_Candidatos", "Proposta", "Encerramento_do_Processo", "Data_de_Admissao", "Empresa_de_Origem", "Tipo_de_Contratacao", "Observacao", "Abertura_ate_Envio_de_Curriculos", "Retorno_de_Perfis_ate_Entrevista_RH", "Entrevista_RH_ate_Parecer_do_Gestor", "Parecer_ate_Entrevista_do_Gestor", "Feedback_do_Gestor_ate_Proposta", "Proposta_ate_Admissao"],
                "mode": "file_id",
                "folder_id": ""
            },
            "6": {
                "sheet_name": "Jan",
                "dump_type": 0
            },
            "7": {
                "sheet_name": "Fev",
                "dump_type": 0
            },
            "8": {
                "sheet_name": "Mar",
                "dump_type": 0
            },
            "9": {
                "sheet_name": "Abr",
                "dump_type": 0
            },
            "10": {
                "sheet_name": "Mai",
                "dump_type": 0
            },
            "11": {
                "sheet_name": "Jun",
                "dump_type": 0
            },
            "12": {
                "sheet_name": "Jul",
                "dump_type": 0
            },
            "13": {
                "sheet_name": "Ago",
                "dump_type": 0
            },
            "14": {
                "sheet_name": "Set",
                "dump_type": 0
            },
            "15": {
                "sheet_name": "Out",
                "dump_type": 0
            },
            "16": {
                "sheet_name": "Nov",
                "dump_type": 0
            },
            "17": {
                "sheet_name": "Dez",
                "dump_type": 0
            },
            "18": {
                "sheet_name": "Rel_Geral",
                "dump_type": 0
            },
            "19": {
                "sheet_name": "Rel_C",
                "dump_type": 0
            },
            "20": {
                "sheet_name": "Rel_Tempo",
                "dump_type": 0
            },
            "21": {
                "sheet_name": "Status",
                "dump_type": 0
            },
            "22": {
                "sheet_name": "Dash Comparativo",
                "dump_type": 0
            },
            "23": {
                "sheet_name": "Dados Comparativo",
                "dump_type": 0
            },
            "24": {
                "sheet_name": "Unidade de Neg\u00f3cio",
                "dump_type": 0
            },
            "25": {
                "sheet_name": "Motivo",
                "dump_type": 0
            },
            "26": {
                "sheet_name": "Contrata\u00e7\u00e3o",
                "dump_type": 0
            },
            "27": {
                "sheet_name": "Status1",
                "dump_type": 0
            },
            "28": {
                "sheet_name": "Vagas Clientes",
                "dump_type": 0
            },
            "29": {
                "sheet_name": "Nivel",
                "dump_type": 0
            },
            "30": {
                "sheet_name": "Admiss\u00f5es",
                "dump_type": 0
            },
            "31": {
                "sheet_name": "Prazo",
                "dump_type": 0
            },
            "32": {
                "sheet_name": "Planilha1",
                "dump_type": 0
            },
            "33": {
                "sheet_name": "Abertas",
                "dump_type": 0
            },
            "34": {
                "sheet_name": "Base DASH geral",
                "dump_type": 0
            },
            "35": {
                "sheet_name": "M\u00e9dia fechamento de vagas",
                "dump_type": 0
            },
            "36": {
                "sheet_name": "Status Processos",
                "dump_type": 0
            },
            "37": {
                "sheet_name": "Lista",
                "dump_type": 0
            },
            "38": {
                "sheet_name": "Equipe",
                "dump_type": 0
            },
            "39": {
                "sheet_name": "Aux1",
                "dump_type": 0
            },
            "40": {
                "sheet_name": "Aux2",
                "dump_type": 0
            }
        }
    },
    "Metas BI DealernetWF - DPR.xlsx": {
        "file_id": "0A3896D6-EE7B-47FF-AF3E-AD81D1390861",
        "pages": {
            "Meta": {
                "sheet_name": "",
                "dump_type": 1,
                "table_name": "MetaBIDealernetWF_DPR",
                #"column_names": ["AnoMes","EmpresaCod","Empresa","Indicador","UsuarioCod","Usuario","VlrMeta"],
                "mode": "file_name",
                "folder_id": "429ca0fc-c3cc-493c-b31d-58da9784c4bf"
            },
            "DeParaProdutos2": {
                "sheet_name": "",
                "dump_type": 1,
                "table_name": "BIDealernetWF_DeParaProdutos2",
                #"column_names": ["ID","TipoProdutoWF","IDGrupo","Grupo"],
                "mode": "file_name",
                "folder_id": "429ca0fc-c3cc-493c-b31d-58da9784c4bf"
            }
        }
    },
    "Metas BI DealernetWF - DVS.xlsx": {
        "file_id": "A2EA9A31-E3C4-4278-93D9-FC55AD476215",
        "pages": {
            "Meta": {
                "sheet_name": "",
                "dump_type": 1,
                "table_name": "MetaBIDealernetWF_DVS",
                #"column_names": ["AnoMes","EmpresaCod","Empresa","Indicador","UsuarioCod","Usuario","VlrMeta"],
                "mode": "file_name",
                "folder_id": "2f452902-e804-420e-9c59-335376ca50c6"
            }
        }
    },
    "DeParaMEF.xlsx": {
        # Esta planilha é copiada do FileServer do FP&A todos os dias para o Sharepoint através do Scheduler do Windows no Servidor Vulture
        "file_id": "DED5B4AF-08A8-4568-905A-AE4AEDB8FE7C",
        "pages": {
            "dContaContabil": {
                "sheet_name": "",
                "dump_type": 1,
                "table_name": "DeParaMEF_dContaContabil",
                #"column_names": ["AnoMes","EmpresaCod","Empresa","Indicador","UsuarioCod","Usuario","VlrMeta"],
                "mode": "file_name",
                "folder_id": "47e36c1a-1444-4387-921f-7e4a16e13092"
            }
        }
    },
    "Estoque_GWM_BH.xlsx": {
        "file_id": "D02F8A7A-EC1E-40BF-AEB6-557B6A41F1F7",
        "pages": {
            "0": {
                "sheet_name": "Vehicle",
                "dump_type": 1,
                "table_name": "Estoque_GWM_BH",
                "column_names": ["no","vin", "pdi", "status_stock", "br_product_name", "brand", "source", "model", "interior_color", "package", "model_year", "production_year", "exterior_color", "br_sales_material_code", "warehouse_level", "warehouse_no", "warehouse_name", "vehicle_sales_status", "not_available_reason", "days_in_stock_nac", "days_in_stock_cd", "stock_status", "retail_order_no", "retail_order_status", "retail_invoice_status", "retail_cancel_status", "order_create_time", "dealer_code", "dealer_name", "sto_no", "actual_outbound_date", "actual_inbound_date"],
                "mode": "file_name",
                "folder_id": "76208bf0-dd7a-41af-99d5-0bc548f0298b"
            }
        }
    },
    "Estoque_GWM_CG.xlsx": {
        "file_id": "A1BB6962-AE65-4B3D-99FD-4ADE895BF5B2",
        "pages": {
            "0": {
                "sheet_name": "Vehicle",
                "dump_type": 1,
                "table_name": "Estoque_GWM_CG",
                "column_names": ["no","vin","pdi","status_stock","br_product_name","brand","source","model","interior_color","package","model_year","production_year","exterior_color","br_sales_material_code","warehouse_level","warehouse_no","warehouse_name","vehicle_sales_status","not_available_reason","days_in_stock_nac","days_in_stock_cd","stock_status","retail_order_no","retail_order_status","retail_invoice_status","retail_cancel_status","order_create_time","dealer_code","dealer_name","sto_no","actual_outbound_date","actual_inbound_date"],
                "mode": "file_name",
                "folder_id": "60cbebb6-dbd1-4847-ad51-354858d923d7"
            }
        }
    },
    "Dados_GWM_BH.xlsx": {
        "file_id": "F4A75D54-1A55-4888-BA8A-FA5DEEF82C17",
        "pages": {
            "Retail Order": {
                "dump_type": 1,
                "table_name": "GWM_BH",
                "mode": "file_name",
                "folder_id": "76208bf0-dd7a-41af-99d5-0bc548f0298b",
                "skiprows": 0
            }
        }
    },
    "Dados_GWM_CG.xlsx": {
        "file_id": "0D88DA50-8773-46AE-9EA1-5CBFA064C900",
        "pages": {
            "Retail Order": {
                "dump_type": 1,
                "table_name": "GWM_CG",
                "mode": "file_name",
                "folder_id": "60cbebb6-dbd1-4847-ad51-354858d923d7",
                "skiprows": 0
            }
        }
    },
    "Dados_GWM_Pamp.xlsx": {
        "file_id": "F4A75D54-1A55-4888-BA8A-FA5DEEF82C17",
        "pages": {
            "Retail Order": {
                "dump_type": 1,
                "table_name": "GWM_Pamp",
                "mode": "file_name",
                "folder_id": "a46ffcbd-a04f-431c-9c0b-69d0d6681100",
                "skiprows": 0
            }
        }
    },
    "Entrevista de Desligamento(1-91).xlsx": {
        "file_id": "78768e21-fe1e-4d59-aaa3-bb0430e07aeb",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 1,
                "table_name": "Desligamento",
                "column_names": ["ID", "Hora_de_inicio", "Hora_de_conclusao", "Email", "Nome", "Hora_da_ultima_modificacao", "Nome_completo", "Cargo", "Empresa_anterior", "Lider_imediato", "Data_de_admissao", "Tipo_do_desligamento", "Data_do_ultimo_dia_de_trabalho", "Principal_fator_de_desligamento", "Justificativa", "Avaliacao_da_lideranca_imediata", "Comentarios_sobre_a_lideranca", "Avaliacao_do_ambiente_de_trabalho", "Comentarios_sobre_o_ambiente_de_trabalho", "Compatibilidade_salarial", "Comentario_sobre_o_salario", "Assistencia_medica", "Comentario_sobre_a_assistencia_medica", "Avaliacao_dos_beneficios", "Outros_beneficios_sugeridos", "Imagem_da_empresa", "Comentarios_sobre_a_empresa", "Feedback_adicional", "Recomendacao_do_Grupo_Bamaq"],
                "mode": "file_id",
                "folder_id": ""
            }
        }
    },
    "Avalia\u00e7\u00e3o_ 45 dias de Atua\u00e7\u00e3o no Grupo Bamaq - Colaborador(1-237) (1).xlsx": {
        "file_id": "b90cac8c-209e-4f35-a607-23c11cbe7ca4",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 4,
                "table_name": "Aval_Col",
                "column_names": ["id", "hora_inicio", "hora_conclusao", "email", "nome", "nome_lider", "experiencia_momento", "processo_integracao", "suporte_equipe", "suporte_lideranca", "clima_organizacional", "comunicacao_interna", "experiencia_valores", "motivacao_contribuicao", "desempenho", "clareza_responsabilidades", "alinhamento_metas", "satisfacao_atividades", "satisfacao_grupo", "justificativa"],
                "period": "45 dias",
                "recreate": "Y"
            }
        }
    },
    "Avalia\u00e7\u00e3o_ 90 dias de Atua\u00e7\u00e3o no Grupo Bamaq - Colaborador(1-120) (1).xlsx": {
        "file_id": "e6cd81f5-e4b8-418a-bef2-40cd5c66e3b6",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 4,
                "table_name": "Aval_Col",
                "column_names": ["id", "hora_inicio", "hora_conclusao", "email", "nome", "nome_lider", "experiencia_momento", "processo_integracao", "suporte_equipe", "suporte_lideranca", "clima_organizacional", "comunicacao_interna", "experiencia_valores", "motivacao_contribuicao", "desempenho", "clareza_responsabilidades", "alinhamento_metas", "satisfacao_atividades", "satisfacao_grupo", "justificativa"],
                "period": "90 dias" ,
                "recreate": ""
            }
        }
    },
    "Avalia\u00e7\u00e3o_ 45 dias de Atua\u00e7\u00e3o no Grupo Bamaq - L\u00edder(1-90).xlsx": {
        "file_id": "79b69a97-8545-450f-93a6-82a81fc64359",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 4,
                "table_name": "Aval_Lid",
                "column_names": ["id", "hora_inicio", "hora_conclusao", "email", "nome", "nome_colaborador_avaliado", "processo_integracao", "suporte_equipe", "suporte_lideranca", "clima_organizacional", "comunicacao_interna", "experiencia_valores", "motivacao_contribuicao", "desempenho", "clareza_responsabilidades", "alinhamento_metas", "satisfacao_atividades", "satisfacao_grupo", "justificativa", "continuar_apostando"],
                "period": "45 dias",
                "recreate": "Y"
            }
        }
    },
    "Avalia\u00e7\u00e3o_ 90 dias de Atua\u00e7\u00e3o no Grupo Bamaq - L\u00edder(1-121).xlsx": {
        "file_id": "e1020bfb-6253-4f9a-8f1f-f248bedac404",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 4,
                "table_name": "Aval_Lid",
                "column_names": ["id", "hora_inicio", "hora_conclusao", "email", "nome", "nome_colaborador_avaliado", "processo_integracao", "suporte_equipe", "suporte_lideranca", "clima_organizacional", "comunicacao_interna", "experiencia_valores", "motivacao_contribuicao", "desempenho", "clareza_responsabilidades", "alinhamento_metas", "satisfacao_atividades", "satisfacao_grupo", "justificativa", "continuar_apostando"],
                "period": "90 dias",
                "recreate": ""
            }
        }
    },
    "Consorcio_Auxiliar.xlsx": {
        "file_id": "8273aef5-3e5f-4496-b33b-1e655eee176f",
        "pages": {
            "Consorcio": {
                "dump_type": 1,
                "table_name": "consorcio_auxiliar",
                "mode": "file_id"
            }
        }
    },
    "Feriados_BAMAQ.xlsx": {
        "file_id": "716a2628-b480-4aca-8dad-17a7650e8d50",
        "pages": {
            "0": {
                "sheet_name": "Feriados",
                "dump_type": 1,
                "table_name": "Feriados",
                "column_names": ["Data", "Descricao", "Escopo"],
                "mode": "file_id",
                "folder_id": ""
            },
            "1": {
                "sheet_name": "Listas",
                "dump_type": 0
            }
        }
    },
    "Consorcio.xlsx": {
        "file_id": "626D4ABD-748A-471B-9D5C-E5920861F23B",
        "pages": {
            "Porsche BH": {
                "sheet_name": "Porsche BH",
                "dump_type": 2,
                "metas_objetivos_id": 2,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Porsche SSA": {
                "sheet_name": "Porsche SSA",
                "dump_type": 2,
                "metas_objetivos_id": 2,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Mercedes Raja": {
                "sheet_name": "Mercedes Raja",
                "dump_type": 2,
                "metas_objetivos_id": 2,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Mercedes Barão": {
                "sheet_name": "Mercedes Barão",
                "dump_type": 2,
                "metas_objetivos_id": 2,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Mercedes JF": {
                "sheet_name": "Mercedes JF",
                "dump_type": 2,
                "metas_objetivos_id": 2,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Gwm BH": {
                "sheet_name": "Gwm BH",
                "dump_type": 2,
                "metas_objetivos_id": 2,    
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Gwm CG": {
                "sheet_name": "Gwm CG",
                "dump_type": 2,
                "metas_objetivos_id": 2,    
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Premium": {
                "sheet_name": "Premium",
                "dump_type": 2,
                "metas_objetivos_id": 2,    
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            }
        }
    },
    "Estoque_Cariacica_BH.xlsx": {
        "file_id": "F4A75D54-1A55-4888-BA8A-FA5DEEF82C17",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 1,
                "table_name": "estoque_nacionalizar_porschebh",
                "mode": "file_name",
                "folder_id": "7a364d0d-a618-4726-be95-d23911b669fd"
            }
        }
    },
    "Estoque_Cariacica_SSA.xlsx": {
        "file_id": "F4A75D54-1A55-4888-BA8A-FA5DEEF82C17",
        "pages": {
            "0": {
                "sheet_name": "Sheet1",
                "dump_type": 1,
                "table_name": "estoque_nacionalizar_porschessa",
                "mode": "file_name",
                "folder_id": "bb750619-f619-4663-a7c6-50eefade9d7a"
            }
        }
    },
    "1-Dados_BI_Automotivo_Porsche_vendas_BH_Novos.xlsx": {
        "file_id": "9237123e-2b2d-40ba-ad4a-726503cb883c",
        "pages": {
            "PSQ": {
                "dump_type": 2,
                "metas_objetivos_id": 5,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "CES": {
                "dump_type": 2,
                "metas_objetivos_id": 8,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 2,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Vendas Veiculos (Valor R$)": {
                "dump_type": 2,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Margem Veiculos": {
                "dump_type": 2,
                "metas_objetivos_id": 16,    
                "system": "NBS",
                "cod_empresa": "401"
            },
            "6": {
                "sheet_name": "Market Share",
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "7": {
                "sheet_name": "Bonus Performance - Vendas",
                "dump_type": 0,
                "metas_objetivos_id": 36,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "8": {
                "sheet_name": "Bonus Performance - Outros",
                "dump_type": 0,
                "metas_objetivos_id": 37,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Reservas a faturar": {
                "dump_type": 2,
                "metas_objetivos_id": 42,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Cotas Alocadas": {
                "sheet_name": "Cotas Alocadas",
                "dump_type": 2,
                "metas_objetivos_id": 43,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Fila de Espera": {
                "sheet_name": "Fila de Espera",
                "dump_type": 1,
                "table_name": "fila_de_espera_porsche_bh",
                "column_names": ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"],
                "mode": "file_id",
                "folder_id": ""
            },
            "12": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_Porsche_pos_vendas_BH.xlsx": {
        "file_id": "36da166b-3e49-4386-8d9b-e366b1189770",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "CSI": {
                "sheet_name": "CSI",
                "dump_type": 2,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "CES": {
                "sheet_name": "CES",
                "dump_type": 2,
                "metas_objetivos_id": 22,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Passagens": {
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Margem": {
                "dump_type": 2,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Vendas VN": {
                "dump_type": 2,
                "metas_objetivos_id": 32,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "9": {
                "sheet_name": "Bonus Performance - Pos-Vendas",
                "dump_type": 0,
                "metas_objetivos_id": 35,   
                "system": "NBS",
                "cod_empresa": "401"
            }
        }
    },
"1-Dados_BI_Automotivo_Porsche_pos_vendas_Salvador.xlsx": {
        "file_id": "DDDD1769-E5E7-4D16-94EC-42FE383F645C",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "CSI": {
                "dump_type": 2,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "CES": {
                "dump_type": 2,
                "metas_objetivos_id": 22,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Passagens": {
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Margem": {
                "dump_type": 2,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Vendas VN": {
                "dump_type": 2,
                "metas_objetivos_id": 32,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "9": {
                "sheet_name": "Bonus Performance - Pos-Vendas",
                "dump_type": 0,
                "metas_objetivos_id": 35,   
                "system": "NBS",
                "cod_empresa": "403"
            }
        }
    },
    "1-Dados_BI_Automotivo_Porsche_vendas_Salvador_Novos.xlsx": {
        "file_id": "815bcd7e-55fc-4017-8ea2-343bf31d4ce6",
        "pages": {
            "PSQ": {
                "dump_type": 2,
                "metas_objetivos_id": 5,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "CES": {
                "sheet_name": "CES",
                "dump_type": 2,
                "metas_objetivos_id": 8,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 2,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Vendas Veiculos (Valor R$)": {
                "dump_type": 2,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Margem Veiculos": {
                "dump_type": 2,
                "metas_objetivos_id": 16,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "6": {
                "sheet_name": "Market Share",
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "7": {
                "sheet_name": "Bonus Performance - Vendas",
                "dump_type": 0,
                "metas_objetivos_id": 36,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "8": {
                "sheet_name": "Bonus Performance - Outros",
                "dump_type": 0,
                "metas_objetivos_id": 37,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Reservas a faturar": {
                "dump_type": 2,
                "metas_objetivos_id": 42,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Cotas Alocadas": {
                "dump_type": 2,
                "metas_objetivos_id": 43,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Fila de Espera": {
                "dump_type": 1,
                "table_name": "fila_de_espera_porsche_ssa",
                "column_names": ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"],
                "mode": "file_id",
                "folder_id": ""
            },
            "12": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_GWM_Vendas_BH.xlsx": {
        "file_id": "db000dd2-1c72-4632-8edf-24704369d545",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "1": {
                "sheet_name": "CSI",
                "dump_type": 0,
                "metas_objetivos_id": 7,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 12,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 0,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Vendas Veiculos (Valor R$)": {
                "dump_type": 0,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Margem veículos": {
                "dump_type": 0,
                "metas_objetivos_id": 16,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Market Share": {
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Reservas do Mês": {
                "dump_type": 2,
                "metas_objetivos_id": 38,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Pendências emplacamento": {
                "dump_type": 2,
                "metas_objetivos_id": 39,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Estoque": {
                "dump_type": 2,
                "metas_objetivos_id": 41,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Comissoes VD": {
                "dump_type": 2,
                "metas_objetivos_id": 55,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "11": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-1-Dados_BI_Automotivo_GWM_Vendas_Pamp.xlsx": {
        "file_id": "A2F6093D-B022-4EDF-8799-3FBA095246EC",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "1": {
                "sheet_name": "CSI",
                "dump_type": 0,
                "metas_objetivos_id": 7,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 12,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 0,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Vendas Veiculos (Valor R$)": {
                "dump_type": 0,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Margem veículos": {
                "dump_type": 0,
                "metas_objetivos_id": 16,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Market Share": {
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Reservas do Mês": {
                "dump_type": 2,
                "metas_objetivos_id": 38,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Pendências emplacamento": {
                "dump_type": 2,
                "metas_objetivos_id": 39,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Estoque": {
                "dump_type": 2,
                "metas_objetivos_id": 41,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Comissoes VD": {
                "dump_type": 2,
                "metas_objetivos_id": 55,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "11": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_GWM_pos_vendas_BH.xlsx": {
        "file_id": "CF745A03-0ED5-408B-971B-ED8C4AD653F1",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "1": {
                "sheet_name": "CSI",
                "dump_type": 0,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 17,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Passagens": {
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "Margem": {
                "dump_type": 2,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "601"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "601"
            }
        }
    },
    "1-1-Dados_BI_Automotivo_GWM_pos_vendas_Pamp.xlsx": {
        "file_id": "383F8BD0-53D3-4B5B-A6AA-9EB88724E41A",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "1": {
                "sheet_name": "CSI",
                "dump_type": 0,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 17,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Passagens": {
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "Margem": {
                "dump_type": 2,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "605"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "605"
            }
        }
    },
    "1-Dados_BI_Automotivo_GWM_Vendas_MS.xlsx": {
        "file_id": "56b3ef86-fe38-4812-8559-5a222c2c7038",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "1": {
                "sheet_name": "CSI",
                "dump_type": 0,
                "metas_objetivos_id": 7,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 12,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 0,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Vendas Veiculos (Valor R$)": {
                "dump_type": 0,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Margem Veiculos": {
                "dump_type": 0,
                "metas_objetivos_id": 16,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Market Share": {
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Reservas do Mês": {
                "dump_type": 2,
                "metas_objetivos_id": 38,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Pendências emplacamento": {
                "dump_type": 2,
                "metas_objetivos_id": 39,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Estoque": {
                "sheet_name": "Estoque",
                "dump_type": 2,
                "metas_objetivos_id": 41,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Comissoes VD": {
                "sheet_name": "Comissoes VD",
                "dump_type": 2,
                "metas_objetivos_id": 55,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "11": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_GWM_pos_vendas_MS.xlsx": {
        "file_id": "EB6775E6-5CD2-4F3C-BABE-72C63E94445E",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "1": {
                "sheet_name": "CSI",
                "dump_type": 0,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "NPS": {
                "sheet_name": "NPS",
                "dump_type": 2,
                "metas_objetivos_id": 17,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Passagens": {
                "sheet_name": "Passagens",
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "Margem": {
                "dump_type": 2,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "602"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "602"
            }
        }
    }, 
    "1-Dados_BI_Automotivo_Bamaq_Premium.xlsx": {
        "file_id": "eee1467d-e0d4-4bac-b6cf-1154263bcbed",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "CSI": {
                "dump_type": 2,
                "metas_objetivos_id": 7,    
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 12,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 0,
                "metas_objetivos_id": 1,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "Vendas Veiculos (Valor R$)": {
                "dump_type": 0,
                "metas_objetivos_id": 20,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "Margem Veiculos": {
                "dump_type": 2,
                "metas_objetivos_id": 16,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "Performance de Captação": {
                "dump_type": 2,
                "metas_objetivos_id": 14,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "Estoque": {
                "dump_type": 2,
                "metas_objetivos_id": 15,   
                "system": "DEALERNETWF",
                "cod_empresa": "23"
            },
            "Vendedores": {
                "dump_type": 1,
                "table_name": "vendedores_premium",
                "mode": "file_id",
                "dtype": {
                    "cpf":str
                }
            },
            "Intermediacao Venda - PC SSA": {
                "dump_type": 2,
                "metas_objetivos_id": 64,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "8": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_Mercedes_pos_vendas_BH.xlsx": {
        "file_id": "2e36e189-c520-45b7-af55-0a5c96f57d48",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,  
                "system": "NBS",
                "cod_empresa": "102"
            },
            "CSI": {
                "dump_type": 2,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 17,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Passagens": {
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Margem": {
                "dump_type": 0,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Vendas VN": {
                "dump_type": 2,
                "metas_objetivos_id": 32,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "9": {
                "sheet_name": "Starclass",
                "dump_type": 0,
                "metas_objetivos_id": 34,   
                "system": "NBS",
                "cod_empresa": "102"
            }
        }
    },
    "1-Dados_BI_Automotivo_Mercedes_vendas_BH_Raja.xlsx": {
        "file_id": "8f02d27d-55ec-4146-be59-df73d3fa925c",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "CSI": {
                "dump_type": 2,
                "metas_objetivos_id": 7,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 12,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 0,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Vendas Veiculos Novos (R$)": {
                "dump_type": 2,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Margem veículos": {
                "dump_type": 0,
                "metas_objetivos_id": 16,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "6": {
                "sheet_name": "Market Share",
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "7": {
                "sheet_name": "Starclass",
                "dump_type": 0,
                "metas_objetivos_id": 33,   
                "system": "NBS",
                "cod_empresa": "102"
            },
            "Reservas a faturar": {
                "dump_type": 2,
                "metas_objetivos_id": 40,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Fila de Espera": {
                "dump_type": 1,
                "table_name": "fila_de_espera_mercedes_bh",
                "column_names": ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"],
                "mode": "file_id",
                "folder_id": ""
            },
            "10": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_Mercedes_vendas_JF.xlsx": {
        "file_id": "997c421f-4a19-495b-b7c8-d7798ea85d9b",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 62,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "CSI": {
                "dump_type": 2,
                "metas_objetivos_id": 7,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 12,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Vendas Veiculos (Volume)": {
                "dump_type": 0,
                "metas_objetivos_id": 1,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Vendas Veiculos Novos (R$)": {
                "dump_type": 2,
                "metas_objetivos_id": 20,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Margem veículos": {
                "dump_type": 0,
                "metas_objetivos_id": 16,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "6": {
                "sheet_name": "Market Share",
                "dump_type": 0,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "7": {
                "sheet_name": "Starclass",
                "dump_type": 0,
                "metas_objetivos_id": 33,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Reservas a faturar": {
                "dump_type": 2,
                "metas_objetivos_id": 40,
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Fila de Espera": {
                "dump_type": 1,
                "table_name": "fila_de_espera_mercedes_jf",
                "column_names": ["modelo", "total", "comprados", "saldo_target", "cinco_meses_antes", "quatro_meses_antes", "tres_meses_antes", "dois_meses_antes", "um_mes_antes", "mes_atual", "target_descontado"],
                "mode": "file_id",
                "folder_id": ""
            },
            "10": {
                "sheet_name": "Apoio",
                "dump_type": 0
            }
        }
    },
    "1-Dados_BI_Automotivo_Mercedes_pos_vendas_JF.xlsx": {
        "file_id": "bdb78f9d-3664-47d2-a992-7ebd7475d7ad",
        "pages": {
            "Percentual de conversão": {
                "dump_type": 2,
                "metas_objetivos_id": 63,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "CSI": {
                "dump_type": 2,
                "metas_objetivos_id": 18,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "NPS": {
                "dump_type": 2,
                "metas_objetivos_id": 17,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Performance de peças": {
                "dump_type": 2,
                "metas_objetivos_id": 3,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Performance de serviços": {
                "dump_type": 2,
                "metas_objetivos_id": 4,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Passagens": {
                "dump_type": 2,
                "metas_objetivos_id": 29,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Margem": {
                "dump_type": 0,
                "metas_objetivos_id": 30,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "7": {
                "sheet_name": "Ticket médio",
                "dump_type": 0,
                "metas_objetivos_id": 31,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Vendas VN": {
                "dump_type": 2,
                "metas_objetivos_id": 32,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "9": {
                "sheet_name": "Starclass",
                "dump_type": 0,
                "metas_objetivos_id": 34,   
                "system": "NBS",
                "cod_empresa": "103"
            }
        }
    },
    "Referencias_BI_Automotivo.xlsx": {
        "file_id": "A35A267F-27FB-4CB0-BCA6-87CBD0C1AF25",
        "pages": {
            "0": {
                "sheet_name": "Planilha1",
                "dump_type": 1,
                "table_name": "referencias_bi_automotivo",
                "column_names": ["indice", "ref", "categoria", "totalizador", "formato", "mascara"],
                "mode": "file_id",
                "folder_id": ""
            }
        }
    },
    "Controle de Venda Direta - Haval H6 e ORA.xlsx": {
    "file_id": "EE49C6EE-D78F-448A-A75B-9CCAAECE15DB",
    # "alt_site": "https://bqmm-my.sharepoint.com/personal/fellipe_cruz_grupobamaq_com_br/",
        "pages": {
            "Faturamento Haval": {
                "dump_type": 1,
                "table_name": "faturamento_haval",
                "mode": "file_name",
                "skiprows": 1,
                "folder_id": "a6a5c382-b370-46c3-b974-02f713dcac20"
            },
            "Faturamento ORA": {
                "dump_type": 1,
                "table_name": "faturamento_ora",
                "mode": "file_name",
                "skiprows": 1,
                "folder_id": "a6a5c382-b370-46c3-b974-02f713dcac20"
            },
            "Comissionamento Fixo": {
                "dump_type": 1,
                "table_name": "comissionamentofixo_gwm",
                "mode": "file_name",
                "folder_id": "a6a5c382-b370-46c3-b974-02f713dcac20"
            },
            "Comissionamento Variável": {
                "dump_type": 1,
                "table_name": "comissionamentovariavel_gwm",
                "mode": "file_name",
                "folder_id": "a6a5c382-b370-46c3-b974-02f713dcac20"
            }
        }
    },
    "Market_Share.xlsx": {
        "file_id": "E60E90B2-ADE7-43A2-B64C-8B5AC0501081",
        "pages": {
            "Porsche BH": {
                "dump_type": 2,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Porsche SSA": {
                "dump_type": 2,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Mercedes BH": {
                "dump_type": 2,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Mercedes JF": {
                "dump_type": 2,
                "metas_objetivos_id": 11,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "GWM BH": {
                "dump_type": 2,
                "metas_objetivos_id": 11,    
                "system": "NBS",
                "cod_empresa": "601"
            },
            "GWM CG": {
                "dump_type": 2,
                "metas_objetivos_id": 11,    
                "system": "NBS",
                "cod_empresa": "602"
            }
        }
    },
    "Starclass.xlsx": {
        "file_id": "41f732cb-6c0e-4def-826d-b08956bcb1eb",
        "pages": {
            "Mercedes BH - Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 33,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Mercedes BH - Pós Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 34,   
                "system": "NBS",
                "cod_empresa": "101"
            },
            "Mercedes JF - Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 33,   
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Mercedes JF - Pós Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 34,    
                "system": "NBS",
                "cod_empresa": "103"
            },
            "Qulificadores": {
                "dump_type": 1,
                "table_name": "qulificadores",
                "mode": "file_id"
            }
        }
    },
    "Bonus_Performance.xlsx": {
        "file_id": "345311fb-9334-4f7a-a722-ae549832cc50",
        "pages": {
            "Porsche BH - Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 36,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Porsche BH - Outros": {
                "dump_type": 2,
                "metas_objetivos_id": 37,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Porsche BH - Pós Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 35,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Porsche SSA - Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 36,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Porsche SSA - Outros": {
                "dump_type": 2,
                "metas_objetivos_id": 37,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Porsche SSA - Pós Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 35,    
                "system": "NBS",
                "cod_empresa": "403"
            }
        }
    },
    "Tomorrow_guide_GWM.xlsx": {
        "file_id": "4B6CF4CF-5FA0-48F3-8CB1-23EECD3755EB",
        "pages": {
            "GWM": {
                "dump_type": 1,
                "table_name": "tomorrow_guide_GWM",
                "mode": "file_id"
            }
        }
    },
    "CRM_Porsche_BH.xlsx": {
        "file_id": "65979B79-BA2E-4A02-8E3B-1C1BE1B7B5B2",
        "pages": {
            "Funil de Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 56,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Vendas por Mídia": {
                "dump_type": 2,
                "metas_objetivos_id": 57,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Vendas por Canal": {
                "dump_type": 2,
                "metas_objetivos_id": 58,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Vendas por Semana": {
                "dump_type": 2,
                "metas_objetivos_id": 59,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Insucessos": {
                "dump_type": 2,
                "metas_objetivos_id": 60,   
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Test Drive": {
                "dump_type": 2,
                "metas_objetivos_id": 61,    
                "system": "NBS",
                "cod_empresa": "401"
            },
            "Insucessos Test Drive": {
                "dump_type": 1,
                "table_name": "crm_test_drive_bh",
                "column_names": ["periodo", "modelo", "motivo_insucesso", "quantidade"],
                "mode": "file_id",
                "folder_id": ""
            }
        }
    },
    "CRM_Porsche_SSA.xlsx": {
        "file_id": "A2A8DE60-2FC4-40B3-81DE-FBC0185D1026",
        "pages": {
            "Funil de Vendas": {
                "dump_type": 2,
                "metas_objetivos_id": 56,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Vendas por Mídia": {
                "dump_type": 2,
                "metas_objetivos_id": 57,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Vendas por Canal": {
                "dump_type": 2,
                "metas_objetivos_id": 58,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Vendas por Semana": {
                "dump_type": 2,
                "metas_objetivos_id": 59,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Insucessos": {
                "dump_type": 2,
                "metas_objetivos_id": 60,   
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Test Drive": {
                "dump_type": 2,
                "metas_objetivos_id": 61,    
                "system": "NBS",
                "cod_empresa": "403"
            },
            "Insucessos Test Drive": {
                "dump_type": 1,
                "table_name": "crm_test_drive_ssa",
                "column_names": ["periodo", "modelo", "motivo_insucesso", "quantidade"],
                "mode": "file_id",
                "folder_id": ""
            }
        }
    },
    "MEF": {
        "file_path": "\\nova.mmbq.local\9-Power-BI$\Projeto\MEF",
        "pages": {
            ("\\Base\\Realizado", "\\Base\\Orçado"): {  
                "dump_type": 3,
                "table_name": "dados_mef"
            },
            "\\Base\\Drill Orçado": {
                "dump_type": 3,
                "table_name": "dados_mef_drill"
            },
            "\\Parâmetros": {
                "dump_type": 3,
                "table_name": "mef_depara",
                "tab_name": "dFormatoMEF"
            }
        }
    },
    "Ata_Reuniao.xlsx": {
        "file_id": "fe3a0077-0f7d-4aac-a0c3-c12cbcef5ac0",
        "pages": {
            "Ata": {
                "dump_type": 1,
                "table_name": "Atas_Comite",
                "mode": "file_id"
            }
        }
    },
    "Referencia_Orcado.xlsx": {
        "file_id": "d6ed738b-361f-46b8-8dc3-cc2f4fddd017",
        "pages": {
            "Orcado": {
                "dump_type": 1,
                "table_name": "referencia_orcado",
                "column_names": ["periodo", "tipo_mask", "tipo_orcado"],
                "mode": "file_id"
            }
        }
    },
    "Dados_Controladoria_Consolidado.xlsx": {
        "file_id": "65a16263-505d-42d5-a5c2-ea5c4f914edb",
        "pages": {
            "Vendedores - Pesados": {
                "dump_type": 1,
                "table_name": "custo_vendedores_pesados",
                "mode": "file_id"
            },
            "Vendedores - Automotivo": {
                "dump_type": 1,
                "table_name": "custo_vendedores_automotivo",
                "mode": "file_id"
            },
            "DePara - Veiculos": {
                "dump_type": 1,
                "table_name": "DePara_Contabilidade",
                "mode": "file_id"
            },
            "DePara - Oficina": {
                "dump_type": 1,
                "table_name": "DePara_Contabilidade_Oficina",
                "mode": "file_id"
            },
            "DePara - Clientes": {
                "dump_type": 1,
                "table_name": "DePara_Contabilidade_Clientes",
                "mode": "file_id"
            },
            "Venda direta - Nfs pendentes": {
                "dump_type": 1,
                "table_name": "venda_direta_pendentes",
                "mode": "file_id"
            },
            "Cargos - Performance": {
                "dump_type": 1,
                "table_name": "cargos_performance",
                "mode": "file_id"
            }
        }
    },
    "Documentacao.xlsx": {
        "file_id": "c85ba229-3967-4931-9033-81a4ddd08f81",
        "pages": {
            "doc": {
                "dump_type": 1,
                "table_name": "documentacao_controladoria",
                "mode": "file_id"
            }
        }
    }
}