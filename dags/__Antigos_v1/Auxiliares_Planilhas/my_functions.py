from office365.runtime.auth.authentication_context import Authentication<PERSON>ontext
from office365.sharepoint.client_context import ClientContext
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
import pandas as pd
from io import BytesIO
from smbclient import register_session, scandir, open_file
from __Antigos_v1.Auxiliares_Planilhas.variables import *
import json
import chardet
from io import StringIO
import psycopg2
import unicodedata
import re

# import win32netcon
# import win32wnet

def create_index(site_url, username, password):
    # ID da pasta inicial
    folder_id = "d3f768dd-7df1-4997-8b97-417f5c51bfcb"

    # Inicializa o contexto de autenticação
    ctx_auth = AuthenticationContext(url=site_url)
    if ctx_auth.acquire_token_for_user(username, password):
        ctx = ClientContext(site_url, ctx_auth)
        web = ctx.web
        ctx.load(web)
        ctx.execute_query()

        # Obtém a pasta inicial pelo seu ID
        folder = web.get_folder_by_id(folder_id)
        ctx.load(folder)
        ctx.execute_query()

        # Dicionário para armazenar informações dos arquivos Excel
        excel_info = {}

        # Função para listar arquivos Excel recursivamente e obter números das abas
        def list_excel_files_recursive(parent_folder):
            # Obtém todos os itens na pasta
            items = parent_folder.files
            ctx.load(items)
            ctx.execute_query()

            # Itera sobre os itens
            for item in items:
                # Verifica se o item é um arquivo Excel (.xlsx ou .xlsm)
                if item.properties["Name"].endswith(".xlsx") or item.properties["Name"].endswith(".xlsm"):
                    excel_dict = {}
                    excel_dict["file_id"] = item.properties["UniqueId"]

                    # Lê o conteúdo do arquivo Excel
                    file_content = item.read()
                    file_stream = BytesIO(file_content)
                    
                    # Obtém o número de abas e seus índices
                    xl = pd.ExcelFile(file_stream)
                    sheet_dict = {i: {"sheet_name": sheet_name, "dump_type": 0} for i, sheet_name in enumerate(xl.sheet_names)}
                    excel_dict["pages"] = sheet_dict

                    # Adiciona ao dicionário principal
                    excel_info[item.properties["Name"]] = excel_dict

            # Obtém todas as subpastas
            sub_folders = parent_folder.folders
            ctx.load(sub_folders)
            ctx.execute_query()

            # Chama a função recursivamente para cada subpasta
            for sub_folder in sub_folders:
                list_excel_files_recursive(sub_folder)

        # Chama a função para listar arquivos Excel recursivamente na pasta inicial
        list_excel_files_recursive(folder)

        # Converter o dicionário para JSON
        excel_info_json = json.dumps(excel_info, indent=4)
        print(excel_info_json)
        
        return excel_info_json

    else:
        print(ctx_auth.get_last_error())

def PostgreSQL_Insert(username, password, dbname, host, data, table, query = None):
    try:
        conn = psycopg2.connect(dbname=dbname, user=username, password=password, host=host)
        cur = conn.cursor()
    except psycopg2.Error as e:
        print(f"Erro ao conectar ao banco de dados: {e}")
    try:
        if query:
            cur.execute(query)
            conn.commit()

        # Criar um buffer de string para escrever os dados como CSV
        sio = StringIO()
        data.to_csv(sio, index=None, header=None)
        sio.seek(0)
        # Ler os dados do buffer de string e inserir em lote no PostgreSQL
        cur.copy_expert(f"COPY {table} FROM STDIN WITH CSV", sio)
        # Commit das alterações
        conn.commit()

    except Exception as e:
        # Em caso de erro, faça rollback e feche a conexão
        conn.rollback()
        raise e
    
    finally:
        # Feche o cursor e a conexão
        cur.close()
        conn.close()
        
    return data

def remove_prefix(prefix, col_name):
    if prefix and col_name.startswith(prefix):
        return col_name[len(prefix)+1:]
    return col_name

def normalize_column_name(col_name):
    # Remove acentos
    col_name = unicodedata.normalize('NFKD', str(col_name)).encode('ascii', 'ignore').decode('utf-8')
    # Verifica se o nome é um número e adiciona o prefixo 'coluna'
    if col_name.isdigit():
        col_name = f"coluna{col_name}"
    # Substitui espaços e caracteres especiais por _
    col_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
    return col_name

def dataframe_to_create_table_query(df, table_name, date_columns= None, prefix_before_column_name=None, date_type = None, column_types = None):
    df.columns = [normalize_column_name(col) for col in df.columns]
    # Mapeia os tipos de dados do pandas para os tipos de dados do PostgreSQL
    pg_data_types = {
        'int64': 'bigint',
        'float64': 'double precision',
        'object': 'text',
        'datetime64[ns]': 'timestamp',
        'bool': 'boolean'
    }
    if date_columns is None:
        date_columns = []
    # Cria a parte da query para definir as colunas e seus tipos
    columns = []
    for col_name, col_type in df.dtypes.items():
        # Verifica se a coluna está no dicionário column_types
        if column_types and col_name.lower() in column_types:
            pg_type = column_types[col_name.lower()]
        elif any(col.lower() in col_name.lower() for col in date_columns) and date_type == "date":
            pg_type = 'timestamp'
        elif col_type == 'object':
            max_length = df[col_name].astype(str).str.len().max()
            if max_length <= 10:
                pg_type = 'varchar(10)'
            elif max_length <= 25:
                pg_type = 'varchar(25)'
            elif max_length <= 50:
                pg_type = 'varchar(50)'
            elif max_length <= 100:
                pg_type = 'varchar(100)'
            else:
                pg_type = 'text'
        else:
            pg_type = pg_data_types.get(str(col_type), 'text')  # Default para texto se o tipo não for mapeado
        
        if prefix_before_column_name:
            col_name = remove_prefix(prefix_before_column_name.lower(), col_name.lower())
        columns.append(f"{col_name} {pg_type}")
    
    # Junta todas as colunas em uma string separada por vírgulas
    columns_str = ",\n    ".join(columns)
    
    # Monta a query completa para criar a tabela
    create_table_query = f"DROP TABLE IF EXISTS {table_name}; \nCREATE TABLE {table_name} (\n    {columns_str}\n);\n"

    for user in all_grants:
        create_table_query += f"grant all on table {table_name} to {user};\n"

    return create_table_query

def sharepoint_to_df(index, dump_type):
    tabs = []
    for file_name, file_info in index.items():
        file_id = file_info.get("file_id")
        alt_site = file_info.get("alt_site")
        for page_idx, page_data in file_info.get("pages", {}).items():
            try:
                tab_name = int(page_idx) 
                sheet_name = page_data.get("sheet_name")
            except:
                tab_name = page_idx
                sheet_name = page_idx
                
            if dump_type == 0 and page_data["dump_type"] == 0:
                pass
            
            if dump_type == 1 and page_data["dump_type"] == 1:
                table_name = page_data.get("table_name")
                column_names = page_data.get("column_names")
                mode = page_data.get("mode")
                folder_id = page_data.get("folder_id")
                skiprows = page_data.get("skiprows")
                dtype = page_data.get("dtype")
                column_types = page_data.get("column_types")
                tabs.append((file_name, sheet_name, file_id, table_name, column_names, tab_name, mode, folder_id, skiprows, alt_site, dtype, column_types))
            
            if dump_type == 2 and page_data["dump_type"] == 2:
                metas_objetivos_id = page_data.get("metas_objetivos_id")
                system = page_data.get("system")
                cod_empresa = page_data.get("cod_empresa")
                tabs.append((file_name, sheet_name, file_id, metas_objetivos_id, system, cod_empresa, tab_name))
            
            if dump_type == 3 and page_data["dump_type"] == 3:
                table_name = page_data.get("table_name")
                if isinstance(page_idx, str):
                    file_paths = f'{file_info.get("file_path")}{page_idx}'
                else:
                    file_paths = [f'{file_info.get("file_path")}{idx}' for idx in page_idx]
                tab_name = page_data.get("tab_name")
                tabs.append((file_name, sheet_name, file_paths, table_name, tab_name))

            if dump_type == 4 and page_data["dump_type"] == 4:
                table_name = page_data.get("table_name")
                column_names = page_data.get("column_names")
                period = page_data.get("period")
                recreate = page_data.get("recreate")
                tabs.append((file_name, sheet_name, file_id, table_name, column_names, period, recreate, tab_name))
                
    return tabs

def process_sheet(ctx, id, sheet_name, file_name, tab_name, mode="file_id", skiprows = 0, dtype = None):
    try:
        if mode == "file_id":
            file = ctx.web.get_file_by_id(id)
        if mode == "file_name":
            folder = ctx.web.get_folder_by_id(id)
            file = folder.files.get_by_url(file_name)
        ctx.load(file)
        ctx.execute_query()
        file_stream = BytesIO(file.read())

        df = pd.read_excel(file_stream, sheet_name=tab_name, skiprows = skiprows, dtype = dtype)
        num_rows = len(df)
        print(f'Número de registros na planilha: {num_rows}\n ')
        return df, num_rows
    except Exception as e:
        print(f"Erro ao processar planilha: {file_name}, aba: {sheet_name}. Erro: {e}")
        return pd.DataFrame(), 0

def ETL(username, password, site_url, file_tabs, dump_type):
    try:
        ctx_auth = AuthenticationContext(site_url)
        if ctx_auth.acquire_token_for_user(username, password):
            ctx = ClientContext(site_url, ctx_auth)
        else:
            raise Exception("Falha na autenticação")
        
        total_rows_processed = 0
        
        if dump_type == 0:
            pass
        
        elif dump_type == 1:
            for idx, (file_name, sheet_name, file_id, table_name, column_names, tab_name, mode, folder_id, skiprows, alt_site, dtype, column_types) in enumerate(file_tabs, start=1):
                print(f"Planilha: {file_name}")
                print(f"Processando página {idx} de {len(file_tabs)}: {sheet_name}")
                ctx_alt = ""
                try:
                    if alt_site:
                        ctx_auth_alt = AuthenticationContext(alt_site)
                        if ctx_auth_alt.acquire_token_for_user(username, password):
                            ctx_alt = ClientContext(alt_site, ctx_auth_alt) 
                    if mode == "file_id":
                        df, num_rows = process_sheet(ctx if not alt_site else ctx_alt, file_id, sheet_name, file_name, tab_name, mode, skiprows, dtype)
                    elif mode == "file_name":
                        df, num_rows = process_sheet(ctx if not alt_site else ctx_alt, folder_id, sheet_name, file_name, tab_name, mode, skiprows, dtype)             
                    if column_names:
                        df.columns = column_names
                    table_name = f"{SCHEMA_CRIACAO_INSERT}.planilha_{table_name.lower()}"
                    query = dataframe_to_create_table_query(df, table_name, column_types = column_types)
                    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, f"{DW_CORPORATIVO_HOST},{DW_CORPORATIVO_PORT}", df, table_name, query)
                    print(f"Tabela {table_name} criada com sucesso!")
                    total_rows_processed += num_rows
                    # else: 
                    #     print(f"Adcione os nomes das colunas no json! Nomes atuais: {df.columns}")
                except Exception as e:
                    print(f"Erro na planilha {file_name}: {e}")

            print(f'Total de linhas processadas: {total_rows_processed}')
        
        elif dump_type == 2:
            
            df_merged = pd.DataFrame({
                    "metas_objetivos_id": [],
                    "sistema_origem": [],
                    "cod_empresa": [],
                    "periodo": [],
                    "valor": [],
                    "tipo_registro": [],
                    "meta_objetivo_subitem": []
                })
            
            partial_dfs = []
            
            for idx, (file_name, sheet_name, file_id, metas_objetivos_id, sistema_origem, cod_empresa, tab_name) in enumerate(file_tabs, start=1):
                try:
                    print(f"Planilha: {file_name}")
                    print(f"Processando página {idx} de {len(file_tabs)}: {sheet_name}")
                    df, num_rows = process_sheet(ctx, file_id, sheet_name, file_name, tab_name)
                    
                    if metas_objetivos_id:

                        def add_to_partial_dfs(column_name, tipo_registro):
                                    if column_name in df.columns:
                                        partial_dfs.append(pd.DataFrame({
                                            "metas_objetivos_id": metas_objetivos_id,
                                            "sistema_origem": sistema_origem,
                                            "cod_empresa": cod_empresa,
                                            "periodo": df["periodo"],
                                            "valor": df[column_name],
                                            "tipo_registro": tipo_registro,
                                            "meta_objetivo_subitem": df.get("meta_objetivo_subitem", pd.Series([None]*len(df)))
                                        }))

                        # Adicionando dados ao DataFrame parcial
                        add_to_partial_dfs("local", "R")
                        add_to_partial_dfs("realizado", "R")
                        add_to_partial_dfs("realizado_perc", "RP")
                        add_to_partial_dfs("objetivo", "O")
                        add_to_partial_dfs("nacional", "N")
                        add_to_partial_dfs("nacional_perc", "NP")
                        add_to_partial_dfs("captado", "CP")
                        add_to_partial_dfs("local_acumulado_trimestre", "RT")
                        add_to_partial_dfs("nacional_acumulado_trimestre", "NT")
                        add_to_partial_dfs("a_faturar", "AF")
                        add_to_partial_dfs("projetado", "PJ")
                        add_to_partial_dfs("faixa", "FX")
                        add_to_partial_dfs("pontuacao", "PT")
                        add_to_partial_dfs("bonus", "BN")
                        add_to_partial_dfs("montadora", "O")
                        add_to_partial_dfs("objetivo_montadora", "O")
                        add_to_partial_dfs("transito", "T")
                        add_to_partial_dfs("concessionaria", "R")
                        add_to_partial_dfs("aguardando_liberacao", "AL")
                        add_to_partial_dfs("proj_realizado", "PR")
                        add_to_partial_dfs("proj_faixa", "PF")
                        add_to_partial_dfs("proj_pontuacao", "PP")
                        add_to_partial_dfs("pendente", "P")
                        add_to_partial_dfs("cariacica", "C")
                        add_to_partial_dfs("estoque_loja", "R")
                        add_to_partial_dfs("loja", "R")
                        add_to_partial_dfs("loja_acumulado_trimestre", "RT")
                        add_to_partial_dfs("oportunidades", "OP")
                        add_to_partial_dfs("leads", "LD")
                        add_to_partial_dfs("vendas", "VD")
                        add_to_partial_dfs("local_total", "LT")
                        add_to_partial_dfs("nacional_total", "NT")
                        add_to_partial_dfs("local_comparativo", "LC")
                        add_to_partial_dfs("nacional_comparativo", "NC")
                        add_to_partial_dfs("formato", "F")
                        add_to_partial_dfs("comissao", "CO")
                        add_to_partial_dfs("objetivo_lojas", "O")
                        add_to_partial_dfs("objetivo_frotistas", "OF")
                        
                    else:
                        partial_dfs.append(df)
        
                except Exception as e:
                    # Log de erro para a planilha atual
                    print(f"Erro ao processar planilha: {file_name}, aba: {sheet_name}. Erro: {e}")
                    continue

            df_merged = pd.concat(partial_dfs, ignore_index=True) if partial_dfs else pd.DataFrame()

            # Fazendo o tratamento dos dados
            df_merged["periodo"] = pd.to_numeric(df_merged["periodo"], errors="coerce")
            df_merged = df_merged[df_merged["periodo"].notnull()]
            df_merged = df_merged[(df_merged["periodo"] % 100 >= 1) & (df_merged["periodo"] % 100 <= 12)]
            df_merged["periodo"] = df_merged["periodo"].astype(int)
            df_merged["valor"] = pd.to_numeric(df_merged["valor"], errors='coerce')
            # df_merged = df_merged[df_merged["valor"].notnull()]     Recolocar caso a tabela fique muito grande. Tiramos pra um objetivo especifico em jan/25 

            table_name = f'{SCHEMA_CRIACAO_INSERT}.planilha_metas_objetivos_valores'
            query = F'TRUNCATE {table_name};'
            # Inserindo no postgres
            PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, f"{DW_CORPORATIVO_HOST},{DW_CORPORATIVO_PORT}", df_merged, table_name, query)
            print(f"Tabela {table_name} populada com sucesso!")
            print(f'Total de linhas do dataframe final: {len(df_merged)}')       
        
        elif dump_type == 3:

            # Conectar à rede
            register_session("nova.mmbq.local", username=WINDOWS_USERNAME, password=WINDOWS_PASSWORD)

            for idx, (file_name, sheet_name, file_paths, table_name, tab_name) in enumerate(file_tabs, start=1):
                try:
                    print(f"Local: {file_name}")
                    print(f"Processando etapa {idx} de {len(file_tabs)}: {file_paths}")
                    
                    # DataFrame para acumular todos os dados
                    df_combined = pd.DataFrame()

                    if isinstance(file_paths, str):
                        file_paths = [file_paths, ]

                    # Verificar se a conexão foi estabelecida corretamente
                    for file_path in file_paths:
                        for entry in scandir(file_path):
                            print(f"Entry: {entry.name}")
                            fp = f"{file_path}\{entry.name}"
                            if entry.is_file() and (entry.name.endswith('.csv') or entry.name.endswith('.dat')):
                                try:
                                    # Abrir e ler o arquivo
                                    with open_file(fp, mode='r', encoding='utf-8') as file_obj:
                                        df = pd.read_csv(file_obj, delimiter=';', dtype={'Filial': str})
                                        print(file_path)
                                        print(df)
                                        df_combined = pd.concat([df_combined, df], ignore_index=True)
                                        print(f"Arquivo '{entry.name}' lido com sucesso.")
                                except pd.errors.ParserError as e:
                                    print(f"Erro ao ler o arquivo '{entry.name}': {e}")
                            elif entry.is_file() and entry.name.endswith('DeParaMEF.xlsx'):
                                try:
                                    with open_file(fp, mode='rb') as file_obj:
                                        df = pd.read_excel(file_obj, sheet_name=tab_name)
                                        df_combined = pd.concat([df_combined, df], ignore_index=True)
                                        print(f"Arquivo '{entry.name}' lido com sucesso.")
                                except pd.errors.ParserError as e:
                                    print(f"Erro ao ler o arquivo '{entry.name}': {e}")
                            elif entry.is_file() and entry.name.endswith('Drill Forecast 5+7 2024 - Receita Bruta de Vendas.xlsx'):
                                try:
                                    with open_file(fp, mode='rb') as file_obj:
                                        df = pd.read_excel(file_obj, dtype={'Unnamed: 3': str})
                                        df.columns = ["Years", "Period", "Scenario", "Entity", "Filial", "Centro_Custo", "Account", "Produto_Canal", "amount"]
                                        df_combined = pd.concat([df_combined, df], ignore_index=True)
                                        print(f"Arquivo '{entry.name}' lido com sucesso.")
                                except pd.errors.ParserError as e:
                                    print(f"Erro ao ler o arquivo '{entry.name}': {e}")

                    if not df_combined.empty:
                        if 'Amount' in df_combined.columns:
                            # Converter a coluna 'Amount' para string
                            df_combined['Amount'] = df_combined['Amount'].astype(str)

                            # Aplicar operações de string e converter para float
                            df_combined['Amount'] = (
                                df_combined['Amount']
                                .str.strip()  # Remover espaços em branco
                                .str.replace('.', '', regex=False)  # Remover pontos
                                .str.replace(',', '.', regex=False)  # Substituir vírgulas por pontos
                                .replace('-', '0', regex=False)  # Substituir sinais de menos por zero
                                .astype(float)  # Converter para float
                            )

                        if 'Filial' in df_combined.columns:
                                df_combined['Filial'] = df_combined['Filial'].astype(str).replace("2901290000412", "02901290000412").replace("2901290000170","02901290000170")

                        df_combined.columns = df_combined.columns.str.replace(' ', '_').str.replace('.', '_').str.replace('-', '_').str.replace('Í', 'i').str.replace(':', '_')
                        df_combined = df_combined[df_combined['Years'].notnull()]

                        table_name = f"{SCHEMA_CRIACAO_INSERT}.planilha_{table_name.lower()}"
                        query = dataframe_to_create_table_query(df_combined, table_name)
                        PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, f"{DW_CORPORATIVO_HOST},{DW_CORPORATIVO_PORT}", df_combined, table_name, query)
                        print(f"Tabela {table_name} criada com sucesso!")

                except Exception as e:
                    print(f"Erro ao acessar a pasta de rede: {e}")
                
        elif dump_type == 4:
            for idx, (file_name, sheet_name, file_id, table_name, column_names, period, recreate, tab_name) in enumerate(file_tabs, start=1):
                print(f"Planilha: {file_name}")
                print(f"Processando página {idx} de {len(file_tabs)}: {sheet_name}")
                df, num_rows = process_sheet(ctx, file_id, sheet_name, file_name, tab_name)
                if column_names:
                    df.columns = column_names
                    table_name = f"{SCHEMA_CRIACAO_INSERT}.planilha_{table_name.lower()}"
                    df["ref"] = period
                    query = None
                    if recreate:
                        query = dataframe_to_create_table_query(df, table_name)
                    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, f"{DW_CORPORATIVO_HOST},{DW_CORPORATIVO_PORT}", df, table_name, query)
                    print(f"Tabela {table_name} populada com os dados de: {period}")
                    total_rows_processed += num_rows
                else: 
                    print(f"Adcione os nomes das colunas no json! Nomes atuais: {df.columns}")
                    
            print(f'Total de linhas processadas: {total_rows_processed}')     
        else:
            raise Exception("Tipo de despejo inválido")
            
    except Exception as e:
        print("Erro:", e)

def ETL_Tasks(index, username, password, site_url, dump_type):
    result = sharepoint_to_df(index, dump_type)
    resultado = ETL(username = username, password= password, site_url = site_url, file_tabs = result, dump_type=dump_type)

def create_tasks(dag, username, password, site_url, dump_types):
    start_silver_task = EmptyOperator(task_id=f'start_load_planilhas', dag=dag)
    end_silver_task = EmptyOperator(task_id=f'end_load_planilhas', dag=dag)
    previous_task = start_silver_task  # Tarefa anterior à primeira tarefa do loop

    for dump_type, description in dump_types.items():
        task = PythonOperator(
            task_id=f'Processamento_Estagio_{description}',
            python_callable=ETL_Tasks,
            op_args=[index, username, password, site_url, dump_type],
            trigger_rule='all_done',
            dag=dag
        )
        previous_task >> task
        previous_task = task

    task >> end_silver_task

    return start_silver_task, end_silver_task