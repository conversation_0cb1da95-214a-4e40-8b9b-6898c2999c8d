# Este script implementa um DAG de Airflow para realizar ETLs de vÃ¡rias fontes de dados, incluindo MSSQL, Oracle e planilhas,
# e insere os resultados em uma instÃ¢ncia PostgreSQL. Ele cobre a criaÃ§Ã£o de tabelas e o processamento de dados para vendas,
# pÃ³s-vendas, seguros, entre outros.
from __Antigos_v1.Auxiliares_Autom.variables import (
    DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST,
    DEALER_USERNAME, DEALER_PASSWORD, DEALER_DBNAME, DEALER_HOST, DEALER_PORT,
    NBS_USERNAME, NBS_PASSWORD, NBS_SERVICE_NAME, NBS_HOST, NBS_PORT, 
    QUIVER_USERNAME, QUIVER_PASSWORD, QUIVER_DBNAME, QUIVER_HOST, QUIVER_PORT,
    DW_BAMAQ_USERNAME, DW_BAMAQ_PASSWORD, DW_BAMAQ_DBNAME, DW_BAMAQ_HOST, DW_BAMAQ_PORT,
    SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD, SITE_URL, bp_file_id, mpbh_file_id,
    mvjf_file_id, mvbh_file_id, pvba_file_id, pvbh_file_id, ppbh_file_id,
    mpjf_file_id, DW_CORPORATIVO_PORT, ck_pv_file_id, fe_pv_file_id, feriados_file_id,
    vendedores_file_id, vendedores_sheet_name, SCHEMA_CRIACAO_INSERT, consorcio_file_name,
    fe_mvbh_file_id, fe_mvjf_file_id
)
import pytz
import pandas as pd
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator

# Importando funÃ§Ãµes personalizadas e variÃ¡veis
from __Antigos_v1.Auxiliares_Autom.my_functions_2 import (
    ETL_FILA_ESPERA, PostgreSQL_Insert, generate_etl_data,
    ETL_VENDAS_GWM, ETL, merge_and_clean_data, PostgreSQL_Create,
    get_data_from_db, get_sharepoint_excel_df, ETL_BAMAQ_PREMIUM
)
from __Antigos_v1.Auxiliares_Autom.querys import (
    query_seguros, query_consorcio, query_criacao_tabelas, query_dealer_vendas,
    query_dealer_estoque, query_nbs_posvendas, query_nbs_vendas, query_metricas,
    query_nbs_estoque
)

index = generate_etl_data()

def Criacao_Tabelas():
    PostgreSQL_Create(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, query_criacao_tabelas)

def Processamento_Oracle_Nbs_Vendas():
    data = get_data_from_db("Oracle", NBS_USERNAME, NBS_PASSWORD, NBS_SERVICE_NAME, NBS_HOST,  NBS_PORT, query_nbs_vendas)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_vendas')
    return len(data)

def Processamento_Oracle_Nbs_Posvendas():
    data = get_data_from_db("Oracle", NBS_USERNAME, NBS_PASSWORD, NBS_SERVICE_NAME, NBS_HOST,  NBS_PORT,  query_nbs_posvendas)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_posvendas')
    return len(data)

def Processamento_Consolidado_Estoque():
    df = get_data_from_db("Oracle", NBS_USERNAME, NBS_PASSWORD, NBS_SERVICE_NAME, NBS_HOST,  NBS_PORT, query_nbs_estoque)
    df1 = get_data_from_db("Mssql", DEALER_USERNAME, DEALER_PASSWORD, DEALER_DBNAME, DEALER_HOST, DEALER_PORT, query_dealer_estoque)
    # Convertendo todos os nomes das colunas para maiÃºsculas
    df.columns = df.columns.str.upper()
    df1.columns = df1.columns.str.upper()

    # Inicializa um dataframe vazio para armazenar o resultado da concatenaÃ§Ã£o
    data = pd.concat([df, df1], ignore_index=True)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_Posicao_Estoque')
    return len(data)

def Processamento_Mssql_Dealer_Vendas():
    df1 = get_data_from_db("Mssql", DEALER_USERNAME, DEALER_PASSWORD, DEALER_DBNAME, DEALER_HOST, DEALER_PORT, query_dealer_vendas)
    df2 = get_sharepoint_excel_df(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id = vendedores_file_id,
        sheet_name=vendedores_sheet_name
    )
    merged_data = merge_and_clean_data(df1, df2)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, merged_data, f'{SCHEMA_CRIACAO_INSERT}.f_vendas')
    return len(df1)

def Processamento_Mssql_Dealer_Estoque():
    data = get_data_from_db("Mssql", DEALER_USERNAME, DEALER_PASSWORD, DEALER_DBNAME, DEALER_HOST, DEALER_PORT, query_dealer_estoque)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_vendas')
    return len(data)

def Processamento_Mssql_Consorcio():
    data = get_data_from_db("Mssql", DW_BAMAQ_USERNAME, DW_BAMAQ_PASSWORD, DW_BAMAQ_DBNAME, DW_BAMAQ_HOST, DW_BAMAQ_PORT, query_consorcio)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_vendas')
    return len(data)

def Processamento_Mssql_Seguros():
    data = get_data_from_db("Mssql", QUIVER_USERNAME, QUIVER_PASSWORD, QUIVER_DBNAME, QUIVER_HOST, QUIVER_PORT, query_seguros)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_Seguros')
    return len(data)

def Processamento_Planilhas_Feriados():
    data = get_sharepoint_excel_df(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id = feriados_file_id
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.dim_feriados')
    return len(data)

def Processamento_Planilhas_GWM_MS_Vendas():
    data = ETL_VENDAS_GWM(602)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_vendas')
    return len(data)

def Processamento_Planilhas_GWM_MG_Vendas():
    data = ETL_VENDAS_GWM(601)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.f_vendas')
    return len(data)

def Processamento_Planilhas_PorscheBH_Vendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=pvbh_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_PorscheBA_Vendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=pvba_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_PorscheBH_Posvendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=ppbh_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_MercedesBH_Vendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=mvbh_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_MercedesJF_Vendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=mvjf_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_MercedesBH_Posvendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=mpbh_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_MercedesJF_Posvendas():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=mpjf_file_id,
        index=index
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_Bamaq_Premium():
    data = ETL(
        username=SHAREPOINT_USERNAME, 
        password=SHAREPOINT_PASSWORD, 
        site_url=SITE_URL,
        file_id=bp_file_id,
        index=index
    )

    data = ETL_BAMAQ_PREMIUM(data)

    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

def Processamento_Planilhas_Cockpit():
    data = get_sharepoint_excel_df(
        username = SHAREPOINT_USERNAME, 
        password = SHAREPOINT_PASSWORD, 
        site_url = SITE_URL,
        file_id = ck_pv_file_id
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_cockpit_porsche')
    return len(data)

def Processamento_Planilhas_Porsche_Fila_Espera():
    data = get_sharepoint_excel_df(
        username = SHAREPOINT_USERNAME, 
        password = SHAREPOINT_PASSWORD, 
        site_url = SITE_URL,
        file_id = fe_pv_file_id
    )
    data = ETL_FILA_ESPERA(data, 401)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_fila_espera')
    return len(data)

def Processamento_Planilhas_Mercedes_Fila_Espera():
    data_bh = get_sharepoint_excel_df(
        username = SHAREPOINT_USERNAME, 
        password = SHAREPOINT_PASSWORD, 
        site_url = SITE_URL,
        file_id = fe_mvbh_file_id
    )
    data_bh = ETL_FILA_ESPERA(data_bh, 101)
    data_jf = get_sharepoint_excel_df(
        username = SHAREPOINT_USERNAME, 
        password = SHAREPOINT_PASSWORD, 
        site_url = SITE_URL,
        file_id = fe_mvjf_file_id
    )
    data_jf = ETL_FILA_ESPERA(data_jf, 103)
    data = pd.concat([data_bh, data_jf], ignore_index=True)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_fila_espera')
    return len(data)

def Processamento_Postgres_Metricas():
    data = get_data_from_db(db_type="Postgres", 
                            username=DW_CORPORATIVO_USERNAME,
                            password=DW_CORPORATIVO_PASSWORD, 
                            dbname=DW_CORPORATIVO_DBNAME, 
                            host=DW_CORPORATIVO_HOST, 
                            port=DW_CORPORATIVO_PORT, 
                            query=query_metricas)
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos')
    return len(data)

def Processamento_Planilhas_Consorcio():
    data = get_sharepoint_excel_df(
        username = SHAREPOINT_USERNAME, 
        password = SHAREPOINT_PASSWORD, 
        site_url = SITE_URL,
        file_id = consorcio_file_name
    )
    PostgreSQL_Insert(DW_CORPORATIVO_USERNAME, DW_CORPORATIVO_PASSWORD, DW_CORPORATIVO_DBNAME, DW_CORPORATIVO_HOST, data, f'{SCHEMA_CRIACAO_INSERT}.bqtb_metas_objetivos_valores')
    return len(data)

# Definição do DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=3)
}

# Configurando o fuso horÃƒÂ¡rio para SÃƒÂ£o Paulo
sp_timezone = pytz.timezone('America/Sao_Paulo')

# Definindo a data e hora desejada em SÃƒÂ£o Paulo
start_date = sp_timezone.localize(datetime(2024, 3, 6, 7, 45))

# with DAG(
#     'ETL_BAMAQ_GOLD_BI_AUTOMOTIVO',
#     default_args=default_args,
#     description='DAG para ETL de dados da BU Automotiva',
#     schedule_interval='15 1,12 * * 1-6',
#     start_date=start_date,
#     catchup=False,
# ) as dag:

#     tarefa0 = PythonOperator(task_id='Criacao_Tabelas', python_callable=Criacao_Tabelas, trigger_rule='all_done')

#     tarefa1 = PythonOperator(task_id='Dados_Oracle_Nbs_Vendas',python_callable=Processamento_Oracle_Nbs_Vendas, trigger_rule='all_done')

#     tarefa2 = PythonOperator(task_id='Dados_Oracle_Nbs_Posvendas', python_callable=Processamento_Oracle_Nbs_Posvendas, trigger_rule='all_done')

#     tarefa3 = PythonOperator(task_id='Dados_Mssql_Dealer_Vendas', python_callable=Processamento_Mssql_Dealer_Vendas, trigger_rule='all_done')

#     tarefa4 = PythonOperator(task_id='Dados_Mssql_Dealer_Estoque', python_callable=Processamento_Mssql_Dealer_Estoque, trigger_rule='all_done')

#     tarefa5 = PythonOperator(task_id='Dados_Mssql_Consorcio', python_callable=Processamento_Mssql_Consorcio, trigger_rule='all_done')

#     tarefa6 = PythonOperator(task_id='Dados_Mssql_Seguros', python_callable=Processamento_Mssql_Seguros, trigger_rule='all_done')

#     # tarefa7 = PythonOperator(task_id='Dados_Planilhas_GWM_MG_Vendas', python_callable=Processamento_Planilhas_GWM_MG_Vendas, trigger_rule='all_done')
    
#     # tarefa8 = PythonOperator(task_id='Dados_Planilhas_GWM_MS_Vendas', python_callable=Processamento_Planilhas_GWM_MS_Vendas, trigger_rule='all_done')
    
#     tarefa9 = PythonOperator(task_id='Dados_Planilhas_Feriados', python_callable=Processamento_Planilhas_Feriados, trigger_rule='all_done')

#     tarefa10 = PythonOperator(task_id='Dados_Planilhas_Cockpit', python_callable=Processamento_Planilhas_Cockpit, trigger_rule='all_done')

#     tarefa11 = PythonOperator(task_id='Dados_Planilhas_Porsche_Fila_Espera', python_callable=Processamento_Planilhas_Porsche_Fila_Espera, trigger_rule='all_done')
    
#     tarefa12 = PythonOperator(task_id='Dados_Planilhas_PorscheBH_Vendas', python_callable=Processamento_Planilhas_PorscheBH_Vendas, trigger_rule='all_done')
    
#     tarefa13 = PythonOperator(task_id='Dados_Planilhas_PorscheBA_Vendas', python_callable=Processamento_Planilhas_PorscheBA_Vendas, trigger_rule='all_done')
    
#     tarefa14 = PythonOperator(task_id='Dados_Planilhas_PorscheBH_Posvendas', python_callable=Processamento_Planilhas_PorscheBH_Posvendas, trigger_rule='all_done')

#     tarefa15 = PythonOperator(task_id='Dados_Planilhas_MercedesBH_Vendas', python_callable=Processamento_Planilhas_MercedesBH_Vendas, trigger_rule='all_done')

#     tarefa16 = PythonOperator(task_id='Dados_Planilhas_MercedesJF_Vendas', python_callable=Processamento_Planilhas_MercedesJF_Vendas, trigger_rule='all_done')

#     tarefa17 = PythonOperator(task_id='Dados_Planilhas_MercedesBH_Posvendas', python_callable=Processamento_Planilhas_MercedesBH_Posvendas, trigger_rule='all_done')

#     tarefa18 = PythonOperator(task_id='Dados_Planilhas_MercedesJF_Posvendas', python_callable=Processamento_Planilhas_MercedesJF_Posvendas, trigger_rule='all_done')

#     tarefa19 = PythonOperator(task_id='Dados_Planilhas_Bamaq_Premium', python_callable=Processamento_Planilhas_Bamaq_Premium, trigger_rule='all_done')

#     tarefa20 = PythonOperator(task_id='Dados_Postgres_Metricas', python_callable=Processamento_Postgres_Metricas, trigger_rule='all_done')

#     tarefa21 = PythonOperator(task_id='Dados_Planilhas_Consorcio', python_callable=Processamento_Planilhas_Consorcio, trigger_rule='all_done')

#     tarefa22 = PythonOperator(task_id='Dados_Consolidado_Estoque', python_callable=Processamento_Consolidado_Estoque, trigger_rule='all_done')
    
#     tarefa23 = PythonOperator(task_id='Dados_Planilhas_Mercedes_Fila_Espera', python_callable=Processamento_Planilhas_Mercedes_Fila_Espera, trigger_rule='all_done')

#     # Estrutura de dependÃªncia das tarefas
#     tarefa0 >> tarefa1 >> tarefa2 >> tarefa3 >> tarefa4 >> tarefa5 >> tarefa6 >> tarefa9 >> tarefa10 >> tarefa11 >> tarefa12 >> tarefa13 >> tarefa14 >> tarefa15 >> tarefa16 >> tarefa17 >> tarefa18 >> tarefa19 >> tarefa20 >> tarefa21 >> tarefa22 >> tarefa23
