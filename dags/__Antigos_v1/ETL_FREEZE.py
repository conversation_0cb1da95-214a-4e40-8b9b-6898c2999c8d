from airflow import DAG
from airflow.utils.dates import days_ago
from datetime import timedelta
from __Antigos_v1.Auxiliares_ETL.functions import *

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    # 'email': ['<EMAIL>'], 
    # 'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1)
}

# with DAG(
#     'ETL_BAMAQ_FREEZE',
#     default_args=default_args,
#     description='DAG para ETL de congelamento de dados da BAMAQ',
#     schedule_interval=None,
#     max_active_tasks=2, 
#     start_date=days_ago(1),
#     catchup=False
# ) as dag:

#     start_ext, end_ext = create_ext_tasks(dag, SCHEMA_CRIACAO_INSERT, stg_freeze_tables)

#     # Task que verifica falhas e cria o chamado
#     check_falhas_task = PythonOperator(
#         task_id='check_failed_tasks',
#         python_callable=check_failed_tasks,
#         provide_context=True,
#         op_kwargs={'schema': SCHEMA_CRIACAO_INSERT},
#         dag=dag,
#     )

#     start_ext >> end_ext >> check_falhas_task