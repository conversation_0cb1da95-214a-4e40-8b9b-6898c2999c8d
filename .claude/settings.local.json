{"permissions": {"allow": ["<PERSON><PERSON>(docker compose:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "Bash(sudo chown:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)", "Bash(python -m pytest tests/unit/test_base_etl.py::TestBaseETLProcessor::test_initialization -v)", "Bash(python3 -m pytest tests/unit/test_base_etl.py::TestBaseETLProcessor::test_initialization -v)", "Bash(telnet:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker-compose logs:*)"], "deny": []}}